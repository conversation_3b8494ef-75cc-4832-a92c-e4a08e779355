
#Step 1. Build the Docker Container
FROM node:18-alpine AS build
WORKDIR /app

#Step 2. Copy Package.json and other dependencies
COPY package*.json ./

#Step 3. Run NPM Install/Build 
RUN npm install
COPY . .
RUN npm run build:qa

RUN mkdir -p ./build/client ./build/portals
RUN cp -rf dist/* build/portals/
RUN cp -rf ./src build/client/src
RUN cp -f ./package*.json build/client/
RUN cp -f ./.env.qa build/client/.env
RUN cp -f ./postcss.config.js build/client/postcss.config.js
RUN cp -f ./tsconfig.json build/client/tsconfig.json 
RUN sed -i 's|src="/assets/|src="assets/|g; s|href="/assets/|href="assets/|g' build/portals/index.html

RUN apk add --no-cache python3 py3-pip zip && ln -sf python3 /usr/bin/python
RUN cd build/ && zip -r build.zip ./client ./portals
RUN mv build/build.zip .
RUN pip install -r requirements.txt --break-system-packages && python main.py





