# Stage 1: Build the React application
FROM node:18-alpine AS build
# Set the working directory
WORKDIR /app

# Copy package.json and package-lock.json (or yarn.lock) to the working directory
COPY package*.json ./

# Install dependencies
RUN npm install

# Copy the rest of the application code to the working directory
COPY . .

# Set environment variable (this can be overridden during the build process)
ARG NODE_ENV=production


# Update the timestamp in the .env.qa file
# Determine the appropriate .env file based on NODE_ENV
RUN if [ "$NODE_ENV" = "production" ]; then \
      ENV_FILE=".env.production"; \
    elif [ "$NODE_ENV" = "qa" ]; then \
      ENV_FILE=".env.qa"; \
    else \
      ENV_FILE=".env.local"; \
    fi && \
    echo "Using environment file: $ENV_FILE" && \
    CURRENT_TIMESTAMP=$(date +"%Y-%m-%dT%H:%M:%S %Z") && \
    sed -i "s/^VITE_APP_BUILDTIMESTAMP=.*/VITE_APP_BUILDTIMESTAMP=$CURRENT_TIMESTAMP/" $ENV_FILE



# Run different build commands based on the environment variable
RUN if [ "$NODE_ENV" = "production" ]; then \
      npm run build:prod; \
    elif [ "$NODE_ENV" = "qa" ]; then \
      npm run build:qa; \
    else \
      npm run build:local; \
    fi


# Stage 2: Serve the React application with Nginx
FROM 958991296092.dkr.ecr.us-east-2.amazonaws.com/nginx-1.23.3-alpine:latest

# Copy the build output to the Nginx HTML directory
COPY --from=build /app/dist /usr/share/nginx/html

# Copy custom Nginx configuration file if you have one
COPY nginx.conf /etc/nginx/nginx.conf

# Expose port 80
EXPOSE 80

# Start Nginx server
CMD ["nginx", "-g", "daemon off;"]