# DevOps for CDERGPT PREPROD INTERNAL App


# Basic Description of the App
CDERGPT is a RAG chatbot application created using React.js/Typescript and Python. 
Users can ask a question and receive a response back from the interface. 
Users are able to upload .pdf or .docx documents and ask questions about the document(s) they've uploaded.
Each chat is a new session and sessions are saved, so users have the ability to go back to a previous chat session and continue their chat.
Depending on the users permission level, the user is able to upload a large number of documents to document libraries and ask or obtain information from those sets of documents. 
A key benefit is its ability to rapidly analyze multiple documents and provide swift answers, saving users time from having to manually check through each document. 

Features:
-Natural language interaction
-PDF and DOCX file analysis
-Session-based conversations
-Accessible chat history
-Scalable document libraries
-Permission-based user access

To start this application locally, run the following commands: 
1) npm i
2) npm run dev

## DevOps Pipeline Setup

    - Ideal Structure
        - One GitLab Repo for every App
        - One Jenkins Pipeline for every App
        - Minimum of three deployment branches per repo. One for each environment (PreProdDev/PreProd/Prod)
        - Every deployment branch has the following files/packages to aid in deployment. We will use PreProd-CFGAI branch
          as the example
            - Dockerfile
            - go.sh
            - main.py
            - requirements.txt
            - PEM file for URL (Ex. cderonegpt-dev.preprod.fda.gov.pem)
            - Created via NPM and have client/portals directory

     - Triggers to Build App and Deploy to CDERGPT (Question is which branches do we want this for?)
        - Push events 
        - Merge Requests

    - Next Steps
        - Update README.md for branch preprod-cfgai (Basic Description)
        - Cleanup branch preprod-cfgai. Move the files to an archive folder. Discuss with Sam.
        - Parametrize all variables (Keys/URLs/Ids) and use GitLab secrets
        - Repeat Structure for future app deployments and keep it consistent.

## Command to Perform Local Build
    npm run dev

## Command to Test Locally
    test

# Important Links
App Link - https://cderonegpt-dev.preprod.fda.gov/SemossWeb/packages/client/dist/#/app/102effb9-b4f9-4512-9f35-eb53a77ee465
Jenkins Link - https://jenkins.fda.gov/job/FDA/job/CDER/job/CDEROne/job/CFG-AI/job/cderone_cdergpt_cfg_ai/

