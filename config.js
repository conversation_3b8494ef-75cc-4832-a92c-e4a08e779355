export const API_URL = import.meta.env.VITE_APP_API_URL;
export const VITE_PROJECT_ID=import.meta.env.VITE_PROJECT_ID;
export const CFG_API_URL = import.meta.env.VITE_APP_CFG_AI_MONOLITH
export const CFG_AI_SECRET_KEY = import.meta.env.VITE_APP_CFG_AI_SECRET_KEY;
export const CFG_AI_ACCESS_KEY = import.meta.env.VITE_APP_CFG_AI_ACCESS_KEY;
export const CFG_AI_SERVER_URL = import.meta.env.VITE_APP_CFG_AI_SERVER_URL;
export const SERVICE_NAME = import.meta.env.VITE_APP_SERVICE_NAME;
export const BEDROCKENABLED = import.meta.env.VITE_APP_BEDROCKENABLED === 'true';
export const CFGAIENABLED = import.meta.env.VITE_APP_CFGAIENABLED === 'true';
export const CFGAIWPENABLED = import.meta.env.VITE_APP_CFGAIWPENABLED === 'true';
export const BEDROCKWPENABLED = import.meta.env.VITE_APP_BEDROCKWPENABLED === 'true';

export const BUILDTIMESTAMP = import.meta.env.VITE_APP_BUILDTIMESTAMP;
