docker build --platform=linux/amd64 -t 958991296092.dkr.ecr.us-east-2.amazonaws.com/irpolicybot/ui:latest .

aws ecr get-login-password --region us-east-2 | docker login --username AWS --password-stdin 958991296092.dkr.ecr.us-east-2.amazonaws.com
docker push 958991296092.dkr.ecr.us-east-2.amazonaws.com/irpolicybot/ui:latest

aws ecs update-service --cluster irpolicybot-demo-ecs-cluster --service irpolicybot-demo-ui-service --region us-east-2 --force-new-deployment > /dev/null

