# Define the image name
# Define arrays for Docker image names and ECR image names
DOCKER_IMAGE_NAMES=("nginx:1.23.3-alpine" "node:18-alpine" "python:3.11-slim")
ECR_IMAGE_NAMES=("nginx-1.23.3-alpine" "node-18-alpine" "python-3.11-slim")

# Define the platform
PLATFORM="linux/amd64"

# Authenticate Docker to ECR
aws ecr get-login-password --region us-east-2 | docker login --username AWS --password-stdin 958991296092.dkr.ecr.us-east-2.amazonaws.com

# Loop through the arrays
for i in "${!DOCKER_IMAGE_NAMES[@]}"; do
  DOCKER_IMAGE_NAME=${DOCKER_IMAGE_NAMES[$i]}
  ECR_IMAGE_NAME=${ECR_IMAGE_NAMES[$i]}

  # Pull the Docker image from Docker Hub with the specified platform
  docker pull --platform $PLATFORM $DOCKER_IMAGE_NAME

  # Tag the Docker image for ECR
  docker tag $DOCKER_IMAGE_NAME 958991296092.dkr.ecr.us-east-2.amazonaws.com/$ECR_IMAGE_NAME:latest

  # Push the Docker image to ECR
  docker push 958991296092.dkr.ecr.us-east-2.amazonaws.com/$ECR_IMAGE_NAME:latest

done