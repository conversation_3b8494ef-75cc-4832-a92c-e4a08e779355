import requests
import base64
from urllib.parse import urlencode
import os
 
# Constants
username = "ea901f9e-6300-43ee-b2b8-bc36c4ffd72c" #Access Key that is created in CDERGPT
password = "6021b688-1de6-4dd6-b655-195e36b944c3" #Secret Key that is created in CDERGPT 
PROJECT_ID = "102effb9-b4f9-4512-9f35-eb53a77ee465" #Project ID of App that is Created in CDERGPT
BASE_URL = "https://cderonegpt-dev.preprod.fda.gov/Monolith/api"
certificate = "cderonegpt-dev.preprod.fda.gov.pem"

#Zip File that is uploaded to CDERGPT. Input zip file was created via go.sh file
ZIP_FILE_NAME = "build.zip"
ZIP_FILE_PATH = "./build.zip" 



# Helper Functions
def create_auth_headers(username, password):
    credentials = f"{username}:{password}"
    credentials_bytes = credentials.encode("utf-8")
    base64_credentials = base64.b64encode(credentials_bytes)
    return {
        "Authorization": f"Basic {base64_credentials.decode('utf-8')}",
        "Content-Type": "application/x-www-form-urlencoded",
    }
#Have to send request with certificate of URL. Will be needed in every deployment. 
def send_request(method, url, headers=None, data=None, files=None, cookies=None):
    response = requests.request(
        method, url, headers=headers, data=data, files=files, cookies=cookies, verify=certificate
    )
    print(f"Response {response.text}")
    #if response.status_code == 200:
    try:
        return response.json(), response.cookies
    except ValueError:
        return ({
            "status": response.status_code,
            "message": "Non-JSON response received",
            "data": response.text,
        },"tempvalue")


# API Operations
def execute_expression(expression, insight_id, cookies):
    url = f"{BASE_URL}/engine/runPixel"
    encoded_expression = urlencode({"expression": expression})
    payload = f"{encoded_expression}&insightId={insight_id}"
    return send_request(
        "POST",
        url,
        headers={**standard_headers, **auth_headers},
        data=payload,
        cookies=cookies,
    )
def upload_file(file_path, insight_id, cookies):
    url = f"{BASE_URL}/uploadFile/baseUpload?insightId={insight_id}&projectId={PROJECT_ID}&path=version/assets/"
    files = [("file", (f"{ZIP_FILE_NAME}", open(file_path, "rb"), f"{ZIP_FILE_NAME}"))]
    return send_request(
        "POST",
        url,
        headers={**standard_headers},
        files=files,
        cookies=cookies,
    )
def set_project_portal(cookies):
    url = f"{BASE_URL}/auth/project/setProjectPortal"
    payload = f"projectId={PROJECT_ID}&hasPortal=true&projectId=public"
    return send_request(
        "POST",
        url,
        headers={**standard_headers, **auth_headers},
        data=payload,
        cookies=cookies,
    )
def publish_project(insight_id, cookies):
    expression = f"PublishProject('{PROJECT_ID}', release=True);"
    return execute_expression(expression, insight_id, cookies)

# Main Execution Flow
auth_headers = create_auth_headers(username, password)
standard_headers = {
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "accept": "application/json, text/plain, */*",
}

# Perform API calls
initial_response, initial_cookies = execute_expression("2+2", "new", None)
insight_id = initial_response.get("insightID")
delete_response = execute_expression(
    f"DeleteAsset(filePath=['version/assets/'], space=['{PROJECT_ID}'])",
    insight_id,
    initial_cookies,
)
upload_response = upload_file(ZIP_FILE_PATH, insight_id, initial_cookies)
unzip_response = execute_expression(
    f"UnzipFile(filePath=['version/assets/{ZIP_FILE_NAME}'], space=['{PROJECT_ID}'])",
    insight_id,
    initial_cookies,
)
reload_response = execute_expression(
    f"ReloadInsightClasses('{PROJECT_ID}');", insight_id, initial_cookies
)
portal_response = set_project_portal(initial_cookies)
publish_response = publish_project(insight_id, initial_cookies)