worker_processes  auto;
error_log  /var/log/nginx/error.log notice;
pid        /var/run/nginx.pid;

events {
    worker_connections  1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    sendfile        on;
    keepalive_timeout  65;

     proxy_read_timeout 600;
     proxy_connect_timeout 600;
     proxy_send_timeout 600;

    server {
        listen 80;
        listen  [::]:80;
        server_name  $host;

        location /assistant {
        alias /usr/share/nginx/html;  # Use alias instead of root
        index index.html;

        # Try to serve the file directly, fallback to index.html
        try_files $uri $uri/ /assistant/index.html;
    }

    

       
    }
}
