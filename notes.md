# Sugestions: Generate a customized MOU 逻辑

chat-input-panel.tsx 中的 handleOptionsClick 是负责页面上 
Suggestions: `Generate a customized MOU`
这里有3个旧的reset的逻辑(估计可以删除)：
- Document builder
- party1 
- party 2



chat-input-panel.tsx 中的 MultiplePromptSuggestions 是负责页面上
documentBuilder, party1,2 和 muti selections

由于我们现在需要让用户在选择了 Suggestions: `Generate a customized MOU` 的时候， 直接重置
MultiplePromptSuggestions 中的 documentBuilder, party1,2 和 muti selections， 因此也是需要在
handleOptionsClick 中增加 setResetMultiplePrompts(true) 的逻辑，它跟下面这部分代码
```typescript
useEffect(() => {
    if (props.resetState) {
        setSelectedDocumentBuilder(documentBuilderOptions[0]);
        setSelectedParty1(party1Options[0]);
        setSelectedParty2(party2Options[0]);
    }
}, [props.resetState]);
```

相呼应。 注意这里使用了 Boolean 的方法， 默认是false