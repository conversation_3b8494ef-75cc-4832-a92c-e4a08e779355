from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium import webdriver
from selenium.webdriver.support.ui import Select
import time
import os
import requests

# Initialize the Selenium WebDriver
driver = webdriver.Chrome()  # or use Firefox(), Edge(), etc.
driver.get("https://www.fda.gov/drugs/pharmaceutical-quality-resources/search-pharmaceutical-quality-documents")

# Find the dropdown menu and select "All"
select_element = Select(driver.find_element(By.NAME, "DataTables_Table_0_length"))
select_element.select_by_value("-1")
time.sleep(5)  # Wait for the page to load all documents

# Extract all document links
# document_links = [element.get_attribute('href') for element in driver.find_elements(By.XPATH, '//a[@data-entity-substitution="canonical"]')]
document_links = [(element.get_attribute('href'), element.text) for element in driver.find_elements(By.XPATH, '//td/a[@href]')]

# Download PDFs
os.makedirs('pdfs', exist_ok=True)
import re

# ...
# count of document_links
# ...
print(f"Found {len(document_links)} documents")
count = 0 
for document_link, link_text in document_links:
    time.sleep(1)  # Wait for the page to load all documents
    print(count)
    count += 1
    # print(f"Processing {document_link}")
    driver.get(document_link)
    try:    
        pdf_link = ''
        if '/media/' in document_link:
            pdf_link =  document_link
        else:
            pdf_link = WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.XPATH, '//a[contains(@href, "/media/") and contains(@class, "btn-primary")]'))).get_attribute('href')
        # print(pdf_link)
        response = requests.get(pdf_link, stream=True)
        response.raise_for_status()
        # Get the filename from the 'Content-Disposition' header
        content_disposition = response.headers.get('content-disposition')
        if content_disposition:
            filename = document_link.split('/')[-1]
        filename += '.pdf'

        if link_text:
            filename = re.sub(r'[^\w\s-]', '', link_text)
            filename = re.sub(r'[-\s]+', '-', filename)
            filename += '.pdf'
   
        pdf_path = os.path.join('pdfs', filename)
        # print(f"Downloading {pdf_link} to {pdf_path}")
        with open(pdf_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
    except Exception as e:
        print(f"Failed to download PDF from {document_link}: {e}")

driver.quit()