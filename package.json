{"name": "irpolicybot", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite  --mode dev", "build": "tsc && vite build", "build:dev": "tsc && cross-env NODE_ENV=dev vite build --mode dev", "build:qa": "tsc && cross-env NODE_ENV=qa vite build --mode qa", "build:prod": "tsc && cross-env NODE_ENV=production vite build --mode production", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "timestamp": "node ./timestamp.cjs", "format": "npx prettier --ignore-path .gitignore --write \"**/*.+(tsx|js|ts|json)\""}, "dependencies": {"@aws-amplify/ui-react": "^5.3.2", "@cloudscape-design/collection-hooks": "^1.0.37", "@cloudscape-design/component-toolkit": "^1.0.0-beta.101", "@cloudscape-design/components": "^3.0.985", "@cloudscape-design/design-tokens": "^3.0.28", "@cloudscape-design/global-styles": "^1.0.13", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@fortawesome/fontawesome-svg-core": "^6.5.2", "@fortawesome/free-solid-svg-icons": "^6.5.2", "@fortawesome/react-fontawesome": "^0.2.2", "@lexical/code": "^0.30.0", "@lexical/link": "^0.30.0", "@lexical/list": "^0.30.0", "@lexical/react": "^0.30.0", "@lexical/rich-text": "^0.30.0", "@lexical/table": "^0.30.0", "@lexical/utils": "^0.30.0", "@modelcontextprotocol/sdk": "^1.10.2", "@okta/okta-auth-js": "7.0.2", "@okta/okta-react": "^6.7.0", "@react-pdf-viewer/core": "^3.12.0", "@react-pdf-viewer/default-layout": "^3.12.0", "@react-pdf-viewer/search": "^3.12.0", "@reduxjs/toolkit": "^2.2.7", "@semoss/sdk": "^1.0.0-beta.19", "@semoss/sdk-react": "^1.0.0-beta.16", "aws-amplify": "^5.3.12", "docx": "^9.3.0", "dompurify": "^3.2.5", "file-saver": "^2.0.5", "framer-motion": "^12.15.0", "irpolicybot": "file:", "javascript-time-ago": "^2.5.10", "lexical": "^0.30.0", "lodash": "^4.17.21", "luxon": "^3.4.3", "markdown-to-jsx": "^7.2.1", "marked": "^5.1.2", "mobx": "^6.3.7", "moment": "2.29.4", "moment-timezone": "0.5.28", "node-forge": "^1.3.1", "pdfjs-dist": "^3.4.120", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-error-boundary": "^4.0.13", "react-hook-form": "^7.34.2", "react-icons": "^4.12.0", "react-json-view-lite": "^0.9.8", "react-markdown": "^9.0.0", "react-redux": "^9.1.2", "react-router-dom": "^6.15.0", "react-speech-recognition": "^3.10.0", "react-textarea-autosize": "^8.5.3", "react-time-ago": "^7.3.1", "react-use-websocket": "^4.5.0", "redux-logger": "^3.0.6", "regenerator-runtime": "^0.14.0", "rehype-raw": "^7.0.0", "remark-docx": "^0.1.6", "remark-gfm": "^4.0.0", "remark-parse": "^11.0.0", "replace-in-file": "7.0.2", "unified": "^11.0.5", "uuid": "^9.0.0"}, "devDependencies": {"@types/file-saver": "^2.0.7", "@types/lodash": "^4.14.202", "@types/luxon": "^3.3.2", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@types/react-speech-recognition": "^3.9.2", "@types/uuid": "^9.0.3", "@types/zen-observable": "^0.8.5", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.14", "cross-env": "^7.0.3", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "http-proxy-middleware": "^2.0.6", "postcss": "^8.4.27", "sass": "^1.65.1", "typescript": "^5.0.2", "vite": "^6.3.5", "zen-observable": "^0.10.0"}}