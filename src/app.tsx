import { useContext, useState } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Route,
  Routes,
} from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "./redux/store/configureStore";
import { AppContext } from "./common/app-context";
import GlobalHeader from "./components/global-header";
import RequiredAuth from "./components/secure-route";
import Playground from "./pages/chatbot/playground/playground";
import AddData from "./pages/rag/add-data/add-data";
import CreateWorkspace from "./pages/rag/create-workspace/create-workspace";
import WorkspacePane from "./pages/rag/workspace/workspace";
import Workspaces from "./pages/rag/workspaces/workspaces";
import Welcome from "./pages/welcome";
import PromptPlaygroundPage from "./pages/chatbot/prompts/prompt-playground-page";
import PromptHubPage from "./pages/chatbot/prompts/prompt-hub-page";
import Reports from "./pages/reports/reports";
import SemanticSearch from "./pages/rag/semantic-search/semantic-search";
import NotFound from "./pages/not-found";
import { UserInfo } from "./common/types";
import { SpaceBetween, Modal, Box, Button } from "@cloudscape-design/components";
import "./styles/app.scss";
// ①
import MCPPage from "@/pages/mcp/mcp-page";
import SectionPage from "./pages/sections/SectionPage";
import PreviewPage from "./pages/preview/PreviewPage";

function App() {
  // Get app context and router type
  const appContext = useContext(AppContext);
  const Router = appContext?.config.privateWebsite ? HashRouter : BrowserRouter;

  // Redux state
  const dataState = useSelector((state: RootState) => state.rootReducer);
  const userRoles = dataState?.chatReducer?.userRoles;

  // Local states
  const [userInfo, setUserInfo] = useState<UserInfo>();
  const [visible, setVisible] = useState(true);

  const dispatch = useDispatch();
  const basePath = import.meta.env.VITE_BASE_PATH;

  return (
    <div style={{ height: "100%" }}>
      {/* Accountability and Privacy Warning Modal */}
      {visible && (
        <Modal
          visible={visible}
          footer={
            <Box float="right">
              <SpaceBetween direction="horizontal" size="xs">
                <Button variant="primary" onClick={() => setVisible(false)}>
                  I Agree
                </Button>
              </SpaceBetween>
            </Box>
          }
        >
          <div style={{marginLeft: '-6px', marginRight: '0px', marginTop: '-46px' }}>
            <h2 style={{ textAlign: 'center', marginTop: '10px', marginBottom: '15px' }}>
              Accountability and Privacy Warning
            </h2>
            <h3 style={{ textAlign: 'center', marginTop: '0', marginBottom: '20px' }}>
              Welcome to Data Governance Document Builder
            </h3>
          <ul style={{ 
            listStyle: 'disc', 
            paddingLeft: '20px',
            lineHeight: '1.5'
          }}>
            <style>{`
              ul li {
                margin-bottom: 15px;
              }
              ul li:last-child {
                margin-bottom: 0;
              }
            `}</style>
            <li>
              <strong>User Responsibility:</strong> Users should be aware that generative AI tools, such as the Data
              Governance Document Builder, can produce output that is incorrect, inaccurate, incomplete,
              outdated, biased, offensive, or plagiarized, and may include content that is copyrighted. Users of
              the Data Governance Document Builder are solely responsible for reviewing, verifying, and
              where appropriate, modifying the output generated by the tool. Users must ensure that any
              agreement generated is complete, accurate, and appropriate for its intended use before relying
              on or executing such documents.
            </li>
            <li>
            {/*<b>DUA builder users are prohibited from <span style={{ color: '#ff0000' }}><strong>uploading any Personally Identifiable Information (PII)</strong></span> into DUA builder. </b>*/}
            {/* This measure is in place to protect privacy and maintain data security.*/}
              <strong>Tool Limitations:</strong> This tool is designed to assist with the initial drafting process and is not a
              substitute for professional legal review. This tool does not constitute legal advice and may not
              account for all applicable laws, regulations, institutional policies, or specific contextual
              requirements.
            </li>
            <li>
              <strong>Creator and Owner Disclaimer:</strong> The creators and owners of the Data Governance Document
              Builder provide the tool "as-is" and make no warranties or representations regarding the
              accuracy, completeness, or suitability of the generated content for any particular purpose. The
              creators and owners disclaim any liability for damages arising from the use of the tool or
              reliance on its output. Users are advised to consult with qualified legal professionals prior to
              implementing or executing any DUAs based on the generated content.
            </li>
            <li>
              <strong>Review Acknowledgement:</strong> By using the Data Governance Document Builder, users
              acknowledge that they understand their obligation to review and vet all generated agreements
              for legal sufficiency, compliance, and appropriateness. Failure to do so is at the user's own risk.
            </li>
          </ul>
          </div>
        </Modal>
      )}

      {/* Main Router */}
      <Router basename={basePath}>
        <GlobalHeader />
        <div style={{ height: "56px", backgroundColor: "#000716" }}>&nbsp;</div>
        <div>
          <Routes>
            {/* Secure all routes under RequiredAuth */}
            <Route element={<RequiredAuth />}>
              {/* Root Route - Direct to SectionPage */}
              <Route index element={<SectionPage />} />

              {/* Preview Route */}
              <Route path="/preview" element={<PreviewPage />} />

              {/* Sections Routes */}
              <Route path="/sections" element={<SectionPage />} />

              {/* Chatbot Routes */}
              <Route path="/chatbot" element={<RequiredAuth />}>
                <Route path="playground" element={<Playground />} />
                <Route path="playground/:sessionId" element={<Playground />} />
                {!userRoles?.includes("READ_ONLY") && (
                  <>
                    <Route path="prompts" element={<PromptHubPage />} />
                    <Route
                      path="prompts/:promptId"
                      element={<PromptPlaygroundPage />}
                    />
                  </>
                )}
              </Route>

              {/* RAG (Retrieval-Augmented Generation) Routes */}
              <Route path="/rag" element={<RequiredAuth />}>
                <Route path="workspaces" element={<Workspaces />} />
                <Route path="workspaces/:workspaceId" element={<WorkspacePane />} />
                {!userRoles?.includes("READ_ONLY") && (
                  <>
                    <Route path="workspaces/create" element={<CreateWorkspace />} />
                    <Route path="semantic-search" element={<SemanticSearch />} />
                    <Route path="workspaces/add-data" element={<AddData />} />
                  </>
                )}
              </Route>
              {/*MCP routes*/}
              <Route path="/mcp" element={<RequiredAuth />} >
              <Route index element={<MCPPage/>} />
              </Route>
              {/* Reports (Only for authorized users) */}
              <Route path="/reports" element={<RequiredAuth />}>
                <Route path="export_csv" element={<Reports />} />
              </Route>

              {/* 404 Not Found */}
              <Route path="*" element={<NotFound />} />
            </Route>
          </Routes>
        </div>
      </Router>
    </div>
  );
}

export default App;
