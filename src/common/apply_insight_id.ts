import { AxiosInstance } from "axios";
import { allApis } from "@/configs/api";

let insightId: string | null = null; // Shared variable to store insightId

// Function to set the insightId
export const setInsightId = (id: string) => {
  insightId = id;
  allApis.forEach(applyInsightIdInterceptor);

};

// Function to apply an interceptor to an Axios instance
export const applyInsightIdInterceptor = (instance: AxiosInstance) => {
  instance.interceptors.request.use(
    (config) => {
      if (insightId) {
        config.headers["x-insight-id"] = insightId; // Add insightId to headers
        config.headers["details"] = document.cookie }
      config.withCredentials = false;
      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );
};
