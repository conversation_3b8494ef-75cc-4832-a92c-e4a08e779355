import {
    Document,
    Packer,
    Paragraph,
    Table,
    TableRow,
    TableCell,
    TextRun,
    BorderStyle
} from 'docx';

import {
    ParsedElement,
    ParsedText,
    ParsedHeading,
    ParsedParagraph,
    ParsedTable,
    Parsed<PERSON>ode<PERSON><PERSON>,
    ParsedList,
    Parsed<PERSON>lockquote,
    ParsedHorizontalRule
} from './markdown-parser';

export class DocxConverter {
    public convertToDocxElements(elements: ParsedElement[]): any[] {
        const docxElements: any[] = [];

        elements.forEach(element => {
            switch (element.type) {
                case 'heading':
                    docxElements.push(this.convertHeading(element as ParsedHeading));
                    break;

                case 'paragraph':
                    docxElements.push(this.convertParagraph(element as ParsedParagraph));
                    break;

                case 'table':
                    docxElements.push(this.convertTable(element as ParsedTable));
                    break;

                case 'codeBlock':
                    docxElements.push(...this.convertCodeBlock(element as Parsed<PERSON>odeBlock));
                    break;

                case 'list':
                    docxElements.push(...this.convertList(element as ParsedList));
                    break;

                case 'blockquote':
                    docxElements.push(...this.convertBlockquote(element as ParsedBlockquote));
                    break;

                case 'hr':
                    docxElements.push(this.convertHorizontalRule());
                    break;
            }
        });

        return docxElements;
    }

    private convertHeading(heading: ParsedHeading): Paragraph {
        const headingLevel = this.getHeadingLevel(heading.level);

        return new Paragraph({
            text: heading.content,
            heading: headingLevel
        });
    }

    private getHeadingLevel(level: number): "Heading1" | "Heading2" | "Heading3" | "Heading4" | "Heading5" | "Heading6" | "Title" {
        switch (level) {
            case 1: return "Heading1";
            case 2: return "Heading2";
            case 3: return "Heading3";
            case 4: return "Heading4";
            case 5: return "Heading5";
            case 6: return "Heading6";
            default: return "Heading1";
        }
    }

    private convertParagraph(paragraph: ParsedParagraph): Paragraph {
        if (!paragraph.content || paragraph.content.length === 0) {
            return new Paragraph({});
        }

        const textRuns = paragraph.content.map(text => this.createTextRun(text));

        return new Paragraph({
            children: textRuns
        });
    }

    private createTextRun(text: ParsedText): TextRun {
        return new TextRun({
            text: text.content,
            bold: text.bold,
            italics: text.italic,
            strike: text.strike,
            style: text.code ? 'Code' : undefined
        });
    }

    private convertTable(table: ParsedTable): Table {
        const tableRows: TableRow[] = [];

        if (table.headers.length > 0) {
            tableRows.push(
                new TableRow({
                    children: table.headers.map(header =>
                        new TableCell({
                            children: [new Paragraph({
                                children: [new TextRun({ text: header, bold: true })]
                            })],
                            shading: { fill: '#F2F2F2' }
                        })
                    )
                })
            );
        }

        table.rows.forEach(rowData => {
            tableRows.push(
                new TableRow({
                    children: rowData.map(cell =>
                        new TableCell({
                            children: [new Paragraph({ text: cell })]
                        })
                    )
                })
            );
        });

        return new Table({
            rows: tableRows,
            width: {
                size: 100,
                type: 'pct'
            },
            borders: {
                top: { style: BorderStyle.SINGLE, size: 1, color: "#BFBFBF" },
                bottom: { style: BorderStyle.SINGLE, size: 1, color: "#BFBFBF" },
                left: { style: BorderStyle.SINGLE, size: 1, color: "#BFBFBF" },
                right: { style: BorderStyle.SINGLE, size: 1, color: "#BFBFBF" },
                insideHorizontal: { style: BorderStyle.SINGLE, size: 1, color: "#BFBFBF" },
                insideVertical: { style: BorderStyle.SINGLE, size: 1, color: "#BFBFBF" }
            }
        });
    }

    private convertCodeBlock(codeBlock: ParsedCodeBlock): Paragraph[] {
        const paragraphs: Paragraph[] = [];

        paragraphs.push(new Paragraph({
            text: '```' + codeBlock.language,
            style: 'Code' as any
        }));

        codeBlock.content.forEach(line => {
            paragraphs.push(new Paragraph({
                text: line,
                style: 'Code' as any
            }));
        });

        paragraphs.push(new Paragraph({
            text: '```',
            style: 'Code' as any
        }));

        return paragraphs;
    }

    private convertList(list: ParsedList): Paragraph[] {
        const paragraphs: Paragraph[] = [];

        list.items.forEach((item, index) => {
            const prefix = list.ordered ? `${index + 1}. ` : '• ';

            paragraphs.push(new Paragraph({
                text: prefix + item,
                indent: {
                    left: 720 // 0.5 inches ?
                }
            }));
        });

        return paragraphs;
    }

    private convertBlockquote(blockquote: ParsedBlockquote): Paragraph[] {
        const paragraphs: Paragraph[] = [];

        blockquote.content.forEach(text => {
            paragraphs.push(new Paragraph({
                text: text,
                indent: {
                    left: 720 // 0.5 英寸
                },
                border: {
                    left: {
                        color: '#CCCCCC',
                        style: BorderStyle.SINGLE,
                        size: 12
                    }
                },
                style: 'Quote' as any
            }));
        });

        return paragraphs;
    }

    private convertHorizontalRule(): Paragraph {
        return new Paragraph({
            border: {
                bottom: {
                    color: '#CCCCCC',
                    style: BorderStyle.SINGLE,
                    size: 12
                }
            },
            spacing: {
                before: 240,
                after: 240
            }
        });
    }

    public createDocument(elements: any[]): Document {
        return new Document({
            styles: this.getDocumentStyles(),
            sections: [{
                properties: {},
                children: elements
            }]
        });
    }

    public getDocumentStyles(): any {
        return {
            paragraphStyles: [
                {
                    id: "Heading1",
                    name: "Heading 1",
                    basedOn: "Normal",
                    next: "Normal",
                    quickFormat: true,
                    run: {
                        bold: true,
                        size: 28,
                        color: "000000"
                    },
                    paragraph: {
                        alignment: "center",
                        spacing: {
                            before: 240,
                            after: 120
                        }
                    }
                },
                {
                    id: "Heading2",
                    name: "Heading 2",
                    basedOn: "Normal",
                    next: "Normal",
                    quickFormat: true,
                    run: {
                        bold: true,
                        size: 24,
                        color: "000000"
                    },
                    paragraph: {
                        spacing: {
                            before: 200,
                            after: 100
                        }
                    }
                },
                {
                    id: "Heading3",
                    name: "Heading 3",
                    basedOn: "Normal",
                    next: "Normal",
                    quickFormat: true,
                    run: {
                        bold: true,
                        size: 20,
                        color: "000000"
                    },
                    paragraph: {
                        spacing: {
                            before: 180,
                            after: 90
                        }
                    }
                },
                {
                    id: "Heading4",
                    name: "Heading 4",
                    basedOn: "Normal",
                    next: "Normal",
                    quickFormat: true,
                    run: {
                        bold: true,
                        size: 18,
                        color: "000000"
                    },
                    paragraph: {
                        spacing: {
                            before: 160,
                            after: 80
                        }
                    }
                },
                {
                    id: "Heading5",
                    name: "Heading 5",
                    basedOn: "Normal",
                    next: "Normal",
                    quickFormat: true,
                    run: {
                        bold: true,
                        size: 16,
                        color: "000000"
                    },
                    paragraph: {
                        spacing: {
                            before: 140,
                            after: 70
                        }
                    }
                },
                {
                    id: "Heading6",
                    name: "Heading 6",
                    basedOn: "Normal",
                    next: "Normal",
                    quickFormat: true,
                    run: {
                        bold: true,
                        size: 14,
                        color: "000000"
                    },
                    paragraph: {
                        spacing: {
                            before: 120,
                            after: 60
                        }
                    }
                },
                {
                    id: "Code",
                    name: "Code",
                    basedOn: "Normal",
                    next: "Normal",
                    quickFormat: true,
                    run: {
                        font: "Courier New",
                        size: 20
                    },
                    paragraph: {
                        spacing: {
                            before: 120,
                            after: 120
                        },
                        shading: {
                            fill: "F5F5F5"
                        }
                    }
                },
                {
                    id: "Quote",
                    name: "Quote",
                    basedOn: "Normal",
                    next: "Normal",
                    quickFormat: true,
                    run: {
                        italics: true,
                        color: "666666"
                    },
                    paragraph: {
                        indent: {
                            left: 720
                        },
                        spacing: {
                            before: 120,
                            after: 120
                        },
                        border: {
                            left: {
                                color: "#CCCCCC",
                                style: BorderStyle.SINGLE,
                                size: 12
                            }
                        }
                    }
                }
            ]
        };
    }
}