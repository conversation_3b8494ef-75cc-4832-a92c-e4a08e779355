import { Document, Packer } from 'docx';
import { saveAs } from 'file-saver';
import { MarkdownParser } from './markdown-parser';
import { DocxConverter } from './docx-converter';

export const markdownToDocxAndDownload = async (
    text: string,
    filename: string,
): Promise<void> => {
    try {
        console.log("Markdown to DOCX...");

        // create markdown
        const parser = new MarkdownParser();
        const parsedElements = parser.parse(text);

        // 2. create DOCX converter to DOCX elements
        const converter = new DocxConverter();
        const docxElements = converter.convertToDocxElements(parsedElements);

        const doc = converter.createDocument(docxElements);

        Packer.toBlob(doc).then(blob => {
            saveAs(blob, filename);
            console.log("Done");
        });
    } catch (error) {
        console.error("markdownToDocxAndDownload error:", error);
        throw error;
    }
};