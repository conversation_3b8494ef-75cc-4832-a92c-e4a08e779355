import { SelectProps } from '@cloudscape-design/components';
import { OptionsHelper } from './options-helper';
import { StorageHelper } from './storage-helper';
import { Model } from '../../API';

export abstract class ModelsHelper {
    static getSelectedModelOption(models: Model[]): SelectProps.Option | null {
        let selectedModelOption: SelectProps.Option | null = null;
        const savedModel = StorageHelper.getSelectedLLM();

        if (savedModel) {
            const savedModelDetails = OptionsHelper.parseValue(savedModel);
            const targetModel = models.find(
                (m) =>
                    m.name === savedModelDetails.name &&
                    m.provider === savedModelDetails.provider,
            );

            if (targetModel) {
                selectedModelOption = OptionsHelper.getSelectOptionGroups([
                    targetModel,
                ])[0].options[0];
            }
        }

        let candidate: Model | undefined = undefined;
        if (!selectedModelOption) {
            const bedrockModels = models.filter(
                (m) => m.provider === 'bedrock',
            );

            // First priority: look for the specified Claude 3 Haiku model
            candidate = bedrockModels.find(
                // (m) => m.name === "anthropic.claude-3-haiku-20240307-v1:0",
                (m) => m.name === "anthropic.claude-3-5-sonnet-20240620-v1:0",
            );

            // If the specified model isn't available, fall back to other models
            if (!candidate) {
                candidate = bedrockModels.find(
                    (m) => m.name === "anthropic.claude-3-5-sonnet-20240620-v1:0",
                );
            }

            if (!candidate) {
                candidate = bedrockModels.find(
                    (m) => m.name === 'anthropic.claude-v1',
                );
            }

            if (!candidate) {
                candidate = bedrockModels.find(
                    (m) => m.name === 'amazon.titan-tg1-large',
                );
            }

            if (!candidate) {
                candidate = bedrockModels.find((m) =>
                    m.name.startsWith('amazon.titan-'),
                );
            }

            const sageMakerModels = models.filter(
                (m) => m.provider === 'sagemaker',
            );
            if (!candidate && sageMakerModels.length > 0) {
                candidate = sageMakerModels[0];
            }

            const openAIModels = models.filter((m) => m.provider === 'openai');
            if (openAIModels.length > 0) {
                if (!candidate) {
                    candidate = openAIModels.find((m) => m.name === 'gpt-4');
                }

                if (!candidate) {
                    candidate = openAIModels.find(
                        (m) => m.name === 'gpt-3.5-turbo-16k',
                    );
                }
            }

            if (!candidate && bedrockModels.length > 0) {
                candidate = bedrockModels[0];
            }

            if (candidate) {
                selectedModelOption = OptionsHelper.getSelectOptionGroups([
                    candidate,
                ])[0].options[0];
            }
        }

        return selectedModelOption;
    }
}