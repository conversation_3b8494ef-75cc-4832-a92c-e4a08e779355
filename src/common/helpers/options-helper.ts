import { SelectProps } from '@cloudscape-design/components';

export abstract class OptionsHelper {
    /**
     *
     * @param model
     * @returns
     */
    static getSelectOption(model?: string): SelectProps.Option | null {
        if (!model) return null;
        const [, name] = model.split('::') ?? [];
        if (!name) return null;

        return {
            label: name,
            value: model,
        };
    }

    /**
     *
     * @param value
     * @returns
     */
    static parseValue(value?: string) {
        const retValue = {
            provider: '',
            name: '',
        };

        try {
            if (!value) return retValue;
            const [provider, name] = value.split('::') ?? [];

            return {
                provider,
                name,
            };
        } catch (error) {
            console.error(error);
            return retValue;
        }
    }

    /**
     *
     * @param value
     * @returns
     */
    static parseWorkspaceValue(value?: string) {
        const retValue = {
            engine: '',
            name: '',
        };

        try {
            if (!value) return retValue;
            const [engine, name] = value.split('::') ?? [];

            return {
                engine,
                name,
            };
        } catch (error) {
            console.error(error);
            return retValue;
        }
    }

    /**
     *
     * @param data
     * @returns
     */
    static getSelectOptionGroups<T extends { provider: string; name: string }>(
        data: T[],
    ) {
        const modelsMap = new Map<string, T[]>();
        data.forEach((item) => {
            let items = modelsMap.get(item.provider);
            if (!items) {
                items = [];
                modelsMap.set(item.provider, [item]);
            } else {
                modelsMap.set(item.provider, [...items, item]);
            }
        });

        const keys = [...modelsMap.keys()];
        keys.sort((a, b) => a.localeCompare(b));

        const options: SelectProps.OptionGroup[] = keys.map((key) => {
            const items = modelsMap.get(key);
            items?.sort((a, b) => a.name.localeCompare(b.name));

            return {
                label: this.getProviderLabel(key),
                options:
                    items?.map((item) => ({
                        label: item.name,
    			value: item.name === 'cfggpt' ? `${item.provider}::${item.name}` : `${item.provider}::${item.name}`,
                    })) ?? [],
            };
        });

        return options;
    }

    /**
     *
     * @param data
     * @returns
     */
    static getSelectWorkspaceOptionGroups<
        T extends { engine: string; name: string },
    >(data: T[]) {
        const workspacesMap = new Map<string, T[]>();
        data.forEach((item) => {
            let items = workspacesMap.get(item.engine);
            if (!items) {
                items = [];
                workspacesMap.set(item.engine, [item]);
            } else {
                workspacesMap.set(item.engine, [...items, item]);
            }
        });

        const keys = [...workspacesMap.keys()];
        keys.sort((a, b) => a.localeCompare(b));

        const options: SelectProps.OptionGroup[] = keys.map((key) => {
            const items = workspacesMap.get(key);
            items?.sort((a, b) => a.name.localeCompare(b.name));

            return {
                label: this.getProviderLabel(key),
                options:
                    items?.map((item) => ({
                        label: item.name,
                        value: `${item.engine}::${item.name}`,
                    })) ?? [],
            };
        });

        return options;
    }

    static getSelectWorkspaceOptions<
        T extends { engine: string; workspaceId: string; name: string },
    >(data: T[]) {
        data?.sort((a, b) => a.name.localeCompare(b.name));

        const workspaceMap = new Map<string, T[]>();
        data.forEach((item) => {
            let items = workspaceMap.get(item.engine);
            if (!items) {
                items = [];
                workspaceMap.set(item.engine, [item]);
            } else {
                workspaceMap.set(item.engine, [...items, item]);
            }
        });

        const keys = [...workspaceMap.keys()];
        keys.sort((a, b) => a.localeCompare(b));

        const options: SelectProps.OptionGroup[] = keys.map((key) => {
            const items = workspaceMap.get(key);
            items?.sort((a, b) => a.name.localeCompare(b.name));

            return {
                label: this.getWorkspaceEngineLabel(key),
                options:
                    items?.map((item) => ({
                        label: item.name,
                        value: item.workspaceId,
                    })) ?? [],
            };
        });

        return options;
    }

    /**
     *
     * @param data
     * @returns
     */
    static getSelectPromptOptions<T extends { id: string; name: string }>(
        data: T[],
    ) {
        data?.sort((a, b) => a.name.localeCompare(b.name));

        const options: SelectProps.Option[] = data.map((item) => {
            return {
                label: item.name,
                value: item.id,
            };
        });

        return options;
    }

    /**
     *
     * @param provider
     * @returns
     */
    static getProviderLabel(provider: string) {
        let label = provider;
        if (label === 'sagemaker') label = 'SageMaker';
        else if (label === 'bedrock') label = 'Bedrock';
        else if (label === 'openai') label = 'OpenAI';

        return label;
    }

    /**
     *
     * @param provider
     * @returns
     */
    static getWorkspaceEngineLabel(engine: string) {
        let label = engine;
        //need to redo grouping when we get the correct respone from the api for the engine
        if (label === '') label = 'CFGAI';
        else if (label === 'opensearch') label = 'Opensearch';
        else label = engine;

        return label;
    }
}
