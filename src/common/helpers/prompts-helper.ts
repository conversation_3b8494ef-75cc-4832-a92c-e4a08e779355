import { SelectProps } from '@cloudscape-design/components';
import { OptionsHelper } from './options-helper';
import { StorageHelper } from './storage-helper';

export abstract class PromptsHelper {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    static getSelectedPromptOption(prompts: any[]): SelectProps.Option | null {
        let selectedPromptOption: SelectProps.Option | null = null;

        const savedPromptId = StorageHelper.getSelectedPromptId();

        if (savedPromptId) {
            const targetPrompt = prompts.find((p) => p.id === savedPromptId);
            if (targetPrompt) {
                selectedPromptOption = OptionsHelper.getSelectPromptOptions([
                    targetPrompt,
                ])[0];
            }
        }

        // if (!selectedPromptOption && prompts) {
        //   selectedPromptOption = OptionsHelper.getSelectPromptOptions([
        //     prompts[0],
        // ])[0];;
        // }

        return selectedPromptOption;
    }
}