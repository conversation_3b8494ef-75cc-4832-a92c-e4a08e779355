import { SelectProps } from '@cloudscape-design/components';
import { OptionsHelper } from './options-helper';
import { StorageHelper } from './storage-helper';
import { Workspace } from '../../API';

export abstract class WorkspacesHelper {
    static getSelectedWorkspaceOption(
        workspaces: Workspace[],
    ): SelectProps.Option | null {
        let selectedWorkspaceOption: SelectProps.Option | null = null;
        const savedWorkspace = StorageHelper.getSelectedWorkspaceId();

        if (savedWorkspace) {
            const savedWorkspaceDetails =
                OptionsHelper.parseWorkspaceValue(savedWorkspace);
            const targetWorkspace = workspaces.find(
                (m) =>
                    m.name === savedWorkspaceDetails.name &&
                    m.engine === savedWorkspaceDetails.engine,
            );

            if (targetWorkspace) {
                selectedWorkspaceOption =
                    OptionsHelper.getSelectWorkspaceOptionGroups([
                        targetWorkspace,
                    ])[0].options[0];
            }
        }

        let candidate: Workspace | undefined = undefined;
        if (!selectedWorkspaceOption) {
            const cfgAIWorkspaces = workspaces.filter(
                (m) => m.engine === 'bedrock',
            );
            // const sageMakerModels = models.filter(
            //     (m) => m.provider === 'sagemaker',
            // );
            // const openAIModels = models.filter((m) => m.provider === 'openai');

            candidate = cfgAIWorkspaces.find(
                (m) => m.name === 'anthropic.claude-v2',
            );
            // if (!candidate) {
            //     candidate = bedrockModels.find(
            //         (m) => m.name === 'anthropic.claude-v1',
            //     );
            // }

            // if (!candidate) {
            //     candidate = bedrockModels.find(
            //         (m) => m.name === 'amazon.titan-tg1-large',
            //     );
            // }

            // if (!candidate) {
            //     candidate = bedrockModels.find((m) =>
            //         m.name.startsWith('amazon.titan-'),
            //     );
            // }

            // if (!candidate && sageMakerModels.length > 0) {
            //     candidate = sageMakerModels[0];
            // }

            // if (openAIModels.length > 0) {
            //     if (!candidate) {
            //         candidate = openAIModels.find((m) => m.name === 'gpt-4');
            //     }

            //     if (!candidate) {
            //         candidate = openAIModels.find(
            //             (m) => m.name === 'gpt-3.5-turbo-16k',
            //         );
            //     }
            // }

            // if (!candidate && bedrockModels.length > 0) {
            //     candidate = bedrockModels[0];
            // }

            if (candidate) {
                selectedWorkspaceOption =
                    OptionsHelper.getSelectWorkspaceOptionGroups([candidate])[0]
                        .options[0];
            }
        }

        return selectedWorkspaceOption;
    }
}
