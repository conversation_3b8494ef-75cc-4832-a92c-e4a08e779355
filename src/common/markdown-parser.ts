export interface ParsedElement {
    type: string;
    content?: any;
    language?: string;
    level?: number;
    headers?: string[];
    rows?: string[][];
    ordered?: boolean;
    items?: string[];
}

export interface ParsedText extends ParsedElement {
    type: 'text';
    content: string;
    bold?: boolean;
    italic?: boolean;
    strike?: boolean;
    code?: boolean;
}

export interface ParsedHeading extends ParsedElement {
    type: 'heading';
    content: string;
    level: number;
}

export interface ParsedParagraph extends ParsedElement {
    type: 'paragraph';
    content: ParsedText[];
}

export interface ParsedTable extends ParsedElement {
    type: 'table';
    headers: string[];
    rows: string[][];
}

export interface ParsedCodeBlock extends ParsedElement {
    type: 'codeBlock';
    language: string;
    content: string[];
}

export interface ParsedList extends ParsedElement {
    type: 'list';
    ordered: boolean;
    items: string[];
}

export interface ParsedBlockquote extends ParsedElement {
    type: 'blockquote';
    content: string[];
}

export interface ParsedHorizontalRule extends ParsedElement {
    type: 'hr';
}

export class MarkdownParser {
    public parse(markdownText: string): ParsedElement[] {
        const lines = markdownText.split('\n');
        const parsedElements: ParsedElement[] = [];

        let inTable = false;
        let tableHeaders: string[] = [];
        let tableRows: string[][] = [];

        let inCodeBlock = false;
        let codeBlockLanguage = '';
        let codeBlockContent: string[] = [];

        let inList = false;
        let listItems: string[] = [];
        let isOrderedList = false;

        let inBlockquote = false;
        let blockquoteContent: string[] = [];

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();

            if (line.startsWith('```')) {
                if (!inCodeBlock) {
                    inCodeBlock = true;
                    codeBlockLanguage = line.substring(3).trim();
                    codeBlockContent = [];
                } else {
                    // 结束当前代码块
                    parsedElements.push({
                        type: 'codeBlock',
                        language: codeBlockLanguage,
                        content: codeBlockContent
                    });
                    inCodeBlock = false;
                    codeBlockLanguage = '';
                    codeBlockContent = [];
                }
                continue;
            }

            if (inCodeBlock) {
                codeBlockContent.push(line);
                continue;
            }

            // table
            if (line.startsWith('|') && line.endsWith('|')) {
                const isSeparatorRow = line.replace(/\||-|\s/g, '').length === 0;

                if (!inTable) {
                    inTable = true;
                    tableHeaders = this.parseTableRow(line);
                } else if (isSeparatorRow) {
                    continue;
                } else {
                    tableRows.push(this.parseTableRow(line));
                }
                continue;
            } else if (inTable) {
                parsedElements.push({
                    type: 'table',
                    headers: tableHeaders,
                    rows: tableRows
                });
                inTable = false;
                tableHeaders = [];
                tableRows = [];
            }

            if (line.startsWith('#')) {
                let level = 0;
                for (let j = 0; j < line.length; j++) {
                    if (line[j] === '#') {
                        level++;
                    } else {
                        break;
                    }
                }

                const headingText = line.substring(level).trim();
                parsedElements.push({
                    type: 'heading',
                    content: headingText,
                    level: level
                });
                continue;
            }

            if (line.match(/^(\*{3,}|-{3,}|_{3,})$/)) {
                parsedElements.push({ type: 'hr' });
                continue;
            }

            if (line.startsWith('>')) {
                if (!inBlockquote) {
                    inBlockquote = true;
                    blockquoteContent = [];
                }

                const quoteContent = line.substring(1).trim();
                blockquoteContent.push(quoteContent);
                continue;
            } else if (inBlockquote) {
                parsedElements.push({
                    type: 'blockquote',
                    content: blockquoteContent
                });
                inBlockquote = false;
                blockquoteContent = [];
            }

            if (line.match(/^(\d+\.|\*|\-|\+)\s/)) {
                const isOrdered = /^\d+\./.test(line);

                if (!inList) {
                    inList = true;
                    listItems = [];
                    isOrderedList = isOrdered;
                }

                const itemContent = line.replace(/^(\d+\.|\*|\-|\+)\s/, '');
                listItems.push(itemContent);
                continue;
            } else if (inList && line === '') {
                parsedElements.push({
                    type: 'list',
                    ordered: isOrderedList,
                    items: listItems
                });
                inList = false;
                listItems = [];
            } else if (inList) {
                // 非空行也结束列表
                parsedElements.push({
                    type: 'list',
                    ordered: isOrderedList,
                    items: listItems
                });
                inList = false;
                listItems = [];
            }

            if (line.length > 0) {
                parsedElements.push({
                    type: 'paragraph',
                    content: this.parseTextFormatting(line)
                });
            } else if (line === '' && i > 0 && i < lines.length - 1) {
                parsedElements.push({
                    type: 'paragraph',
                    content: []
                });
            }
        }

        if (inTable) {
            parsedElements.push({
                type: 'table',
                headers: tableHeaders,
                rows: tableRows
            });
        }

        if (inCodeBlock) {
            parsedElements.push({
                type: 'codeBlock',
                language: codeBlockLanguage,
                content: codeBlockContent
            });
        }

        if (inBlockquote) {
            parsedElements.push({
                type: 'blockquote',
                content: blockquoteContent
            });
        }

        if (inList) {
            parsedElements.push({
                type: 'list',
                ordered: isOrderedList,
                items: listItems
            });
        }

        return parsedElements;
    }

    private parseTableRow(line: string): string[] {
        return line
            .substring(1, line.length - 1)
            .split('|')
            .map(cell => cell.trim());
    }

    private parseTextFormatting(text: string): ParsedText[] {
        const parts: ParsedText[] = [];
        let currentPos = 0;

        if (/^\*\*\*(.*)\*\*\*$/.test(text)) {
            return [{
                type: 'text',
                content: text.replace(/^\*\*\*|\*\*\*$/g, ''),
                bold: true,
                italic: true
            }];
        } else if (/^\*\*(.*)\*\*$/.test(text)) {
            return [{
                type: 'text',
                content: text.replace(/^\*\*|\*\*$/g, ''),
                bold: true
            }];
        } else if (/^\*(.*)\*$/.test(text) || /^_(.*)_$/.test(text)) {
            return [{
                type: 'text',
                content: text.replace(/^\*|\*$|^_|_$/g, ''),
                italic: true
            }];
        } else if (/^~~(.*)~~$/.test(text)) {
            return [{
                type: 'text',
                content: text.replace(/^~~|~~$/g, ''),
                strike: true
            }];
        } else if (/^`(.*)`$/.test(text)) {
            return [{
                type: 'text',
                content: text.replace(/^`|`$/g, ''),
                code: true
            }];
        }

        let remaining = text;
        let plainStart = 0;

        const formatMarkers = [
            { pattern: /\*\*\*(.*?)\*\*\*/g, format: { bold: true, italic: true } },
            { pattern: /\*\*(.*?)\*\*/g, format: { bold: true } },
            { pattern: /\*(.*?)\*/g, format: { italic: true } },
            { pattern: /_(.*?)_/g, format: { italic: true } },
            { pattern: /~~(.*?)~~/g, format: { strike: true } },
            { pattern: /`(.*?)`/g, format: { code: true } }
        ];

        let hasFormat = false;

        for (const marker of formatMarkers) {
            if (marker.pattern.test(text)) {
                hasFormat = true;
                break;
            }
        }

        if (!hasFormat) {
            parts.push({
                type: 'text',
                content: text
            });
        } else {
            parts.push({
                type: 'text',
                content: text
            });
        }
        return parts;
    }
}