import { SelectProps } from '@cloudscape-design/components';
import { ImageFile } from '../components/chatbot/types';

export interface AppConfig {
    aws_project_region: string;
    config: {
        rag_enabled: boolean;
        cross_encoders_enabled: boolean;
        sagemaker_embeddings_enabled: boolean;
        api_endpoint: string;
        default_embeddings_model: string;
        default_cross_encoder_model: string;
        privateWebsite: boolean;
    };
}

export interface NavigationPanelState {
    collapsed?: boolean;
    collapsedSections?: Record<number, boolean>;
}

export type LoadingStatus = 'pending' | 'loading' | 'finished' | 'error';
export type ModelProvider = 'sagemaker' | 'bedrock' | 'openai';
export type RagDocumentType =
    | 'file'
    | 'text'
    | 'qna'
    | 'website'
    | 'rssfeed'
    | 'rsspost';
export type Modality = 'TEXT' | 'IMAGE';
export type ModelInterface = 'langchain' | 'idefics';

export interface DocumentSubscriptionToggleResult {
    id: string;
    workspaceId: string;
    status: string;
}

export enum DocumentSubscriptionStatus {
    ENABLED = 'enabled',
    DISABLED = 'disabled',
    UNKNOWN = 'unknown',
    DEFAULT = UNKNOWN,
}

export interface AuroraWorkspaceCreateInput {
    name: string;
    embeddingsModel: SelectProps.Option | null;
    crossEncoderModel: SelectProps.Option | null;
    languages: readonly SelectProps.Option[];
    metric: string;
    index: boolean;
    hybridSearch: boolean;
    chunkSize: number;
    chunkOverlap: number;
}

export interface OpenSearchWorkspaceCreateInput {
    name: string;
    embeddingsModel: SelectProps.Option | null;
    embeddingsModelProvider: string;
    languages: readonly SelectProps.Option[];
    crossEncoderModel: SelectProps.Option | null;
    hybridSearch: boolean;
    chunkSize: number;
    chunkOverlap: number;
}

export interface KendraWorkspaceCreateInput {
    name: string;
    kendraIndex: SelectProps.Option | null;
    useAllData: boolean;
}

export interface UserInfo {
    email: string;
    last_name: string;
    first_name: string;
    name: string;
}

export interface ChatRequestQueryKwargsInput {
    streaming: boolean;
    useHistory: boolean;
    followPromptId: string;
    maxTokens: number;
    temperature: number;
    topP: number;
    numDocs: number;
}

export interface ChatRequestQueryDataInput {
    provider: string;
    modelName: string;
    mode: string;
    text: string;
    files: ImageFile[] | null;
    workspaceId?: string;
    promptId?: string;
    sessionId: string;
    modelKwargs: ChatRequestQueryKwargsInput;
    chatId?: string;
}

export interface ChatRequestInput {
    userId: string;
    data: ChatRequestQueryDataInput;
}
