import { Button, SpaceBetween, Box } from '@cloudscape-design/components';
import errorIcon from '../assets/images/error-icon.png';

export function ErrorFallBack({ error, resetErrorBoundary }: any) {
    return (
        <Box textAlign="center">
            <img src={errorIcon} alt="Error" />
            <SpaceBetween size="l">
                <h3>
                    {error.message || 'Something went wrong.Try again later.'}
                </h3>
                <SpaceBetween size="xs" alignItems="center">
                    <Button variant="primary" onClick={resetErrorBoundary}>
                        Refresh Page
                    </Button>
                </SpaceBetween>
            </SpaceBetween>
        </Box>
    );
}
export default ErrorFallBack;
