import * as React from "react";
import { motion } from "framer-motion";
import { ReactNode } from "react";

const variants = {
  open: {
    y: 0,
    opacity: 1,
    transition: {
      y: { stiffness: 1000, velocity: -100 }
    }
  },
  closed: {
    y: 50,
    opacity: 0,
    transition: {
      y: { stiffness: 1000 }
    }
  }
};

interface MenuSectionProps {
  children: ReactNode;
}

export const MenuSection = ({ children }: MenuSectionProps) => (
  <motion.div
    variants={variants}
    style={{ 
      marginBottom: "8px",
      width: "100%",
      position: "relative",
      display: "flex",
      flexDirection: "column",
      alignItems: "center"
    }}
  >
    {children}
  </motion.div>
); 