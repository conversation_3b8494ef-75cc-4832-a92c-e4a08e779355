import * as React from "react";
import { motion } from "framer-motion";
import { ReactNode } from "react";

const variants = {
  open: {
    transition: { staggerChildren: 0.07, delayChildren: 0.2 }
  },
  closed: {
    transition: { staggerChildren: 0.05, staggerDirection: -1 }
  }
};

interface NavigationMenuProps {
  children: ReactNode;
}

export const NavigationMenu = ({ children }: NavigationMenuProps) => (
  <motion.div variants={variants} style={{ width: "100%", height: "100%" }}>
    {children}
  </motion.div>
); 