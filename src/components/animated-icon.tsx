import React from 'react';
import { motion } from 'framer-motion';
import { 
  // Bell/Notification Icons
  BsBell,
  BsBellFill,
  BsBellSlash,
  BsBellSlashFill,
  BsAlarm,
  BsAlarmFill,
  BsStopwatch,
  BsStopwatchFill,
  BsClock,
  BsClockFill,
  BsChatDots,
  BsChatDotsFill,
  BsEnvelope,
  BsEnvelopeFill,
  BsExclamationTriangle,
  BsExclamationTriangleFill,
  BsInfoCircle,
  BsInfoCircleFill,
  
  // Help Icons
  BsQuestionCircle,
  BsQuestionCircleFill,
  BsQuestion,
  BsQuestionSquare,
  BsQuestionSquareFill,
  BsInfoSquare,
  BsInfoSquareFill,
  BsExclamationCircle,
  BsExclamationCircleFill,
  BsLightbulb,
  BsLightbulbFill,
  BsBookmark,
  BsBookmarkFill,
  BsBook,
  BsBookFill,
  
  // User Icons
  BsPersonCircle,
  BsPerson,
  BsPersonFill,
  BsPersonBadge,
  BsPersonBadgeFill,
  BsPersonCheck,
  BsPersonCheckFill,
  BsPersonHeart,
  BsPersonHearts,
  BsPersonVcard,
  BsPersonVcardFill,
  BsPersonWorkspace,
  BsPersonSquare,
  BsPersonRolodex,
  BsPersonPlus,
  BsPersonPlusFill,
  BsPersonGear,
  BsPersonFillGear,
  BsPersonExclamation,
  BsPersonDash,
  BsPersonDashFill,
  BsPersonBoundingBox,
  BsPersonAdd,
  BsPersonX,
  BsPersonXFill,
  BsPersonUp,
  BsPersonStanding,
  BsPersonWalking,
  BsPersonArmsUp,
  BsPersonRaisedHand
} from 'react-icons/bs';

// 图标类型映射
const ICON_MAP = {
  // Bell/Notification Icons
  bell: BsBell,
  bellFill: BsBellFill,
  bellSlash: BsBellSlash,
  bellSlashFill: BsBellSlashFill,
  alarm: BsAlarm,
  alarmFill: BsAlarmFill,
  stopwatch: BsStopwatch,
  stopwatchFill: BsStopwatchFill,
  clock: BsClock,
  clockFill: BsClockFill,
  chat: BsChatDots,
  chatFill: BsChatDotsFill,
  envelope: BsEnvelope,
  envelopeFill: BsEnvelopeFill,
  warning: BsExclamationTriangle,
  warningFill: BsExclamationTriangleFill,
  info: BsInfoCircle,
  infoFill: BsInfoCircleFill,
  
  // Help Icons
  questionCircle: BsQuestionCircle,
  questionCircleFill: BsQuestionCircleFill,
  question: BsQuestion,
  questionSquare: BsQuestionSquare,
  questionSquareFill: BsQuestionSquareFill,
  infoSquare: BsInfoSquare,
  infoSquareFill: BsInfoSquareFill,
  exclamationCircle: BsExclamationCircle,
  exclamationCircleFill: BsExclamationCircleFill,
  lightbulb: BsLightbulb,
  lightbulbFill: BsLightbulbFill,
  bookmark: BsBookmark,
  bookmarkFill: BsBookmarkFill,
  book: BsBook,
  bookFill: BsBookFill,
  
  // User Icons
  personCircle: BsPersonCircle,
  person: BsPerson,
  personFill: BsPersonFill,
  personBadge: BsPersonBadge,
  personBadgeFill: BsPersonBadgeFill,
  personCheck: BsPersonCheck,
  personCheckFill: BsPersonCheckFill,
  personHeart: BsPersonHeart,
  personHearts: BsPersonHearts,
  personVcard: BsPersonVcard,
  personVcardFill: BsPersonVcardFill,
  personWorkspace: BsPersonWorkspace,
  personSquare: BsPersonSquare,
  personRolodex: BsPersonRolodex,
  personPlus: BsPersonPlus,
  personPlusFill: BsPersonPlusFill,
  personGear: BsPersonGear,
  personFillGear: BsPersonFillGear,
  personExclamation: BsPersonExclamation,
  personDash: BsPersonDash,
  personDashFill: BsPersonDashFill,
  personBoundingBox: BsPersonBoundingBox,
  personAdd: BsPersonAdd,
  personX: BsPersonX,
  personXFill: BsPersonXFill,
  personUp: BsPersonUp,
  personStanding: BsPersonStanding,
  personWalking: BsPersonWalking,
  personArmsUp: BsPersonArmsUp,
  personRaisedHand: BsPersonRaisedHand
} as const;

type IconType = keyof typeof ICON_MAP;

interface AnimatedIconProps {
  size?: number;
  color?: string;
  className?: string;
  animationType?: 'ring' | 'pulse' | 'rotate' | 'bounce' | 'heartbeat' | 'glow' | 'wiggle' | 'float';
  iconType: IconType;
}

const AnimatedIcon: React.FC<AnimatedIconProps> = ({ 
  size = 20, 
  color = '#666666',
  className = '',
  animationType = 'pulse',
  iconType
}) => {
  
  // 动画定义
  const animations = {
    // 摇摆动画 (铃铛效果)
    ring: {
      initial: { rotate: 0 },
      animate: { 
        rotate: [0, -10, 10, -10, 10, -5, 5, 0],
        transition: {
          duration: 1.5,
          repeat: Infinity,
          repeatDelay: 3,
          ease: "easeInOut"
        }
      }
    },
    
    // 脉冲动画
    pulse: {
      initial: { scale: 1, opacity: 0.8 },
      animate: { 
        scale: [1, 1.15, 1],
        opacity: [0.8, 1, 0.8],
        transition: {
          duration: 2,
          repeat: Infinity,
          repeatDelay: 2,
          ease: "easeInOut"
        }
      }
    },
    
    // 旋转动画
    rotate: {
      initial: { rotate: 0 },
      animate: { 
        rotate: [0, 360],
        transition: {
          duration: 8,
          repeat: Infinity,
          ease: "linear"
        }
      }
    },
    
    // 弹跳动画
    bounce: {
      initial: { y: 0 },
      animate: { 
        y: [0, -4, 0],
        transition: {
          duration: 1,
          repeat: Infinity,
          repeatDelay: 2,
          ease: "easeOut"
        }
      }
    },
    
    // 心跳动画
    heartbeat: {
      initial: { scale: 1 },
      animate: { 
        scale: [1, 1.15, 1, 1.15, 1],
        transition: {
          duration: 1.5,
          repeat: Infinity,
          repeatDelay: 2,
          ease: "easeInOut"
        }
      }
    },
    
    // 发光动画
    glow: {
      initial: { filter: 'drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1))' },
      animate: { 
        filter: [
          'drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1))',
          'drop-shadow(0 0 8px rgba(255, 255, 255, 0.6))',
          'drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1))'
        ],
        transition: {
          duration: 2.5,
          repeat: Infinity,
          repeatDelay: 1.5,
          ease: "easeInOut"
        }
      }
    },
    
    // 摇摆动画
    wiggle: {
      initial: { rotate: 0 },
      animate: { 
        rotate: [0, -5, 5, -5, 5, 0],
        transition: {
          duration: 1,
          repeat: Infinity,
          repeatDelay: 3,
          ease: "easeInOut"
        }
      }
    },
    
    // 浮动动画
    float: {
      initial: { y: 0 },
      animate: { 
        y: [0, -2, 0],
        transition: {
          duration: 3,
          repeat: Infinity,
          repeatDelay: 1,
          ease: "easeInOut"
        }
      }
    }
  };

  const IconComponent = ICON_MAP[iconType];
  
  if (!IconComponent) {
    console.warn(`Icon type "${iconType}" not found`);
    return null;
  }

  return (
    <motion.div
      className={className}
      initial="initial"
      animate="animate"
      variants={animations[animationType]}
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        transformOrigin: animationType === 'ring' ? 'top center' : 'center',
        marginLeft: className.includes('user-icon-right') ? '8px' : '0px'
      }}
    >
      <IconComponent 
        size={size} 
        color={color}
        style={{ 
          filter: animationType !== 'glow' ? 'drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1))' : undefined 
        }}
      />
    </motion.div>
  );
};

export default AnimatedIcon;
export type { IconType }; 