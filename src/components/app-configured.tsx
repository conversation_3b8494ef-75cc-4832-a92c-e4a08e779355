import { useEffect, useState } from "react";
import {
  ThemeProvider,
  defaultDarkModeOverride,
  useTheme,
} from "@aws-amplify/ui-react";
import App from "../app";

import { AppConfig } from "../common/types";
import { AppContext } from "../common/app-context";
import { Al<PERSON>, StatusIndicator } from "@cloudscape-design/components";
import { StorageHelper } from "../common/helpers/storage-helper";
import { Mode } from "@cloudscape-design/global-styles";
import "@aws-amplify/ui-react/styles.css";
import { Env } from "@semoss/sdk";
import { InsightProvider } from "@semoss/sdk-react";
import { Worker } from "@react-pdf-viewer/core";

// if (process.env.NODE_ENV !== "production") {
//   Env.update({
//     MODULE: "/Monolith",
//     ACCESS_KEY: "f9ea7cec-a78a-4860-acce-23a8f3cb7071",
//     SECRET_KEY: "e72ad77f-1dea-466a-abca-2f3911774364",
//     ENDPOINT: "https://workshop.cfg.deloitte.com/cfg-ai-demo",
//   });
// }

export default function AppConfigured() {
  const { tokens } = useTheme();
  const [config, setConfig] = useState<AppConfig | null>(null);
  const [error, setError] = useState<boolean | null>(null);
  const [theme, setTheme] = useState(StorageHelper.getTheme());

  useEffect(() => {
    (async () => {
      try {
        // const result = await fetch("/aws-exports.json");
        // const awsExports = await result.json();
        // const currentConfig = Amplify.configure(awsExports) as AppConfig | null;
        // if (currentConfig?.config.auth_federated_provider?.auto_redirect) {
        //   let authenticated = false;
        //   try {
        //     const user = await Auth.currentAuthenticatedUser();
        //     if (user) {
        //       authenticated = true;
        //     }
        //   } catch (e) {
        //     authenticated = false;
        //   }
        //   if (!authenticated) {
        //     const federatedProvider =
        //       currentConfig.config.auth_federated_provider;
        //     if (!federatedProvider.custom) {
        //       Auth.federatedSignIn({ provider: federatedProvider.name });
        //     } else {
        //       Auth.federatedSignIn({ customProvider: federatedProvider.name });
        //     }
        //     return;
        //   }
        // }
        // setConfig(currentConfig);
      } catch (e) {
        console.error(e);
        setError(true);
      }
    })();
  }, []);

  useEffect(() => {
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (
          mutation.type === "attributes" &&
          mutation.attributeName === "style"
        ) {
          const newValue =
            document.documentElement.style.getPropertyValue(
              "--app-color-scheme"
            );

          const mode = newValue === "dark" ? Mode.Dark : Mode.Light;
          if (mode !== theme) {
            setTheme(mode);
          }
        }
      });
    });

    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ["style"],
    });

    return () => {
      observer.disconnect();
    };
  }, [theme]);

  // if (!config) {
  //   if (error) {
  //     return (
  //       <div
  //         style={{
  //           height: "100%",
  //           width: "100%",
  //           display: "flex",
  //           justifyContent: "center",
  //           alignItems: "center",
  //         }}
  //       >
  //         <Alert header="Configuration error" type="error">
  //           Error loading configuration from "
  //           <a href="/aws-exports.json" style={{ fontWeight: "600" }}>
  //             /aws-exports.json
  //           </a>
  //           "
  //         </Alert>
  //       </div>
  //     );
  //   }

  //   return (
  //     <div
  //       style={{
  //         width: "100%",
  //         height: "100%",
  //         display: "flex",
  //         justifyContent: "center",
  //         alignItems: "center",
  //       }}
  //     >
  //       <StatusIndicator type="loading">Loading</StatusIndicator>
  //     </div>
  //   );
  // }

  return (
    <AppContext.Provider value={config}>
      <InsightProvider>
        <ThemeProvider
          theme={{
            name: "default-theme",
            overrides: [defaultDarkModeOverride],
          }}
          colorMode={theme === Mode.Dark ? "dark" : "light"}
        >
          <Worker workerUrl="https://unpkg.com/pdfjs-dist@3.4.120/build/pdf.worker.min.js">
            <App />
          </Worker>
        </ThemeProvider>
      </InsightProvider>
    </AppContext.Provider>
  );
}
