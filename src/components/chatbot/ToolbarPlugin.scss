/* LexicalEditor.scss */
.lexical-editor-container {
  border: 1px solid #ddd;
  border-radius: 6px;
  margin-bottom: 10px;
  overflow: hidden;
  background-color: #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  width: 100%;
  transition: border-color 0.2s ease;

  &:focus-within {
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
  }
}

.lexical-editor-inner {
  position: relative;
  min-height: 50px;
  max-height: 300px;
  overflow-y: auto;
  padding: 0 10px;
}

.lexical-editor-content-editable {
  outline: none;
  padding: 10px 0;
  min-height: 38px;
  width: 100%;
  resize: none;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  font-size: 14px;
  line-height: 1.5;

  &.expanded {
    min-height: 150px;
  }
}

.lexical-editor-placeholder {
  color: #999;
  pointer-events: none;
  position: absolute;
  top: 10px;
  left: 10px;
  font-size: 14px;
  user-select: none;
}

.lexical-editor-root {
  position: relative;
  width: 100%;
}

/* 格式化样式 */
.lexical-editor-paragraph {
  margin: 0 0 8px 0;
}

.lexical-editor-h1 {
  font-size: 24px;
  font-weight: bold;
  margin: 16px 0 8px 0;
}

.lexical-editor-h2 {
  font-size: 20px;
  font-weight: bold;
  margin: 14px 0 8px 0;
}

.lexical-editor-text-bold {
  font-weight: bold;
}

.lexical-editor-text-italic {
  font-style: italic;
}

.lexical-editor-text-underline {
  text-decoration: underline;
}

.lexical-editor-text-code {
  background-color: #f5f5f5;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: monospace;
}

.lexical-editor-list-ul {
  padding-left: 20px;
  list-style-type: disc;
}

.lexical-editor-list-ol {
  padding-left: 20px;
  list-style-type: decimal;
}

.lexical-editor-list-item {
  margin: 4px 0;
}

.lexical-editor-link {
  color: #007bff;
  text-decoration: underline;
  cursor: pointer;
}

/* ToolbarPlugin.scss */
.toolbar {
  display: flex;
  border-bottom: 1px solid #eee;
  padding: 5px 10px;
  background-color: #f8f9fa;
  flex-wrap: wrap;
}

.toolbar button {
  border: none;
  background: none;
  margin: 0 3px;
  padding: 5px 8px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;

  &:hover {
    background-color: #e9ecef;
  }

  &.active {
    background-color: #e2e6ea;
    color: #007bff;
  }
}

.format-icon {
  font-style: normal;
  display: inline-block;
  font-weight: bold;
  font-size: 14px;
  min-width: 14px;
}

.bold-icon {
  font-weight: bold;
}

.italic-icon {
  font-style: italic;
}

.underline-icon {
  text-decoration: underline;
}

.divider {
  width: 1px;
  background-color: #ddd;
  margin: 0 8px;
  height: 20px;
  align-self: center;
}