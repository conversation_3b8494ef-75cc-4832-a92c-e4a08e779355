import {
    <PERSON>,
    SpaceBet<PERSON>en,
    <PERSON>,
    Pa<PERSON>ation,
    <PERSON>ton,
    TableProps,
  } from "@cloudscape-design/components";
  import { DateTime } from "luxon";
  import { useState, useEffect, useContext, useCallback } from "react";
  import { Link } from "react-router-dom";
  import { v4 as uuidv4 } from "uuid";
  import { useCollection } from "@cloudscape-design/collection-hooks";
  import RouterButton from "../wrappers/router-button";
  import { Session } from "../../API";
  import sessionService from "../../services/session.service";
  import { useOktaAuth } from "@okta/okta-react";
  import { useSelector } from "react-redux";
  import { RootState } from "@/redux/store/configureStore";
  import styles from "../../styles/chat.module.scss";
import ElegantPagination from "./elegantPagination";
  
  export interface SessionsProps {
    toolsOpen: boolean;
    resetBgColor: boolean
  }
  
  export default function ChatHistory(props: SessionsProps) {
    const [selectedItem, setSelectedItem] = useState(null)
    const [sessions, setSessions] = useState<Session[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const dataState = useSelector((state: any) => state?.rootReducer);
    const isFeedbackOpened = dataState?.chatReducer?.isFeedbackOpened;
    const userEmail = dataState?.chatReducer?.userEmail;
    const userRoles = dataState?.chatReducer?.userRoles;
    // const { authState, oktaAuth } = useOktaAuth();
  
    const { items, collectionProps, paginationProps } = useCollection(sessions, {
      filtering: {
        empty: (
          <Box margin={{ vertical: "xs" }} textAlign="center" color="inherit">
            <SpaceBetween size="m">
              <b>No sessions</b>
            </SpaceBetween>
          </Box>
        ),
      },
      pagination: { pageSize: 5 },
      sorting: {
        defaultState: {
          sortingColumn: {
            sortingField: "startTime",
          },
          isDescending: true,
        },
      },
      selection: {},
    });
  
    /**
     * Get all sessions information for this user
     */
    const getSessions = useCallback(async () => {
      try {
        // await oktaAuth.getUser().then(async (user: any) => {
          
        const userId: string = userEmail;
          // const userId: string = "<EMAIL>";
          if (userId !== "") {
            const result = await sessionService.listSessions(userId);
            setSessions(result!);
          } else {
            console.log("Error: User id for Session is not found");
          }
        // });
      } catch (e) {
        console.log(e);
        setSessions([]);
      }
    }, []);
  
    /**
     * Get all sessions information for this user
     */
    useEffect(() => {
      if (!props.toolsOpen || !userEmail) return;
  
      const loadSessions = async () => {
        setIsLoading(true);
        try {
          const result = await sessionService.listSessions(userEmail);
          setSessions(result || []);
        } catch (e) {
          console.error(e);
          setSessions([]);
        }
        setIsLoading(false);
      };
  
      loadSessions();
    }, [props.toolsOpen, userEmail]);

  
    /**
     *
     */
    useEffect(() => {
      if (!props.toolsOpen) return;
  
      (async () => {
        setIsLoading(true);
        await getSessions();
        setIsLoading(false);
      })();
    }, [getSessions, props.toolsOpen]);

    const handleItemClick = (id) => {
        setSelectedItem(id)
    }
 /** Reset the chat history bg color on 'New Chat' click. */
    useEffect(() =>{
        if(props.resetBgColor){
            setSelectedItem(null)
        }
    },[props.resetBgColor])
  
    let chatHistoryContainerSize;
    if (userRoles?.includes("OWNER") || userRoles.includes("EDIT")) {
      chatHistoryContainerSize = 'calc(100vh - 475px)'
    }else{
      chatHistoryContainerSize = 'auto'
    }
    /**
     * Render the component
     */
    return (
      <div style={{display:'flex', flexDirection:'column',paddingRight:'24px',marginLeft:'15px', height:chatHistoryContainerSize,flexGrow:'1',overflowY:'auto', overflowX:'hidden'}}>
        {items.map((item, idx) =>
          (
              <div key={idx} className={`${styles.item_container} ${selectedItem === item.id ? styles.selected_item : ''}`}
              onClick={() => handleItemClick(item.id)}
              >
              <Link to={`/chatbot/playground/${item.id}`} style={{textDecoration:'none', zIndex:'0'}}>
                <h4 className={styles.chat_history_title}>
                  {item.title}
                </h4>
                <p className={styles.chat_history_date}>{DateTime.fromISO(
                      new Date(item.startTime).toISOString()
                    ).toLocaleString(DateTime.DATETIME_SHORT)}
                </p>
                </Link>
                </div>
          )
        )}
        <ElegantPagination {...paginationProps} />
      </div>
    );
}