// very original
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  ColumnLayout,
  Container,
  FlashbarProps,
  FormField,
  Icon,
  ProgressBar,
  ProgressBarProps,
  Select,
  SelectProps,
  SpaceBetween,
  Spinner,
  StatusIndicator,
  Tabs,
} from "@cloudscape-design/components";
import {
  Dispatch,
  SetStateAction,
  useContext,
  useEffect,
  useLayoutEffect,
  useRef,
  useState,
} from "react";
import { useNavigate, useLocation } from "react-router-dom";
import SpeechRecognition, {
  useSpeechRecognition,
} from "react-speech-recognition";
import TextareaAutosize from "react-textarea-autosize";
import { ReadyState } from "react-use-websocket";
import { AppContext } from "../../common/app-context";
import { OptionsHelper } from "../../common/helpers/options-helper";
import { StorageHelper } from "../../common/helpers/storage-helper";
import { Model, Workspace } from "../../API";
import { OpenSearchWorkspaceCreateInput, UserInfo } from "../../common/types";
import styles from "../../styles/chat.module.scss";
import ConfigDialog from "./config-dialog";
import ImageDialog from "./image-dialog";
import {
  ChabotInputModality,
  ChatBotAction,
  ChatBotConfiguration,
  ChatBotHistoryItem,
  ChatBotMessageType,
  ChatBotMode,
  ChatInputState,
  ImageFile,
} from "./types";
import {
  getCFGModels,
  getCFGWorkspaces,
  getSelectedModelMetadata,
  getSignedUrl,
  updateMessageHistoryRef,

} from "./utils";
import { Utils } from "../../common/utils";
import { ChatRequestInput, LoadingStatus } from "../../common/types";
import * as _ from "lodash";
import modelService from "../../services/model.service";
import workspaceService from "../../services/workspace.service";
import promptService from "../../services/prompt.service";
import { PromptsHelper } from "../../common/helpers/prompts-helper";
import { ModelsHelper } from "../../common/helpers/models-helper";
import chatService from "../../services/chat.service";
import { useDispatch, useSelector } from "react-redux";
import { v4 as uuidv4 } from "uuid";
// import { useOktaAuth } from "@okta/okta-react";
import { chatActions } from "../../pages/chatbot/playground/chat.slice";
import { BEDROCKENABLED, CFGAIENABLED,CFGAIWPENABLED,BEDROCKWPENABLED } from "./../../../config";
import { ModelPromptFilter } from "./model-prompt-filter";
import activeSettingsIcon from "../../assets/images/settingsActive.png"
import inactiveSettingsIcon from "../../assets/images/settingsInactive.png"
import suggestionDeleteIcon from "../../assets/images/X-Icon.png"
import adminSettingsIcon from "../../assets/images/Info-Icon.png"
import PDFUploadButton from "../pdf-upload-button";
import { EmbeddingsModelHelper } from "@/common/helpers/embeddings-model-helper";
import embeddingService from "@/services/embedding.service";
import crossEncodersService from "@/services/crossEncoders.service";
import documentService from "@/services/document.service";
import { FileUploader } from "@/common/file-uploader";
import { Labels } from "@/common/constants";
import { SET_EDITOR_CONTENT_COMMAND } from './lexical-editor';
//① highlight and bold partyA/B
import LexicalRichTextEditor, {createFormattedLexicalState} from "./lexical-editor";
import { suggestedPrompts } from './suggestion-prompts.ts';
import { duaPromptTemplate, partyOptions } from './formfield-options';
import PromptSuggestions, {MultiplePromptSuggestions} from "./prompt-suggestions";
import ModelNotification from './model-info-popover';
import * as React from "react";
// import { useMCP } from "../../contexts/mcp-context";
import { useMCPHandler } from './mcp-handler';
import { useMCP } from "../../contexts/mcp-context";

// 添加新的CSS样式到现有的样式表中
const additionalStyles = `
.input_textarea {
  resize: none;
  outline: none;
  width: 100%;
  border: none;
  padding: 10px;
  font-size: 14px;
  line-height: 1.5;
  overflow: hidden; /* 隐藏滚动条 */
  transition: height 0.3s ease; /* 平滑高度变化，增加过渡时间 */
  min-height: 38px; /* 设置最小高度 */
}

/* 针对长文本时的样式 */
.input_textarea.expanded {
  min-height: 150px; /* 为长文本设置最小高度 */
}

/* 防止聚焦时高度变化 */
.input_textarea:focus {
  height: auto !important;
  min-height: 38px;
}

/* 确保容器可以适应扩展的高度 */
.input_textarea_container {
  display: flex;
  width: 100%;
  transition: height 0.3s ease; /* 平滑高度变化 */
}
`;

// 将新样式添加到document中
const addStyles = () => {
  const styleEl = document.createElement('style');
  styleEl.textContent = additionalStyles;
  document.head.appendChild(styleEl);
  return () => {
    document.head.removeChild(styleEl);
  };
};

export interface ChatInputPanelProps {
  running: boolean;
  setRunning: Dispatch<SetStateAction<boolean>>;
  session: { id: string; loading: boolean };
  messageHistory: ChatBotHistoryItem[];
  setMessageHistory: (history: ChatBotHistoryItem[]) => void;
  configuration: ChatBotConfiguration;
  setConfiguration: Dispatch<React.SetStateAction<ChatBotConfiguration>>;
  closeNotification: (isError: boolean) => void;
  setErrorMessage: (message: string) => void;
  setError: (error: boolean) => void;
  chatId: string;
  onFileUploadStatusChange?: (isUploaded: boolean) => void
}

export interface UploadedFile {
  bucketName?: string;
  fileKey?: string;
  fileType?: string;
}

export abstract class ChatScrollState {
  static userHasScrolled = false;
  static skipNextScrollEvent = false;
  static skipNextHistoryUpdate = false;
}

let workspaceDefaultOptions: SelectProps.Option[] = [
  {
    label: "Select",
    value: "",
    iconName: "close",
  },
];

const promptDefaultOptions: SelectProps.Option[] = [
  {
    label: "No Prompt",
    value: "",
    iconName: "close",
  },
];

// Document builder options
const documentBuilderOptions: SelectProps.Option[] = [
  {
    label: "Select",
    value: "",
    iconName: "close",
  },
  {
    label: "DUA",
    value: "dua",
    iconName: "file",
  },
];

// Party1 options
const party1Options: SelectProps.Option[] = [
  { ...partyOptions[0], label: "Select Party 1" },
  ...partyOptions.slice(1)
];

// Party2 options
const party2Options: SelectProps.Option[] = [
  { ...partyOptions[0], label: "Select Party 2" },
  ...partyOptions.slice(1)
];

// Format date function for MOU
const formatDate = (date: Date): string => {
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

export default function ChatInputPanel(props: ChatInputPanelProps) {
  // 添加新样式
  useEffect(() => {
    return addStyles();
  }, []);

  const appContext = useContext(AppContext);
  const [prompts, setPrompts] = useState([]);

  const dispatch = useDispatch();
  const location = useLocation()
  const {onFileUploadStatusChange} = props;
  const dataState = useSelector((state: any) => state?.rootReducer);
  const isFeedbackOpened = dataState?.chatReducer?.isFeedbackOpened;
  const userEmail = dataState?.chatReducer?.userEmail;
  const userName = dataState?.chatReducer?.userName;
  const tempWorkspaceId = dataState?.chatReducer?.tempWorkspaceId
  const [useTempWorkspace, setUseTempWorkspace] = useState<boolean>(false);
  const navigate = useNavigate();
  const { transcript, listening, browserSupportsSpeechRecognition } =
      useSpeechRecognition();
  const [state, setState] = useState<ChatInputState>({
    value: "",
    selectedModel: null,
    selectedModelMetadata: null,
    selectedWorkspace: workspaceDefaultOptions[0],
    selectedPrompt: promptDefaultOptions[0],
    modelsStatus: "loading",
    workspacesStatus: "loading",
    promptsStatus: "loading",
  });

  // New state for document builder
  const [selectedDocumentBuilder, setSelectedDocumentBuilder] = useState<SelectProps.Option>(documentBuilderOptions[0]);
  const [selectedParty1, setSelectedParty1] = useState<SelectProps.Option>(party1Options[0]);
  const [selectedParty2, setSelectedParty2] = useState<SelectProps.Option>(party2Options[0]);

  const [configDialogVisible, setConfigDialogVisible] = useState(false);
  const [documentRepoDialogVisible, setDocumentRepoDialogVisible] = useState(true);
  const [promptDialogVisible, setPromptDialogVisible] = useState(false);
  const [imageDialogVisible, setImageDialogVisible] = useState(false);
  const [files, setFiles] = useState<ImageFile[]>([]);
  const [uploadFiles, setUploadFiles] = useState<{bucketName: string; fileKey: string; fileType: string;}[]>([]);
  const [timeTaken, setTimeTaken] = useState<number>(0);
  const [outputData, setOutputData] = useState<Record<string, any>>();
  const [documentListVisible, setDocumentListVisible] = useState(false)
  const [readyState, setReadyState] = useState<ReadyState>(
      ReadyState.UNINSTANTIATED
  );

  const embedmodel_name = EmbeddingsModelHelper.getSelectOption(
      appContext?.config.default_embeddings_model,
  )

  const [documentStatus, setDocumentStatus] = useState<string>('');
  const [isCheckingStatus, setIsCheckingStatus] = useState<boolean>(false);
  const [uploadingStatus, setUploadingStatus] = useState<FlashbarProps.Type>('info');
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [uploadedPDF, setUploadedPDF] = useState(null);
  const [validationErrorMessage, setValidationErrorMessage] = useState<string | null>(null);
  const [failedDocuments, setFailedDocuments] = useState([]);

  // *********multiple selections**********
  const [multiplePromptValue, setMultiplePromptValue] = useState("");
  const [onlyDefaultOptionSelected, setOnlyDefaultOptionSelected] = useState(true);

  const containerRef = React.useRef(null);
  const [topOffset, setTopOffset] = React.useState(0);

  // Add a state to trigger reset of MultiplePromptSuggestions
  const [resetMultiplePrompts, setResetMultiplePrompts] = useState(false);

  // ②修复同时发送suggestion 和 multi prompt. prompt 类别 state
  const [activePromptSystem, setActivePromptSystem] = useState('none');

  // ②修复同时发送suggestion 和 multi prompt. 监听 MultiplePromptSuggestions 中的 prompt type 事件
  // 当 applyChanges 被调用的时候，prompt type 就是 multiple
  useEffect(() => {
    const handleSetActivePromptSystem = (event) => {
      setActivePromptSystem(event.detail.system);

      // 如果切换到多选系统，清空建议提示
      if (event.detail.system === 'multiple') {
        setSuggestedPrompt('');
      }
    };

    window.addEventListener('set-active-prompt-system', handleSetActivePromptSystem);
    return () => {
      window.removeEventListener('set-active-prompt-system', handleSetActivePromptSystem);
    };
  }, []);

  //Get the extension out of uploaded doc name
  function getFileExtension(fileName) {
    return fileName.slice((fileName.lastIndexOf(".") - 1 >>> 0) + 2);
  }

  const handlePDFUpload = async (files: File[]) => {
    const failedUploads = [];
    if (!files || files.length === 0) return;

    setUploadingStatus('in-progress');
    setDocumentStatus('processing');
    setUploadProgress(0);

    const validFiles = files.filter(file => {
      const fileExtension = getFileExtension(file.name).toLowerCase();
      if (fileExtension === 'pdf' || fileExtension === 'docx') {
        return true;
      } else {
        failedUploads.push(`${file.name}`);
        return false;
      }
    });

    const invalidFiles = files.filter(file => !validFiles.includes(file));

    if (validFiles.length === 0) {
      console.log('No valid files to upload');
      setUploadingStatus('error');
      setDocumentStatus('wrongexttype');
      setFailedDocuments(failedUploads)
      return;
    }

    const uploadPromises = validFiles.map(async (file) => {
      try {
        // Step 1: Get pre-signed URL
        const uploadPayload = {
          fileKey: file.name.toLowerCase(),
          fileType: file.type,
          bucketName: "cderone-preprod-cfgai-irpolicybot-demo-qa-upload",
        };
        const presignedUrl = await documentService.presignedFileUploadPost(uploadPayload);

        // Step 2: Upload file to S3 using pre-signed URL
        const blob = file.slice(0, file.size, "");
        const result = await fetch(presignedUrl, {
          method: "PUT",
          body: blob,
        });

        if (result.ok) {
          console.log(`${uploadPayload.fileKey} uploaded to S3 successfully.`);
          return {
            ...file,
            bucketName: uploadPayload.bucketName,
            fileKey: `multimodal/${userEmail}/${uploadPayload.fileKey}`,
            fileType: getFileExtension(uploadPayload.fileKey),
          };
        } else {
          throw new Error(`Failed to upload ${file.name}`);
        }
      } catch (error) {
        console.error(`Error uploading ${file.name}:`, error.message);
        failedUploads.push(file.name)
        return null;
      }
    });

    try {
      const results = await Promise.all(uploadPromises);
      const successfulUploads = results.filter(Boolean);
      setFailedDocuments(failedUploads)
      if (successfulUploads.length > 0) {
        // Validate context length
        try {
          const validateResult = await documentService.validateContextLength(successfulUploads, state.selectedModel.label);
          if (validateResult.message === 'Upload successful') {
            setUploadFiles([...uploadFiles, ...successfulUploads]);
            setDocumentStatus('processed');
            console.log(`Validated ${successfulUploads.length} file(s) successfully.`);
            onFileUploadStatusChange(true)
          } else {
            setUploadingStatus('error');
            setDocumentStatus('failed');
            setValidationErrorMessage(validateResult.errorMessage || 'Context length validation failed');
            console.log('Context length validation failed');
            return;
          }
        } catch (error) {
          console.error('Error validating context length:', error.response.data.detail);
          setUploadingStatus('error');
          setDocumentStatus('failed');
          setValidationErrorMessage(error.response.data.detail);
          return;
        }
      }

      if (successfulUploads.length < validFiles.length || invalidFiles.length > 0) {
        setUploadingStatus('error');
        console.log(`Failed to upload ${validFiles.length - successfulUploads.length} file(s)`);
        console.log(`${invalidFiles.length} invalid file(s) were not uploaded`);
      } else {
        setUploadingStatus('success');
      }

      if (successfulUploads.length === 0) {
        setDocumentStatus('failed');
      }

    } catch (error) {
      console.error('Error during file uploads:', error.message);
      setUploadingStatus('error');
      setValidationErrorMessage('Error during file uploads');
      props.setError(true);
    }
  };

  useEffect(() => {
    if (documentStatus === 'failed' || uploadingStatus === 'error') {
      const timer = setTimeout(() => {
        setDocumentStatus('processed');
        setUploadingStatus('info');
      }, 10000);

      return () => clearTimeout(timer);
    }
  }, [documentStatus, uploadingStatus]);

  //Hide document list popup if clicked anywhere on the screen
  useEffect(() => {
    function handleClickOutside(event) {
      if (
          alertContainerRef.current &&
          !alertContainerRef.current.contains(event.target) &&
          onClickAreaRef.current &&
          !onClickAreaRef.current.contains(event.target)
      ) {
        setDocumentListVisible(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const clearUploadedPDFs = () => {
    setUploadedPDF([]);
    setUploadingStatus('info');
    setUploadProgress(0);
  };

  const getUploadStatus = (): ProgressBarProps.Status => {
    if (uploadingStatus === 'error') return 'error';
    if (uploadingStatus === 'success') return 'success';
    return 'in-progress';
  };

  const [modelsStatus, setModelsStatus] = useState<LoadingStatus>("loading");
  const [workspacesStatus, setWorkspacesStatus] =
      useState<LoadingStatus>("loading");
  const [promptsStatus, setPromptsStatus] = useState<LoadingStatus>("loading");
  const [suggestedPrompt, setSuggestedPrompt] = useState('')
  const [cfgModels, setCFGModels] = useState<Model[]>([]);
  const [cfgWorkspace, setCFGWorkSpaces] = useState<Workspace[]>([]);
  const [limit, setLimit] = useState<number>(3);
  const [questionContext, setQuestionContext] = useState<string>(
      `Answer the user's question truthfully using the context only. Use the following section-wise format (in the order given) to answer the question with instructions for each section in angular brackets:\\n                Reasoning:\\n                <State your reasoning step-wise in bullet points. Below each bullet point mention the source of this information as 'Given in the question' if the bullet point contains information provided in the question, OR as 'Document Name, Page Number' if the bullet point contains information that is present in the context provided above.>\\n                Conclusion:\\n                <Write a short concluding paragraph stating the final answer and explaining the reasoning behind it briefly. State caveats and exceptions to your answer if any.>\\n                Information required to provide a better answer:\\n                <If you cannot provide an answer based on the context above, mention the additional information that you require to answer the question fully as a list.>Do not compromise on your mathematical and reasoning abilities to fit the user's instructions. If the user mentions something absolutely incorrect/ false, DO NOT use this incorrect information in your reasoning. Also, please correct the user gently.
    `
  );
  // const { authState, oktaAuth } = useOktaAuth();
  const [userInfo, setUserInfo] = useState<UserInfo>();
  const [errorMessageForPrompt, setErrorMessageForPrompt] =
      useState<string>("");
  // const [userRoles, setUserRoles] = useState<string[]>([]);

  const messageHistoryRef = useRef<ChatBotHistoryItem[]>([]);
  const userRoles = dataState?.chatReducer?.userRoles;
  const inputRef = useRef(null)
  const alertContainerRef = useRef(null);
  const onClickAreaRef = useRef(null);
  /**
   * Updates the message history ref when the message history prop changes
   */
  useEffect(() => {
    messageHistoryRef.current = props.messageHistory;
    if(props.messageHistory.length > 0 && props.messageHistory?.[props.messageHistory.length-1].metadata.files){
      setUploadFiles(props.messageHistory?.[props.messageHistory.length-1].metadata?.files.map((file: UploadedFile) => ({
        bucketName: file.bucketName,
        fileKey: file.fileKey,
        fileType: file.fileType
      })))
    }
    else{
      setUploadFiles([])
    }
  }, [props.messageHistory]);

  /**
   * Fetches the user information from Okta
   */
  // useEffect(() => {
  //   (async () => {
  //     if (!authState?.isAuthenticated) {
  //       // setUserInfo(null);
  //     } else {
  //       const user: any = await oktaAuth.getUser();
  //       // if(!user.sub) {
  //       //     throw new Error("Sub Claim not found");
  //       // }
  //       if (!user.roles) {
  //         throw new Error("User Roles not found");
  //       }
  //       console.log(user.roles);
  //       dispatch(chatActions?.handleUserRoles(user?.roles));
  //       // setUserRoles(user.roles || []);
  //       const loggedUser = {
  //         email: user.email,
  //         name: user.name,
  //         first_name: user.given_name,
  //         last_name: user.family_name,
  //       };
  //       setUserInfo(loggedUser);
  //     }
  //   })();
  // }, []);

  /**
   * Updates the transcript in the input field when the user speaks
   */
  useEffect(() => {
    if (transcript) {
      setState((state) => ({ ...state, value: transcript }));
    }
  }, [transcript]);

  /**
   * Fetches the models, workspaces and prompts
   */
  useEffect(() => {
    // if (!appContext) return;
    setReadyState(ReadyState.OPEN);
    (async () => {
      let aws_models: Model[] = [];
      let cfg_models: Model[] = [];
      let models: Model[] = [];

      let aws_workspaces: Workspace[] = [];
      let cfg_workspaces: Workspace[] = [];
      let workspaces: Workspace[] = [];

      let prompts: any[] = [];
      try {
        const fetchedPrompts = await promptService.getPrompts();
        setPrompts(fetchedPrompts); // Save to state directly
        let defaultPrompt = prompts.filter((p) => p.name === 'rag_prompt')
        if (BEDROCKENABLED) {
          aws_models = await modelService.getModels();

        }
        if(BEDROCKWPENABLED)
          aws_workspaces = await workspaceService.getWorkspaces();

        if (CFGAIENABLED) {
          cfg_models = await getCFGModels();

          setCFGModels(cfg_models);
          if(CFGAIWPENABLED){
            cfg_workspaces = await getCFGWorkspaces();
            setCFGWorkSpaces(cfg_workspaces);
          }

        }

        models = aws_models.concat(cfg_models);
        workspaces = aws_workspaces.concat(cfg_workspaces);

        // if (!userRoles.includes("FDA PolicyBot Admins")) {
        // workspaces = workspaces.filter(
        //   (workspace) => workspace.name === "Pharmaceutical_Quality"
        // );
        // }

        if (userRoles.includes("FDA PolicyBot Admins")) {
          workspaceDefaultOptions = [
            {
              label: "No workspace (RAG data source)",
              value: "",
              iconName: "close",
            },
            {
              label: "Create new workspace",
              value: "__create__",
              iconName: "add-plus",
            },
          ];
        }

        const selectedModelOption = ModelsHelper.getSelectedModelOption(models);

        const selectedModelMetadata = getSelectedModelMetadata(
            models,
            selectedModelOption
        );

        const selectedWorkspaceOption = appContext?.config.rag_enabled
            ? getSelectedWorkspaceOption(workspaces)
            : workspaceDefaultOptions[0];

        const selectedPromptOption =
            PromptsHelper.getSelectedPromptOption(defaultPrompt);

        setState((state) => ({
          ...state,
          models,
          workspaces,
          prompts,
          selectedModel: selectedModelOption,
          selectedModelMetadata,
          selectedWorkspace: selectedWorkspaceOption,
          selectedPrompt: selectedPromptOption,
          modelsStatus: "finished",
          workspacesStatus: "finished",
          promptsStatus: "finished",
        }));
        // setState((state) => ({
        //   ...state,
        //   models: models,
        // }));
        // setState((state) => ({
        //   ...state,
        //   prompts: prompts,
        // }));
      } catch (error) {
        console.log(Utils.getErrorMessage(error));
        props.setError(true);
        props.setErrorMessage(Utils.getErrorMessage(error));
        setState((state) => ({
          ...state,
          modelsStatus: "error",
        }));
      }
    })();
  }, [appContext, state.modelsStatus, uploadFiles]);

  useEffect(() => {
    // Reset all states on component mount
    const resetStates = () => {
      setUploadedPDF(null);
      setUploadingStatus('info');
      setUploadProgress(0);
      setDocumentStatus('');
      setUseTempWorkspace(false);
      setUploadFiles([])
      setFailedDocuments([])
      onFileUploadStatusChange(false)
      // ①
      setIsFirstQuestion(true);
      // Reset workspace selection to default
      setState((state) => ({
        ...state,
        selectedWorkspace: workspaceDefaultOptions[0],
        selectedPrompt: promptDefaultOptions[0]
      }));
      // Clear stored workspace ID
      StorageHelper.setSelectedWorkspaceId('');
    };

    if(location.state?.resetChat){
      resetStates()
    }
  }, [location.state]);

  // followupdua-prompt
  const [isFirstQuestion, setIsFirstQuestion] = useState(true);
  useEffect(() => {
    if (uploadFiles.length > 0) {
      // Get all prompts by model
      let filteredPrompts = ModelPromptFilter.filterPrompts(prompts, state.selectedModel);

      // Set default prompt based on whether this is the first question
      let defaultPrompt;
      if (isFirstQuestion) {
        defaultPrompt = filteredPrompts.filter((p) => p.tags.includes("byod_fulldoc"));
      } else {
        defaultPrompt = filteredPrompts.filter((p) => p.tags.includes("followupdua2"));
      }

      const selectedPromptOption = OptionsHelper.getSelectPromptOptions(defaultPrompt)[0];
      setState((state) => ({
        ...state,
        prompts: filteredPrompts,
        selectedPrompt: selectedPromptOption,
        promptsStatus: "finished",
      }));
    }
  }, [uploadFiles, prompts, state.selectedModel, isFirstQuestion]);

  //Update prompt if documents are uploaded
  // useEffect(() => {
  //   if (uploadFiles.length > 0) {
  //     // Get all prompts by model
  //     let filteredPrompts = ModelPromptFilter.filterPrompts(prompts, state.selectedModel);
  //     filteredPrompts.forEach((prompt, index) => {
  //       console.log(`Prompt ${index + 1}:`);
  //       console.log("Name:", prompt.name);
  //       console.log("ID:", prompt.id);
  //       console.log("Tags:", prompt.tags);
  //       console.log("Content:", prompt.prompt);
  //       console.log("------------------------");
  //     });
  //     // Set default prompt
  //     let defaultPrompt = filteredPrompts.filter((p) => p.tags.includes("byod_fulldoc"));
  //     const selectedPromptOption = OptionsHelper.getSelectPromptOptions(defaultPrompt)[0];
  //     setState((state) => ({
  //       ...state,
  //       prompts: filteredPrompts,
  //       selectedPrompt: selectedPromptOption,
  //       promptsStatus: "finished",
  //     }));
  //   }
  // }, [uploadFiles, prompts, state.selectedModel]);

  const handleWorkspaceOrModelChange = async (selectedWorkspace, selectedModel: any) => {
    try {
      // Filter by workspace if selected
      if (selectedWorkspace && selectedWorkspace.value) {
        //get all prompts by model
        let filteredPrompts = ModelPromptFilter.filterPrompts(prompts, selectedModel);
        //set default prompt
        let defaultPrompt = filteredPrompts.filter((p) => p.tags.includes("default"));
        const workspacePrompt = ModelPromptFilter.filterPromptsByWorkspace(filteredPrompts, selectedWorkspace);
        if (workspacePrompt.length>0) {
          defaultPrompt = workspacePrompt
        }

        const selectedPromptOption = OptionsHelper.getSelectPromptOptions(defaultPrompt)[0];
        setState((state) => ({
          ...state,
          prompts: filteredPrompts,
          selectedPrompt: selectedPromptOption,
          promptsStatus: "finished",
        }));
      }
    } catch (error) {
      console.log("Workspace/Model change error:", error);
      props.setError(true);
      props.setErrorMessage(Utils.getErrorMessage(error));
      setState((state) => ({
        ...state,
        promptsStatus: "error",
      }));
    }
  };

  useEffect(() => {
    if (state.selectedWorkspace || state.selectedModel) {
      handleWorkspaceOrModelChange(state.selectedWorkspace, state.selectedModel);
    }
  }, [state.selectedWorkspace, state.selectedModel]);

  /**
   * Updates the message history ref when the message history prop changes
   */
  useEffect(() => {
    const onWindowScroll = () => {
      if (ChatScrollState.skipNextScrollEvent) {
        ChatScrollState.skipNextScrollEvent = false;
        return;
      }

      const isScrollToTheEnd =
          Math.abs(
              window.innerHeight +
              window.scrollY -
              document.documentElement.scrollHeight
          ) <= 10;

      if (!isScrollToTheEnd) {
        ChatScrollState.userHasScrolled = true;
      } else {
        ChatScrollState.userHasScrolled = false;
      }
    };

    window.addEventListener("scroll", onWindowScroll);

    return () => {
      window.removeEventListener("scroll", onWindowScroll);
    };
  }, []);

  /**
   * Scrolls to the bottom of the chat when the message history changes
   */
  useLayoutEffect(() => {
    if (ChatScrollState.skipNextHistoryUpdate) {
      ChatScrollState.skipNextHistoryUpdate = false;
      return;
    }

    if (!ChatScrollState.userHasScrolled && props.messageHistory.length > 0) {
      ChatScrollState.skipNextScrollEvent = true;
      window.scrollTo({
        top: document.documentElement.scrollHeight + 1000,
        behavior: "instant",
      });
    }
  }, [props.messageHistory]);

  /**
   * Fetches the signed urls for the files
   */
  useEffect(() => {
    const getSignedUrls = async () => {
      if (props.configuration?.files as ImageFile[]) {
        const files: ImageFile[] = [];
        for await (const file of props.configuration.files as ImageFile[]) {
          const signedUrl = await getSignedUrl(file.key);
          files.push({
            ...file,
            url: signedUrl as string,
          });
        }

        setFiles(files);
      }
    };

    if (props.configuration.files?.length) {
      getSignedUrls();
    }
  }, [props.configuration]);

  // 当选择MOU构建器和双方时，确保textarea高度自动调整
  useEffect(() => {
    if (selectedDocumentBuilder.value === "dua" &&
        selectedParty1.value &&
        selectedParty2.value) {

      // 当MOU模板准备好后，主动调整文本区域的高度
      setTimeout(() => {
        if (inputRef.current) {
          // 使用MOU模板时添加额外的CSS类
          inputRef.current.classList.add('expanded');

          // 强制重新计算高度
          inputRef.current.style.height = 'auto';
          inputRef.current.style.height = `${inputRef.current.scrollHeight}px`;
        }
      }, 0);
    } else {
      // 移除额外的CSS类
      if (inputRef.current) {
        inputRef.current.classList.remove('expanded');
      }
    }
  }, [selectedDocumentBuilder.value, selectedParty1.value, selectedParty2.value]);

  /**
   * Clears the screen contents
   */
  const clearScreenContents = async () => {
    messageHistoryRef.current = [];
    props.setMessageHistory(messageHistoryRef.current);
    // ①
    setIsFirstQuestion(true);
  };

  /**
   * Validates the prompt selection
   */
  // useEffect(() => {
  //   if (state?.selectedPrompt?.value?.length === 0) {
  //     setErrorMessageForPrompt("Please select a prompt");
  //   } else {
  //     setErrorMessageForPrompt("");
  //   }
  // }, [state.selectedPrompt]);

  /**
   * Handles sending a message to the chatbot
   * @returns
   */
  const handleSendMessage = async () => {
        if (!state.selectedModel) return;
        if (props.running) return;
        if (readyState !== ReadyState.OPEN) return;
        ChatScrollState.userHasScrolled = false;
        let { name, provider } = OptionsHelper.parseValue(
            state.selectedModel.value
        );
        // ① 判定是否为第一次的问题, 只增加这个
        if (isFirstQuestion) {
          setIsFirstQuestion(false);
        }

        // Check if MOU document builder is selected and both parties are chosen
        let value = state.value.trim();

        // Check for MCP command with improved logging and support for both @ and / prefixes
        // console.log("检查输入是否为MCP命令:", value);
        const isMCPCommand = value.match(/^[@/]mcp\s+(.+)/i);

        if (isMCPCommand) {
          // console.log("检测到MCP命令:", isMCPCommand[1]);
          // await handleMCPCommand(isMCPCommand[1]);
          await mcpHandler.handleCommand(isMCPCommand[1]);
          return;
        }


        // 如果输入为空但有组合提示词，并且不是只选择了默认选项，则使用组合提示词
        // if (value.length === 0 && multiplePromptValue && !onlyDefaultOptionSelected) {
        //   value = multiplePromptValue;
        // }
        // 解决 multi 无论如何都发送
        let promptPrefix = suggestedPrompt;
        if (activePromptSystem === 'suggestions') {
          // 使用建议提示
          value = state.value.trim();
          // 确保不使用多选提示
          // 即使输入为空且有多选提示值，也不使用多选提示
        }
        else if (activePromptSystem === 'multiple') {
          // 使用多选提示
          if (value.length === 0 && multiplePromptValue && !onlyDefaultOptionSelected) {
            value = multiplePromptValue;
          }
          // 不添加建议提示前缀
          promptPrefix = '';
        }


        // If document builder is set to MOU and both parties are selected, use the MOU template
        if (selectedDocumentBuilder.value === "dua" &&
            selectedParty1.value &&
            selectedParty2.value) {

          // Get current date for the template
          const currentDate = formatDate(new Date());
          // Set effective date as 1st of next month
          const nextMonth = new Date();
          nextMonth.setMonth(nextMonth.getMonth() + 1);
          nextMonth.setDate(1);
          const effectiveDate = formatDate(nextMonth);

          // Get user name for the template
          const authorName = userName ? `${userName.firstName}` : "Author Name";
          const approverName = userName ? `${userName.firstName}` : "Approver Name";

          // Replace [party1], [party2], dates and names in the template
          let formattedPrompt = duaPromptTemplate
              .replace(/\[party1\]/g, selectedParty1.label)
              .replace(/\[party2\]/g, selectedParty2.label)
              .replace(/\[currentDate\]/g, currentDate)
              .replace(/\[effectiveDate\]/g, effectiveDate)
              .replace(/\[authorName\]/g, authorName)
              .replace(/\[approverName\]/g, approverName);

          // Use the formatted prompt as the input value
          value = formattedPrompt;
        }

        // Clear the state value
        setState((state) => ({
          ...state,
          value: "",
        }));

        // Clear the Lexical editor content
        const editor = (window as any).lexicalEditor;
        if (editor) {
          // Import might be needed if not available in this scope
          // import { SET_EDITOR_CONTENT_COMMAND } from './path/to/LexicalRichTextEditor';
          editor.dispatchCommand(SET_EDITOR_CONTENT_COMMAND, {
            content: "",
            isLongContent: false,
            removeMaxHeight: false
          });
        }

        setFiles([]);

        props.setConfiguration({
          ...props.configuration,
          files: [],
        });

        props.setRunning(true);
        messageHistoryRef.current = [
          ...messageHistoryRef.current,

          {
            type: ChatBotMessageType.Human,
            content: promptPrefix  + value,
            metadata: {
              ...props.configuration,
            },
            tokens: [],
            chatId: props.chatId,
          },
          {
            type: ChatBotMessageType.AI,
            tokens: [],
            content: "",
            metadata: {},
            chatId: props.chatId,
          },
        ];

        props.setMessageHistory(messageHistoryRef.current);

        let response = undefined;
        const isCFGModel = state.selectedModel?.value?.startsWith("cfggpt");

        const foundModel = _.find(state.models, {
          name: state.selectedModel?.label,
        });

        if (foundModel) {
          let selectedModelName = isCFGModel
              ? foundModel?.modelId
              : state.selectedModel?.label;

          /*Temp fix*/
          if(!userRoles?.includes("OWNER")){
            provider =  "bedrock",
                selectedModelName = "anthropic.claude-3-5-sonnet-20240620-v1:0"
                // selectedModelName = "anthropic.claude-3-haiku-20240307-v1:0"
          }

          if (selectedModelName) {
            let followupPromptId =
                StorageHelper.getSelectedFollowupPromptId() !== null
                    ? StorageHelper.getSelectedFollowupPromptId()
                    : "";
            const payload = {
              // userId: "<EMAIL>",
              userId: userEmail,
              data: {
                provider: provider,
                modelName: selectedModelName,
                mode: ChatBotMode.Chain,
                text: value,
                files: uploadFiles || [],
                sessionId: props.session.id,
                workspaceId: state.selectedWorkspace?.value,
                promptId: _.isEmpty(state.selectedPrompt?.value)
                    ? undefined
                    : state.selectedPrompt?.value,
                // promptId: 'b02a61a3-409c-48cb-a6b4-db312a71577a',
                modelKwargs: {
                  streaming: props.configuration.streaming,
                  useHistory: props.configuration.useHistory,
                  followPromptId: followupPromptId,
                  maxTokens: props.configuration.maxTokens,
                  temperature: props.configuration.temperature,
                  topP: props.configuration.topP,
                  numDocs: props.configuration.numDocs,
                },
                chatId: props.chatId,
              },
            };

            try {
              response = await chatService.sendQuery(payload);
              dispatch(chatActions?.setChatId(payload.data.chatId));
              dispatch(
                  chatActions?.handleChatDocuments(
                      response?.data?.metadata?.documents
                  )
              );
            } catch (error) {
              if ((error as any) || (error as any).response?.status !== 200) {
                // console.log('Error',error.response.status, 'Please try again or contact support.');
                messageHistoryRef.current[
                messageHistoryRef.current.length - 1
                    ].content = "NA";
              }
              props.setRunning(false);
            }

            updateMessageHistoryRef(
                props.session.id,
                messageHistoryRef.current,
                response
            );
          }
        }

        if (
            response?.action === ChatBotAction.FinalResponse ||
            response?.action === ChatBotAction.Error
        ) {
          console.log("Final message received");
          props.setRunning(false);
        }
        props.setMessageHistory([...messageHistoryRef.current]);
        // Clear the PDF after sending
        setUploadedPDF(null);
      };

  // MCP hook and handler 被封装了
  // const { servers, clients, getClient } = useMCP();
  // const handleMCPCommand = async (command: string) => {
  //   console.log("handleMCPCommand被调用，命令:", command);
  //   console.log("MCP服务器数量:", servers.length);
  //
  //   if (servers.length === 0) {
  //     // No MCP servers configured
  //     console.log("没有配置MCP服务器");
  //     messageHistoryRef.current = [
  //       ...messageHistoryRef.current,
  //       {
  //         type: ChatBotMessageType.Human,
  //         content: `/mcp ${command}`,
  //         metadata: {
  //           ...props.configuration,
  //         },
  //         tokens: [],
  //         chatId: props.chatId,
  //       },
  //       {
  //         type: ChatBotMessageType.AI,
  //         tokens: [],
  //         content: "没有配置MCP服务器。请前往MCP服务器页面添加并连接服务器。",
  //         metadata: {},
  //         chatId: props.chatId,
  //       },
  //     ];
  //
  //     props.setMessageHistory(messageHistoryRef.current);
  //     return;
  //   }
  //
  //   // Clear the input
  //   setState((state) => ({
  //     ...state,
  //     value: "",
  //   }));
  //
  //   // Clear the Lexical editor content
  //   const editor = (window as any).lexicalEditor;
  //   if (editor) {
  //     editor.dispatchCommand(SET_EDITOR_CONTENT_COMMAND, {
  //       content: "",
  //       isLongContent: false,
  //       removeMaxHeight: false
  //     });
  //   }
  //
  //   // Add user message to chat
  //   messageHistoryRef.current = [
  //     ...messageHistoryRef.current,
  //     {
  //       type: ChatBotMessageType.Human,
  //       content: `/mcp ${command}`,
  //       metadata: {
  //         ...props.configuration,
  //       },
  //       tokens: [],
  //       chatId: props.chatId,
  //     },
  //     {
  //       type: ChatBotMessageType.AI,
  //       tokens: [],
  //       content: "正在处理MCP请求...",
  //       metadata: {},
  //       chatId: props.chatId,
  //     },
  //   ];
  //
  //   props.setMessageHistory(messageHistoryRef.current);
  //   props.setRunning(true);
  //
  //   try {
  //     // Get the first connected client
  //     const connectedServer = servers.find(server => server.isConnected);
  //     console.log("查找已连接的MCP服务器:", connectedServer ? connectedServer.name : "未找到");
  //
  //     if (!connectedServer) {
  //       throw new Error("没有找到已连接的MCP服务器");
  //     }
  //
  //     const mcpClient = getClient(connectedServer.id);
  //     console.log("获取MCP客户端:", mcpClient ? "成功" : "失败");
  //
  //     if (!mcpClient) {
  //       throw new Error(`无法获取服务器 ${connectedServer.name} 的MCP客户端`);
  //     }
  //
  //     try {
  //       console.log("开始调用MCP服务器, 尝试直接调用工具:", command);
  //       // 尝试直接调用服务器工具，假设命令格式为"工具名称 参数1=值1 参数2=值2"
  //       const [toolName, ...args] = command.trim().split(/\s+/);
  //
  //       // 解析参数
  //       let toolArgs = {};
  //       if (args.length > 0) {
  //         // 合并所有参数文本
  //         const argsText = args.join(' ');
  //         console.log("原始命令参数文本:", argsText);
  //
  //         try {
  //           // 首先尝试作为完整的JSON对象解析
  //           if (argsText.trim().startsWith('{') && argsText.trim().endsWith('}')) {
  //             try {
  //               toolArgs = JSON.parse(argsText.trim());
  //               console.log("成功将完整命令解析为JSON对象:", toolArgs);
  //             } catch (jsonError) {
  //               console.warn("JSON解析失败，回退到参数解析:", jsonError);
  //               // 继续使用参数解析
  //             }
  //           }
  //
  //           // 如果不是有效的JSON对象，继续解析参数
  //           if (Object.keys(toolArgs).length === 0) {
  //             // 尝试检测JSON格式参数，如数组或复杂对象
  //             const jsonRegex = /(\w+)=([\[{].*?[\]}])/g;
  //             let match;
  //             while ((match = jsonRegex.exec(argsText)) !== null) {
  //               const [_, key, jsonValue] = match;
  //               try {
  //                 // 尝试解析JSON值
  //                 toolArgs[key] = JSON.parse(jsonValue);
  //                 console.log(`成功解析JSON参数 ${key}:`, toolArgs[key]);
  //               } catch (e) {
  //                 // 如果JSON解析失败，使用原始字符串
  //                 console.warn(`无法解析JSON参数 ${key}=${jsonValue}:`, e);
  //                 toolArgs[key] = jsonValue;
  //               }
  //             }
  //
  //             // 尝试解析key=value形式的参数
  //             const keyValueRegex = /(\w+)=("[^"]+"|'[^']+'|[^"\s\[\{][^\s,]*(?:,[^\s,]*)*)/g;
  //             while ((match = keyValueRegex.exec(argsText)) !== null) {
  //               // 跳过已经处理过的JSON参数
  //               if (toolArgs.hasOwnProperty(match[1])) continue;
  //
  //               const [_, key, rawValue] = match;
  //               // 移除可能的引号
  //               let value = rawValue.replace(/^["']|["']$/g, '');
  //
  //               // 处理特殊参数
  //               if (key === 'file_paths' || key.includes('path') || key.includes('file')) {
  //                 // 检测是否为逗号分隔的路径列表
  //                 if (value.includes(',')) {
  //                   console.log(`检测到逗号分隔的路径列表 ${key}:`, value);
  //                   // 分割并清理每个路径
  //                   const paths = value.split(',')
  //                     .map(path => path.trim())
  //                     .filter(path => path.length > 0)
  //                     // 替换Windows路径中的反斜杠为正斜杠或确保正确转义
  //                     .map(path => path.replace(/\\/g, '\\\\'));
  //
  //                   toolArgs[key] = paths;
  //                   console.log(`处理后的路径列表 ${key}:`, toolArgs[key]);
  //                   continue;
  //                 }
  //                 // 单个路径处理，确保Windows路径正确转义
  //                 else if (value.includes('\\')) {
  //                   value = value.replace(/\\/g, '\\\\');
  //                   console.log(`处理Windows路径 ${key}:`, value);
  //                 }
  //               }
  //
  //               // 尝试转换布尔值和数字
  //               if (value.toLowerCase() === 'true') value = true;
  //               else if (value.toLowerCase() === 'false') value = false;
  //               else if (!isNaN(Number(value)) && value.trim() !== '') value = Number(value);
  //
  //               toolArgs[key] = value;
  //             }
  //
  //             // 如果没有参数匹配，将剩余文本作为query参数
  //             if (Object.keys(toolArgs).length === 0) {
  //               toolArgs = { query: argsText };
  //             }
  //           }
  //         } catch (e) {
  //           console.error("参数解析错误:", e);
  //           // 解析出错时，使用简单的query参数
  //           toolArgs = { query: argsText };
  //         }
  //
  //         console.log("最终解析的参数:", toolArgs);
  //       }
  //
  //       console.log("解析的工具名称:", toolName, "参数:", toolArgs);
  //
  //       // 检查是否有URL
  //       if (!connectedServer.url) {
  //         throw new Error("服务器URL未定义，无法发送请求");
  //       }
  //
  //       console.log("尝试通过fetch直接发送请求到:", connectedServer.url);
  //
  //       // 首先获取工具参数 - 处理带引号的文件路径
  //       let toolParams = {};
  //       if (args.length > 0) {
  //         // 特殊处理带引号的文件路径
  //         if (args.join(' ').match(/^".*"$/)) {
  //           toolParams = { file_path: args.join(' ').replace(/^"|"$/g, '') };
  //         } else {
  //           toolParams = toolArgs;
  //         }
  //       }
  //
  //       console.log("使用修改后的参数:", toolParams);
  //
  //       let response;
  //       try {
  //         console.log("尝试使用execute方法");
  //         const fetchResponse = await fetch(connectedServer.url, {
  //           method: 'POST',
  //           headers: {
  //             'Content-Type': 'application/json',
  //           },
  //           body: JSON.stringify({
  //             jsonrpc: '2.0',
  //             method: 'execute',
  //             params: {
  //               tool: toolName,
  //               params: toolParams
  //             },
  //             id: Date.now()
  //           })
  //         });
  //
  //         if (!fetchResponse.ok) {
  //           throw new Error(`HTTP错误! 状态: ${fetchResponse.status}`);
  //         }
  //
  //         response = await fetchResponse.json();
  //         console.log("通过execute方法获得响应:", response);
  //       } catch (error) {
  //         console.error("execute方法失败:", error);
  //
  //         // 尝试直接使用initialize方法查询服务器能力
  //         try {
  //           console.log("尝试使用initialize方法查询服务器能力");
  //           const initResponse = await fetch(connectedServer.url, {
  //             method: 'POST',
  //             headers: {
  //               'Content-Type': 'application/json',
  //             },
  //             body: JSON.stringify({
  //               jsonrpc: '2.0',
  //               method: 'initialize',
  //               params: {},
  //               id: Date.now()
  //             })
  //           });
  //
  //           if (initResponse.ok) {
  //             const initData = await initResponse.json();
  //             console.log("服务器初始化信息:", initData);
  //
  //             // 尝试使用mcp.process方法
  //             console.log("尝试使用mcp.process方法");
  //             const processResponse = await fetch(connectedServer.url, {
  //               method: 'POST',
  //               headers: {
  //                 'Content-Type': 'application/json',
  //               },
  //               body: JSON.stringify({
  //                 jsonrpc: '2.0',
  //                 method: 'mcp.process',
  //                 params: {
  //                   query: command
  //                 },
  //                 id: Date.now()
  //               })
  //             });
  //
  //             if (processResponse.ok) {
  //               const processData = await processResponse.json();
  //               console.log("mcp.process响应:", processData);
  //               response = processData;
  //             } else {
  //               // 显示服务器信息作为响应
  //               response = {
  //                 result: `MCP服务器信息: ${JSON.stringify(initData.result || initData, null, 2)}\n\n请检查服务器实现是否正确暴露了工具。`
  //               };
  //             }
  //           }
  //         } catch (initError) {
  //           console.error("初始化查询也失败:", initError);
  //           // 仍然使用原始错误信息
  //           throw error;
  //         }
  //       }
  //
  //       console.log("MCP服务器最终响应:", response);
  //
  //       // 更新AI消息内容
  //       const lastMessageIndex = messageHistoryRef.current.length - 1;
  //
  //       // 检查JSON-RPC响应格式
  //       if (response.error) {
  //         // 处理JSON-RPC错误
  //         throw new Error(`MCP服务器错误 ${response.error.code}: ${response.error.message}`);
  //       } else if (response.result !== undefined) {
  //         // 处理响应结果
  //         let resultText = "";
  //
  //         // 检查返回的结果类型
  //         if (typeof response.result === 'string') {
  //           // 如果结果是字符串，直接使用
  //           resultText = response.result;
  //         } else if (Array.isArray(response.result)) {
  //           // 如果结果是数组，尝试提取文本内容
  //           try {
  //             // 遍历数组寻找文本内容
  //             const textItems = response.result
  //               .filter(item => item.type === 'text' && item.text)
  //               .map(item => item.text);
  //
  //             if (textItems.length > 0) {
  //               // 使用所有文本项
  //               resultText = textItems.join('\n');
  //             } else {
  //               // 如果没有找到文本项，使用JSON字符串表示
  //               resultText = JSON.stringify(response.result, null, 2);
  //             }
  //           } catch (e) {
  //             // 如果解析失败，回退到显示原始JSON
  //             console.error("解析结果数组失败:", e);
  //             resultText = JSON.stringify(response.result, null, 2);
  //           }
  //         } else if (typeof response.result === 'object' && response.result !== null) {
  //           // 如果结果是对象，尝试提取有用信息或格式化显示
  //           resultText = JSON.stringify(response.result, null, 2);
  //         } else {
  //           // 其他类型的结果
  //           resultText = String(response.result);
  //         }
  //
  //         // 设置消息内容为提取的文本
  //         messageHistoryRef.current[lastMessageIndex].content = resultText;
  //       } else {
  //         // 未知响应格式
  //         messageHistoryRef.current[lastMessageIndex].content =
  //           `收到MCP服务器响应，但格式未知: ${JSON.stringify(response, null, 2)}`;
  //       }
  //     } catch (error) {
  //       // 更新AI消息中的错误
  //       console.error("MCP请求处理错误:", error);
  //       const lastMessageIndex = messageHistoryRef.current.length - 1;
  //       messageHistoryRef.current[lastMessageIndex].content = `MCP请求处理错误: ${error instanceof Error ? error.message : String(error)}`;
  //     } finally {
  //       props.setMessageHistory(messageHistoryRef.current);
  //       props.setRunning(false);
  //     }
  //   } catch (error) {
  //     // Update the AI message with the error
  //     console.error("MCP连接错误:", error);
  //     const lastMessageIndex = messageHistoryRef.current.length - 1;
  //     messageHistoryRef.current[lastMessageIndex].content = `MCP连接错误: ${error instanceof Error ? error.message : String(error)}`;
  //     props.setMessageHistory(messageHistoryRef.current);
  //     props.setRunning(false);
  //   }
  // };
    const mcpHandler = useMCPHandler(messageHistoryRef, setState, props);
  /**
   * Handles the feedback submission
   */
  const connectionStatus = {
    [ReadyState.CONNECTING]: "Connecting",
    [ReadyState.OPEN]: "Open",
    [ReadyState.CLOSING]: "Closing",
    [ReadyState.CLOSED]: "Closed",
    [ReadyState.UNINSTANTIATED]: "Uninstantiated",
  }[readyState];

  const modelsOptions = OptionsHelper.getSelectOptionGroups(state.models || []);

  const workspaceOptions = [
    ...workspaceDefaultOptions,
    ...OptionsHelper.getSelectWorkspaceOptions(state.workspaces || []),
  ];

  const promptsOptions = [
    ...promptDefaultOptions,
    ...OptionsHelper.getSelectPromptOptions(state.prompts || []),
  ];

  // prompt text fun
  // ①增加bold和高亮
  const handleOptionsClick = (promptText: string) => {
    // 设置活动提示系统
    setActivePromptSystem('suggestions');
    // Reset document builder
    setSelectedDocumentBuilder(documentBuilderOptions[0]);
    // Reset parties
    setSelectedParty1(party1Options[0]);
    setSelectedParty2(party2Options[0]);

    // Trigger reset in MultiplePromptSuggestions
    setResetMultiplePrompts(true);

    // 清空多选提示的值
    setMultiplePromptValue("");
    setOnlyDefaultOptionSelected(true);

    // Reset the reset trigger after a short delay
    setTimeout(() => {
      setResetMultiplePrompts(false);
    }, 100);

    // Create a special JSON state for Lexical that includes formatting
    const lexicalState = createFormattedLexicalState(promptText);

    setState((state) => ({
      ...state,
      value: promptText,
    }));

    setTimeout(() => {
      const editorContainer = document.querySelector('.lexical-editor-container');
      if (editorContainer) {
        const contentLength = promptText.length;
        const screenHeight = window.innerHeight;

        if (contentLength > 3000 || promptText.includes('Data Use Agreement')) {
          const mainContainer = document.querySelector('.editor-border') as HTMLElement;
          if (mainContainer) {
            mainContainer.style.maxHeight = 'none';

            if (promptText.includes('Data Use Agreement')) {
              mainContainer.classList.add('dua-content');
            }
          }
        }

        const editor = (window as any).lexicalEditor;
        if (editor) {
          // editor.dispatchCommand(SET_EDITOR_CONTENT_COMMAND, {
          //   content: promptText,
          //   isLongContent: contentLength > 3000 || promptText.includes('Data Use Agreement'),
          //   screenHeight: screenHeight,
          //   removeMaxHeight: true
          // });
          // Use the pre-formatted Lexical state
          editor.setEditorState(editor.parseEditorState(lexicalState));
        } else {
          // For the custom event approach
          // const event = new CustomEvent('set-editor-content', {
          //   detail: {
          //     content: promptText,
          //     lexicalState: lexicalState, // Pass the formatted state
          //     isLongContent: contentLength > 3000 || promptText.includes('Data Use Agreement'),
          //     screenHeight: screenHeight,
          //     removeMaxHeight: true
          //   },
          //   bubbles: true
          // });
          // editorContainer.dispatchEvent(event);
          const customEvent = new CustomEvent('set-editor-content', {
            detail: {
              content: promptText,
              lexicalState: lexicalState,
              isLongContent: contentLength > 3000 || promptText.includes('Data Use Agreement'),
              removeMaxHeight: true
            },
            bubbles: true
          });

          // 确保editorContainer存在
          const editorContainer = document.getElementById('editor-container');
          if (editorContainer) {
            editorContainer.dispatchEvent(customEvent);
          }
        }
      }
    }, 100);
  };

  // 多选①
  useEffect(() => {
    // 监听组合提示词变化事件
    const handleMultiplePromptChange = (event) => {
      setMultiplePromptValue(event.detail.prompt);
      setOnlyDefaultOptionSelected(event.detail.onlyDefaultSelected);

      // Always update the state and editor regardless of whether only default is selected
      setState((state) => ({
        ...state,
        value: event.detail.prompt,
      }));

      // Use a timeout to avoid interrupting the dropdown interaction
      setTimeout(() => {
        const editor = (window as any).lexicalEditor;
        if (editor) {
          // Check if the event contains a pre-formatted lexicalState
          if (event.detail.lexicalState) {
            try {
              // Use the pre-formatted state if available
              const editorState = editor.parseEditorState(event.detail.lexicalState);
              editor.setEditorState(editorState);
            } catch (error) {
              console.error("Error setting formatted editor state:", error);
              // Fallback to standard content setting
              editor.dispatchCommand(SET_EDITOR_CONTENT_COMMAND, {
                content: event.detail.prompt,
                isLongContent: event.detail.prompt.length > 3000,
                removeMaxHeight: true
              });
            }
          } else {
            // No lexicalState provided, create one with formatting for placeholders
            const formattedLexicalState = createFormattedLexicalState(event.detail.prompt);
            try {
              const editorState = editor.parseEditorState(formattedLexicalState);
              editor.setEditorState(editorState);
            } catch (error) {
              console.error("Error setting dynamically formatted editor state:", error);
              // Fallback to standard content setting
              editor.dispatchCommand(SET_EDITOR_CONTENT_COMMAND, {
                content: event.detail.prompt,
                isLongContent: event.detail.prompt.length > 3000,
                removeMaxHeight: true
              });
            }
          }
        }
      }, 0);
    };

    document.addEventListener('multiple-prompt-change', handleMultiplePromptChange);

    return () => {
      document.removeEventListener('multiple-prompt-change', handleMultiplePromptChange);
    };
  }, []);

  const handleInputClick = () =>{
    if(state.value.includes("[TEXT GOES HERE]")){
      const updatedValue = state.value.replace("[TEXT GOES HERE]", "[]")
      setState((state)=>({
        ...state,
        value: updatedValue,
      }))
      const cursorPosition = updatedValue.indexOf("[") + 1;
      setTimeout(()=>{
        inputRef.current.setSelectionRange(cursorPosition, cursorPosition)
      },0)
    }
  }

  const handleInputDelete = () => {
    setState((state) => ({
      ...state,
      value: '',
    }));

    const editor = (window as any).lexicalEditor;
    if (editor) {
      editor.dispatchCommand(SET_EDITOR_CONTENT_COMMAND, {
        content: "",
        isLongContent: false,
        removeMaxHeight: false
      });

      editor.focus();
    } else {
      const editorContainer = document.querySelector('.lexical-editor-container');
      if (editorContainer) {
        const event = new CustomEvent('set-editor-content', {
          detail: {
            content: "",
            isLongContent: false,
            removeMaxHeight: false
          },
          bubbles: true
        });
        editorContainer.dispatchEvent(event);

        const contentEditable = document.querySelector('.lexical-content-editable') as HTMLElement;
        if (contentEditable) {
          contentEditable.focus();
        }
      }
    }

    const contentEditable = document.querySelector('.lexical-content-editable') as HTMLElement;
    if (contentEditable) {
      contentEditable.classList.remove('long-content-mode');
      contentEditable.classList.remove('expanded');
      contentEditable.style.minHeight = '50px';
    }

    const editorContainer = document.querySelector('.editor-content-container') as HTMLElement;
    if (editorContainer) {
      editorContainer.style.minHeight = '';
    }

    const editorBorder = document.querySelector('.editor-border');
    if (editorBorder) {
      editorBorder.classList.remove('dua-content');
    }
  }

  return (
      <div>
        <SpaceBetween direction="vertical" size="l">
          <div className={`${styles.chat_input_container} {messageHistory.length > 0 ? ${styles.input_container_space} : ''}`}>
            <Container>
              <div>
                <div className={styles.input_textarea_container}>
                  <SpaceBetween size="xxs" direction="horizontal" alignItems="center">
                  </SpaceBetween>
                  <ImageDialog
                      sessionId={props.session.id}
                      visible={imageDialogVisible}
                      setVisible={setImageDialogVisible}
                      configuration={props.configuration}
                      setConfiguration={props.setConfiguration}
                  />
                  <div className={` ${styles.prompts_input_container} ${suggestedPrompt ? styles.no_suggestion : styles.suggestion}`}>
                    {/*<TextareaAutosize*/}
                    {/*    className={`${styles.input_textarea} ${state.value.length > 500 ? 'expanded' : ''}`}*/}
                    {/*    ref={inputRef}*/}
                    {/*    maxRows={20}*/}
                    {/*    minRows={1}*/}
                    {/*    cacheMeasurements*/}
                    {/*    spellCheck={true}*/}
                    {/*    autoFocus={true}*/}
                    {/*    onClick={(e) => {*/}
                    {/*      const target = e.target as HTMLTextAreaElement;*/}
                    {/*      const currentHeight = target.style.height;*/}
                    {/*      handleInputClick();*/}
                    {/*      setTimeout(() => {*/}
                    {/*        if (inputRef.current && currentHeight) {*/}
                    {/*          inputRef.current.style.height = currentHeight;*/}
                    {/*        }*/}
                    {/*      }, 0);*/}
                    {/*    }}*/}
                    {/*    onChange={(e) => {*/}
                    {/*      const currentHeight = inputRef.current ? inputRef.current.style.height : 'auto';*/}
                    {/*      setState((state) => ({*/}
                    {/*        ...state,*/}
                    {/*        value: e.target.value,*/}
                    {/*      }));*/}
                    {/*      setTimeout(() => {*/}
                    {/*        if (inputRef.current) {*/}
                    {/*          if (e.target.value.length > 500) {*/}
                    {/*            inputRef.current.classList.add('expanded');*/}
                    {/*          } else if (e.target.value.length < 100) {*/}
                    {/*            inputRef.current.classList.remove('expanded');*/}
                    {/*          }*/}
                    {/*          const newHeight = Math.max(*/}
                    {/*              parseInt(currentHeight || '38', 10),*/}
                    {/*              inputRef.current.scrollHeight*/}
                    {/*          );*/}

                    {/*          inputRef.current.style.height = `${newHeight}px`;*/}
                    {/*        }*/}
                    {/*      }, 0);*/}
                    {/*    }}*/}
                    {/*    onFocus={(e) => {*/}
                    {/*      if (inputRef.current) {*/}
                    {/*        inputRef.current.dataset.previousHeight = inputRef.current.style.height;*/}
                    {/*      }*/}
                    {/*    }}*/}
                    {/*    onBlur={(e) => {*/}
                    {/*      if (inputRef.current && state.value.trim().length > 0) {*/}
                    {/*        const prevHeight = inputRef.current.dataset.previousHeight;*/}
                    {/*        if (prevHeight) {*/}
                    {/*          inputRef.current.style.height = prevHeight;*/}
                    {/*        }*/}
                    {/*      }*/}
                    {/*    }}*/}
                    {/*    onKeyDown={(e) => {*/}
                    {/*      if (e.key == "Enter" && !e.shiftKey) {*/}
                    {/*        e.preventDefault();*/}

                    {/*        // Only send if input has content OR MOU is selected with parties*/}
                    {/*        if (state.value.trim().length > 0 || (selectedDocumentBuilder.value === "mou" && selectedParty1.value && selectedParty2.value)) {*/}
                    {/*          handleSendMessage();*/}
                    {/*        }*/}
                    {/*      }*/}
                    {/*    }}*/}
                    {/*    value={state.value}*/}
                    {/*    placeholder={listening ? "Listening..." : (selectedDocumentBuilder.value === "mou" && selectedParty1.value && selectedParty2.value) ? "Press Enter to generate MOU" : "Tell us how you would like to modify the document"}*/}
                    {/*/>*/}
                    <LexicalRichTextEditor
                        placeholder={listening ? "Listening..." : (selectedDocumentBuilder.value === "dua" && selectedParty1.value && selectedParty2.value) ? "Press Enter to generate DUA" : "Tell us how you would like to modify the document"}
                        initialValue={state.value}
                        onChange={(text) => {
                          setState((state) => ({
                            ...state,
                            value: text,
                          }));
                        }}
                        onSubmit={(text) => {
                          // Only send if input has content OR MOU is selected with parties
                          if (text.trim().length > 0 || (selectedDocumentBuilder.value === "dua" && selectedParty1.value && selectedParty2.value)) {
                            setState((state) => ({
                              ...state,
                              value: text,
                            }));
                            // handleSendMessage();
                          }
                        }}
                        maxHeight={300}
                    />
                  </div>
                  <div style={{ marginLeft: "8px", maxWidth:userRoles.includes("OWNER") ? '400px' : '370px', display:'flex', flexDirection:'column', alignItems:'end' }}>
                    {state.selectedModelMetadata?.inputModalities.includes(
                            ChabotInputModality.Image
                        ) &&
                        files.length > 0 &&
                        files.map((file, idx) => (
                            <img
                                key={idx}
                                onClick={() => setImageDialogVisible(true)}
                                src={file.url}
                                style={{
                                  borderRadius: "4px",
                                  cursor: "pointer",
                                  maxHeight: "30px",
                                  float: "left",
                                  marginRight: "8px",
                                }}
                            />
                        ))}
                    {documentListVisible && uploadFiles.length > 0 && uploadingStatus !== 'error' && (
                        <div
                            ref={alertContainerRef}
                            style={{
                              position: 'absolute',
                              zIndex: 10,
                              bottom: userRoles.includes("OWNER") ? '130px' : '85px',
                              right: '5px',
                              maxWidth: '600px',
                              display: 'flex',
                              alignItems: 'flex-end',
                              gap: '10px'
                            }}
                        >
                          <div style={{
                            flex: 1,
                            minWidth: '250px',
                            maxWidth: '300px'
                          }}>
                            <Alert
                                statusIconAriaLabel="Success"
                                type="success"
                                header="Attached Documents"
                            >
                              <div style={{height: '100px', overflowY: 'auto'}}>
                                <ul style={{margin: 0, padding: '0 0 0 20px'}}>
                                  {uploadFiles.map((file, index) => (
                                      <li style={{listStyleType: 'decimal'}} key={index}>
                                        {file.fileKey.split('/').pop()}
                                      </li>
                                  ))}
                                </ul>
                              </div>
                            </Alert>
                          </div>
                          {failedDocuments.length > 0 &&
                              <div style={{
                                flex: 1,
                                minWidth: '250px',
                                maxWidth: '300px'
                              }}>
                                <Alert
                                    statusIconAriaLabel="Error"
                                    type="error"
                                    header="Failed Documents"
                                >
                                  <div style={{height: '100px', overflowY: 'auto'}}>
                                    <ul style={{margin: 0, padding: '0 0 0 20px'}}>
                                      {failedDocuments.map((fileName, index) => (
                                          <li style={{listStyleType: 'decimal'}} key={index}>
                                            {fileName}
                                          </li>
                                      ))}
                                    </ul>
                                  </div>
                                </Alert>
                              </div>
                          }
                        </div>
                    )}
                    {(
                        <SpaceBetween direction="vertical" size="s">
                          <div
                              ref={onClickAreaRef}
                              style={{cursor: 'pointer'}}
                              onClick={() => setDocumentListVisible(!documentListVisible)}
                              title="Click to view uploaded documents."
                          >
                            {uploadFiles.length > 0 && (
                                <StatusIndicator type="success">
                                  {uploadFiles.length === 1
                                      ? "1 Document Uploaded"
                                      : `${uploadFiles.length} Documents Uploaded`}
                                </StatusIndicator>
                            )}
                          </div>
                          {documentStatus === 'processing' && (
                              <StatusIndicator colorOverride="blue" type="loading">
                                Uploading document(s)
                              </StatusIndicator>
                          )}
                          {((uploadingStatus === 'error' || documentStatus === 'failed' || documentStatus === 'wrongexttype') && uploadFiles.length === 0) && (
                              <StatusIndicator type="error">
                                {documentStatus === 'failed' ? validationErrorMessage :
                                    documentStatus === 'wrongexttype' ? "Please upload only .pdf or .docx files." :
                                        "An error occurred during upload."}
                              </StatusIndicator>
                          )}
                          {(uploadingStatus === 'error' || documentStatus === 'failed' || documentStatus === 'wrongexttype') && uploadFiles.length > 0 && (
                              <StatusIndicator type="error">
                                {documentStatus === 'failed' ? (
                                    validationErrorMessage
                                ) : (
                                    <>
                                      Some files could not be uploaded.
                                      <br />
                                      Please upload only .pdf or .docx files.
                                    </>
                                )}
                              </StatusIndicator>
                          )}
                        </SpaceBetween>
                    )}
                    <div style={{paddingTop: '5px'}}>
                      {/*{!uploadedPDF && (*/}
                      {/*    <PDFUploadButton*/}
                      {/*        onFilesSelect={handlePDFUpload}*/}
                      {/*        currentFiles={uploadedPDF}*/}
                      {/*        onClear={clearUploadedPDFs}*/}
                      {/*        disabled={documentStatus === 'processing' || state.selectedWorkspace.label !== 'Select'}*/}
                      {/*    />*/}
                      {/*)}*/}
                      <ConfigDialog
                          sessionId={props.session.id}
                          visible={configDialogVisible}
                          setVisible={setConfigDialogVisible}
                          configuration={props.configuration}
                          setConfiguration={props.setConfiguration}
                          selectedModel={state.selectedModel}
                      />
                      {/*{(userRoles?.includes("OWNER")) && <Button*/}
                      {/*    iconName="status-in-progress"*/}
                      {/*    variant="icon"*/}
                      {/*    iconUrl={adminSettingsIcon}*/}
                      {/*    onClick={() => setConfigDialogVisible(true)}*/}
                      {/*/>}*/}
                      {/*<button*/}
                      {/*    className={styles.clear_input_btn}*/}
                      {/*    disabled={ state.value.trim().length === 0}*/}
                      {/*    onClick={() => {handleInputDelete();}}*/}
                      {/*>Clear*/}
                      {/*</button>*/}
                      {/*<button*/}
                      {/*    className={styles.send_input_btn}*/}
                      {/*    disabled={*/}
                      {/*      // readyState !== ReadyState.OPEN ||*/}
                      {/*        !state.models?.length ||*/}
                      {/*        !state.selectedModel ||*/}
                      {/*        props.running ||*/}
                      {/*        documentStatus === 'processing' ||*/}
                      {/*        // Only require input field value if document builder is not MOU with both parties selected*/}
                      {/*        (state.value.trim().length === 0 && !(selectedDocumentBuilder.value === "mou" && selectedParty1.value && selectedParty2.value)) ||*/}
                      {/*        props.session.loading ||*/}
                      {/*        (!state.selectedPrompt?.value?.length &&*/}
                      {/*            !state.workspaces?.length)*/}
                      {/*    }*/}
                      {/*    onClick={() => {handleSendMessage();}}*/}
                      {/*>*/}
                      {/*  {props.running ? (*/}
                      {/*      <>*/}
                      {/*        Loading&nbsp;&nbsp;*/}
                      {/*        <Spinner />*/}
                      {/*      </>*/}
                      {/*  ) :  "Send"*/}
                      {/*  }*/}
                      {/*</button>*/}
                    </div>
                  </div>
                </div>
                <div >
                  {(userRoles?.includes("OWNER") || userRoles.includes("EDIT")) && <hr/>}
                  {(userRoles?.includes("OWNER") || userRoles.includes("EDIT")) && documentRepoDialogVisible &&
                      <div className={styles.document_library_container}>
                        {/*{(userRoles?.includes("OWNER")) &&*/}
                        {/*    <>*/}
                        {/*      <FormField label="Model" />*/}
                        {/*      <span>&nbsp;</span>*/}
                        {/*      <Select*/}
                        {/*          disabled={props.running}*/}
                        {/*          statusType={state.modelsStatus}*/}
                        {/*          loadingText="Loading models (might take few seconds)..."*/}
                        {/*          placeholder="Select a model"*/}
                        {/*          empty={*/}
                        {/*            <div>*/}
                        {/*              No models available. Please make sure you have access to*/}
                        {/*              Amazon Bedrock or alternatively deploy a self hosted model on*/}
                        {/*              SageMaker or add API_KEY to Secrets Manager*/}
                        {/*            </div>*/}
                        {/*          }*/}
                        {/*          filteringType="auto"*/}
                        {/*          selectedOption={state.selectedModel}*/}
                        {/*          onChange={({ detail }) => {*/}
                        {/*            setState((state) => ({*/}
                        {/*              ...state,*/}
                        {/*              selectedModel: detail.selectedOption,*/}
                        {/*              selectedModelMetadata: getSelectedModelMetadata(*/}
                        {/*                  state.models,*/}
                        {/*                  detail.selectedOption*/}
                        {/*              ),*/}
                        {/*            }));*/}
                        {/*            if (detail.selectedOption?.value) {*/}
                        {/*              StorageHelper.setSelectedLLM(detail.selectedOption.value);*/}
                        {/*            }*/}
                        {/*          }}*/}
                        {/*          options={modelsOptions}*/}
                        {/*      />*/}
                        {/*    </>*/}
                        {/*}*/}
                        <span>&nbsp;&nbsp;&nbsp;</span>
                        {/*{(userRoles?.includes("OWNER")) &&*/}
                        {/*    <>*/}
                        {/*      <FormField label="Prompt" />*/}
                        {/*      <span>&nbsp;</span>*/}
                              {/*<Select*/}
                              {/*    disabled={props.running}*/}
                              {/*    statusType={state.promptsStatus}*/}
                              {/*    loadingText="Loading prompts (might take few seconds)..."*/}
                              {/*    placeholder="Select a prompt"*/}
                              {/*    empty={<div>No prompts available.</div>}*/}
                              {/*    filteringType="auto"*/}
                              {/*    selectedOption={state.selectedPrompt}*/}
                              {/*    onChange={({ detail }) => {*/}
                              {/*      console.log("Selected prompt content:----------------》》》》》》》", {*/}
                              {/*        name: detail.selectedOption.label,*/}
                              {/*        value: detail.selectedOption.value,*/}
                              {/*        promptText: prompts.find(p => p.id === detail.selectedOption.value)?.prompt || "Prompt content not found"*/}
                              {/*      });*/}

                              {/*      setState((state) => ({*/}
                              {/*        ...state,*/}
                              {/*        selectedPrompt: detail.selectedOption,*/}
                              {/*      }));*/}
                              {/*      if (detail.selectedOption?.value) {*/}
                              {/*        StorageHelper.setSelectedPromptId(*/}
                              {/*            detail.selectedOption.value*/}
                              {/*        );*/}
                              {/*      }*/}
                              {/*    }}*/}
                              {/*    options={promptsOptions}*/}
                              {/*/>*/}
                        {/*      <Select*/}
                        {/*          disabled={props.running}*/}
                        {/*          statusType={state.promptsStatus}*/}
                        {/*          loadingText="Loading prompts (might take few seconds)..."*/}
                        {/*          placeholder="Select a prompt"*/}
                        {/*          empty={<div>No prompts available.</div>}*/}
                        {/*          filteringType="auto"*/}
                        {/*          selectedOption={state.selectedPrompt}*/}
                        {/*          onChange={({ detail }) => {*/}
                        {/*            try {*/}
                        {/*              setState((state) => ({*/}
                        {/*                ...state,*/}
                        {/*                selectedPrompt: detail.selectedOption,*/}
                        {/*              }));*/}

                        {/*              // 存储选择的 prompt ID*/}
                        {/*              StorageHelper.setSelectedPromptId(*/}
                        {/*                  detail.selectedOption?.value ?? ""*/}
                        {/*              );*/}

                        {/*              // 如果是 claude_follow_up，添加额外调试*/}
                        {/*              if (detail.selectedOption?.label === "claude_follow_up") {*/}
                        {/*                console.log("Claude follow-up prompt selected:", detail.selectedOption);*/}
                        {/*                const promptObj = prompts.find(p => p.id === detail.selectedOption.value);*/}
                        {/*                console.log("Full prompt object:", promptObj);*/}
                        {/*              }*/}
                        {/*            } catch (error) {*/}
                        {/*              console.error("Error when selecting prompt:", error);*/}
                        {/*              console.error("Selected option:", detail.selectedOption);*/}
                        {/*              console.error("Current state:", state);*/}
                        {/*            }*/}
                        {/*          }}*/}
                        {/*          options={promptsOptions}*/}
                        {/*      />*/}
                        {/*    </>*/}
                        {/*}*/}
                        {/*原来的位置，现在改为外面了*/}
                        {/* Document builder field - new addition */}
                        {/*<FormField label="Document builder" />*/}
                        {/*<span>&nbsp;</span>*/}
                        {/*<Select*/}
                        {/*    disabled={props.running}*/}
                        {/*    placeholder="Select document type"*/}
                        {/*    selectedOption={selectedDocumentBuilder}*/}
                        {/*    options={documentBuilderOptions}*/}
                        {/*    onChange={({ detail }) => {*/}
                        {/*      setSelectedDocumentBuilder(detail.selectedOption);*/}
                        {/*      // Reset party selections when document type changes*/}
                        {/*      setSelectedParty1(party1Options[0]);*/}
                        {/*      setSelectedParty2(party2Options[0]);*/}
                        {/*    }}*/}
                        {/*/>*/}

                        {/*/!* Only show Party1 and Party2 fields when MOU is selected *!/*/}
                        {/*{selectedDocumentBuilder.value === "mou" && (*/}
                        {/*    <>*/}
                        {/*      <span>&nbsp;&nbsp;&nbsp;</span>*/}
                        {/*      <FormField label="Party 1" />*/}
                        {/*      <span>&nbsp;</span>*/}
                        {/*      <Select*/}
                        {/*          disabled={props.running}*/}
                        {/*          placeholder="Select Party 1"*/}
                        {/*          selectedOption={selectedParty1}*/}
                        {/*          options={party1Options}*/}
                        {/*          onChange={({ detail }) => setSelectedParty1(detail.selectedOption)}*/}
                        {/*      />*/}

                        {/*      <span>&nbsp;&nbsp;&nbsp;</span>*/}
                        {/*      <FormField label="Party 2" />*/}
                        {/*      <span>&nbsp;</span>*/}
                        {/*      <Select*/}
                        {/*          disabled={props.running}*/}
                        {/*          placeholder="Select Party 2"*/}
                        {/*          selectedOption={selectedParty2}*/}
                        {/*          options={party2Options}*/}
                        {/*          onChange={({ detail }) => setSelectedParty2(detail.selectedOption)}*/}
                        {/*      />*/}
                        {/*    </>*/}
                        {/*)}*/}

                        <span>&nbsp;&nbsp;&nbsp;</span>

                        {!uploadedPDF && (
                            <PDFUploadButton
                                onFilesSelect={handlePDFUpload}
                                currentFiles={uploadedPDF}
                                onClear={clearUploadedPDFs}
                                disabled={documentStatus === 'processing' || state.selectedWorkspace.label !== 'Select'}
                            />
                        )}

                        {(userRoles?.includes("OWNER")) && <Button
                            iconName="status-in-progress"
                            variant="icon"
                            iconUrl={adminSettingsIcon}
                            onClick={() => setConfigDialogVisible(true)}
                        />}
                        <button
                            className={styles.clear_input_btn}
                            disabled={ state.value.trim().length === 0}
                            onClick={() => {handleInputDelete();}}
                        >Clear
                        </button>
                        {/*<button*/}
                        {/*    className={styles.send_input_btn}*/}
                        {/*    disabled={*/}
                        {/*      // readyState !== ReadyState.OPEN ||*/}
                        {/*        !state.models?.length ||*/}
                        {/*        !state.selectedModel ||*/}
                        {/*        props.running ||*/}
                        {/*        documentStatus === 'processing' ||*/}
                        {/*        // Only require input field value if document builder is not MOU with both parties selected*/}
                        {/*        (state.value.trim().length === 0 && !(selectedDocumentBuilder.value === "mou" && selectedParty1.value && selectedParty2.value)) ||*/}
                        {/*        props.session.loading*/}
                        {/*        // 解决Send按钮不可用*/}
                        {/*        // || (!state.selectedPrompt?.value?.length && !state.workspaces?.length)*/}
                        {/*    }*/}
                        {/*    onClick={() => {handleSendMessage();}}*/}
                        {/*>*/}
                        {/*  {props.running ? (*/}
                        {/*      <>*/}
                        {/*        Loading&nbsp;&nbsp;*/}
                        {/*        <Spinner />*/}
                        {/*      </>*/}
                        {/*  ) :  "Send"*/}
                        {/*  }*/}
                        {/*</button>*/}
                        <button
                            className={styles.send_input_btn}
                            disabled={
                                !state.models?.length ||
                                !state.selectedModel ||
                                props.running ||
                                documentStatus === 'processing' ||
                                // 关键修改：检查是否只有默认选项
                                (state.value.trim().length === 0 &&
                                    (onlyDefaultOptionSelected || !multiplePromptValue) &&
                                    !(selectedDocumentBuilder.value === "dua" && selectedParty1.value && selectedParty2.value)) ||
                                props.session.loading
                            }
                            onClick={() => {handleSendMessage();}}
                        >
                          {props.running ? (
                              <>
                                Loading&nbsp;&nbsp;
                                <Spinner />
                              </>
                          ) :  "Send"
                          }
                        </button>

                      </div>
                  }
                </div>
              </div>
            </Container>
          </div>
        </SpaceBetween>
        <div style={{ display: 'flex', alignItems: 'flex-start' }}>
          {state.selectedWorkspace.label === 'Select' &&
              <PromptSuggestions
                  suggestedPrompts={suggestedPrompts}
                  onOptionsClick={handleOptionsClick}
                  resetDocumentBuilder={() => {
                    setSelectedDocumentBuilder(documentBuilderOptions[0]);
                    setSelectedParty1(party1Options[0]);
                    setSelectedParty2(party2Options[0]);
                    setResetMultiplePrompts(true);
                    setTimeout(() => setResetMultiplePrompts(false), 100);
                  }}
              />}
          <span>&nbsp;&nbsp;&nbsp;</span>

          {/* Document builder field - new addition */}
          {/*<div*/}
          {/*    ref={containerRef}*/}
          {/*    style={{*/}
          {/*      marginTop: `${topOffset+21}px`,*/}
          {/*      position: 'relative',*/}
          {/*      marginBottom: '20px'*/}
          {/*    }}*/}
          {/*>*/}
          {/*  <div className={styles.document_library_container} style={{ display: 'flex', alignItems: 'center' }}>*/}
          {/*    /!* Document builder 组 - 水平布局 *!/*/}
          {/*    <div style={{ display: 'flex', alignItems: 'center', marginRight: '20px' }}>*/}
          {/*      <FormField label="Document builder" />*/}
          {/*      <span>&nbsp;&nbsp;</span>*/}
          {/*      <Select*/}
          {/*          disabled={props.running}*/}
          {/*          placeholder="Select document type"*/}
          {/*          selectedOption={selectedDocumentBuilder}*/}
          {/*          options={documentBuilderOptions}*/}
          {/*          onChange={({ detail }) => {*/}
          {/*            setSelectedDocumentBuilder(detail.selectedOption);*/}
          {/*            // Reset party selections when document type changes*/}
          {/*            setSelectedParty1(party1Options[0]);*/}
          {/*            setSelectedParty2(party2Options[0]);*/}
          {/*          }}*/}
          {/*      />*/}
          {/*    </div>*/}

          {/*    /!* Only show Party1 and Party2 fields when MOU is selected *!/*/}
          {/*    {selectedDocumentBuilder.value === "mou" && (*/}
          {/*        <>*/}
          {/*          /!* Party 1 组 - 水平布局 *!/*/}
          {/*          <div style={{ display: 'flex', alignItems: 'center', marginRight: '20px' }}>*/}
          {/*            <FormField label="Party 1" />*/}
          {/*            <span>&nbsp;&nbsp;</span>*/}
          {/*            <Select*/}
          {/*                disabled={props.running}*/}
          {/*                placeholder="Select Party 1"*/}
          {/*                selectedOption={selectedParty1}*/}
          {/*                options={party1Options}*/}
          {/*                onChange={({ detail }) => setSelectedParty1(detail.selectedOption)}*/}
          {/*            />*/}
          {/*          </div>*/}

          {/*          /!* Party 2 组 - 水平布局 *!/*/}
          {/*          <div style={{ display: 'flex', alignItems: 'center' }}>*/}
          {/*            <FormField label="Party 2" />*/}
          {/*            <span>&nbsp;&nbsp;</span>*/}
          {/*            <Select*/}
          {/*                disabled={props.running}*/}
          {/*                placeholder="Select Party 2"*/}
          {/*                selectedOption={selectedParty2}*/}
          {/*                options={party2Options}*/}
          {/*                onChange={({ detail }) => setSelectedParty2(detail.selectedOption)}*/}
          {/*            />*/}
          {/*          </div>*/}
          {/*        </>*/}
          {/*    )}*/}
          {/*  </div>*/}
          {/*</div>*/}
          {/*<span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>*/}
        <MultiplePromptSuggestions
            running={props.running}
            resetState={resetMultiplePrompts}
        />
      </div>
        {state.selectedModel && (
            <ModelNotification
                selectedModel={state.selectedModel}
                duration={3000}
            />
        )}
      </div>
  );
}

function getSelectedWorkspaceOption(
    workspaces: Workspace[]
): SelectProps.Option | null {
  let selectedWorkspaceOption: SelectProps.Option | null = null;

  const savedWorkspaceId = StorageHelper.getSelectedWorkspaceId();
  if (savedWorkspaceId) {
    const targetWorkspace = workspaces.find(
        (w) => w.workspaceId === savedWorkspaceId
    );

    if (targetWorkspace) {
      selectedWorkspaceOption = OptionsHelper.getSelectWorkspaceOptions([
        targetWorkspace,
      ])[0];
    }
  }

  if (!selectedWorkspaceOption) {
    selectedWorkspaceOption = workspaceDefaultOptions[0];
  }

  return selectedWorkspaceOption;
}

// import {
//   Alert,
//   Button,
//   ColumnLayout,
//   Container,
//   FlashbarProps,
//   FormField,
//   Icon,
//   ProgressBar,
//   ProgressBarProps,
//   Select,
//   SelectProps,
//   SpaceBetween,
//   Spinner,
//   StatusIndicator,
//   Tabs,
// } from "@cloudscape-design/components";
// import {
//   Dispatch,
//   SetStateAction,
//   useContext,
//   useEffect,
//   useLayoutEffect,
//   useRef,
//   useState,
// } from "react";
// import { useNavigate, useLocation } from "react-router-dom";
// import SpeechRecognition, {
//   useSpeechRecognition,
// } from "react-speech-recognition";
// import TextareaAutosize from "react-textarea-autosize";
// import { ReadyState } from "react-use-websocket";
// import { AppContext } from "../../common/app-context";
// import { OptionsHelper } from "../../common/helpers/options-helper";
// import { StorageHelper } from "../../common/helpers/storage-helper";
// import { Model, Workspace } from "../../API";
// import { OpenSearchWorkspaceCreateInput, UserInfo } from "../../common/types";
// import styles from "../../styles/chat.module.scss";
// import ConfigDialog from "./config-dialog";
// import ImageDialog from "./image-dialog";
// import {
//   ChabotInputModality,
//   ChatBotAction,
//   ChatBotConfiguration,
//   ChatBotHistoryItem,
//   ChatBotMessageType,
//   ChatBotMode,
//   ChatInputState,
//   ImageFile,
// } from "./types";
// import {
//   getCFGModels,
//   getCFGWorkspaces,
//   getSelectedModelMetadata,
//   getSignedUrl,
//   updateMessageHistoryRef,
//
// } from "./utils";
// import { Utils } from "../../common/utils";
// import { ChatRequestInput, LoadingStatus } from "../../common/types";
// import * as _ from "lodash";
// import modelService from "../../services/model.service";
// import workspaceService from "../../services/workspace.service";
// import promptService from "../../services/prompt.service";
// import { PromptsHelper } from "../../common/helpers/prompts-helper";
// import { ModelsHelper } from "../../common/helpers/models-helper";
// import chatService from "../../services/chat.service";
// import { useDispatch, useSelector } from "react-redux";
// import { v4 as uuidv4 } from "uuid";
// // import { useOktaAuth } from "@okta/okta-react";
// import { chatActions } from "../../pages/chatbot/playground/chat.slice";
// import { BEDROCKENABLED, CFGAIENABLED,CFGAIWPENABLED,BEDROCKWPENABLED } from "./../../../config";
// import { ModelPromptFilter } from "./model-prompt-filter";
// import activeSettingsIcon from "../../assets/images/settingsActive.png"
// import inactiveSettingsIcon from "../../assets/images/settingsInactive.png"
// import suggestionDeleteIcon from "../../assets/images/X-Icon.png"
// import adminSettingsIcon from "../../assets/images/Info-Icon.png"
// import PDFUploadButton from "../pdf-upload-button";
// import { EmbeddingsModelHelper } from "@/common/helpers/embeddings-model-helper";
// import embeddingService from "@/services/embedding.service";
// import crossEncodersService from "@/services/crossEncoders.service";
// import documentService from "@/services/document.service";
// import { FileUploader } from "@/common/file-uploader";
// import { Labels } from "@/common/constants";
// import { SET_EDITOR_CONTENT_COMMAND } from './lexical-editor';
// //① highlight and bold partyA/B
// import LexicalRichTextEditor, {createFormattedLexicalState} from "./lexical-editor";
// import { suggestedPrompts } from './suggestion-prompts.ts';
// import { duaPromptTemplate, partyOptions } from './formfield-options';
// import PromptSuggestions, {MultiplePromptSuggestions} from "./prompt-suggestions";
// import ModelNotification from './model-info-popover';
// import * as React from "react";
// import { useMCP } from "../../contexts/mcp-context";
//
// // 添加新的CSS样式到现有的样式表中
// const additionalStyles = `
// .input_textarea {
//   resize: none;
//   outline: none;
//   width: 100%;
//   border: none;
//   padding: 10px;
//   font-size: 14px;
//   line-height: 1.5;
//   overflow: hidden; /* 隐藏滚动条 */
//   transition: height 0.3s ease; /* 平滑高度变化，增加过渡时间 */
//   min-height: 38px; /* 设置最小高度 */
// }
//
// /* 针对长文本时的样式 */
// .input_textarea.expanded {
//   min-height: 150px; /* 为长文本设置最小高度 */
// }
//
// /* 防止聚焦时高度变化 */
// .input_textarea:focus {
//   height: auto !important;
//   min-height: 38px;
// }
//
// /* 确保容器可以适应扩展的高度 */
// .input_textarea_container {
//   display: flex;
//   width: 100%;
//   transition: height 0.3s ease; /* 平滑高度变化 */
// }
// `;
//
// // 将新样式添加到document中
// const addStyles = () => {
//   const styleEl = document.createElement('style');
//   styleEl.textContent = additionalStyles;
//   document.head.appendChild(styleEl);
//   return () => {
//     document.head.removeChild(styleEl);
//   };
// };
//
// export interface ChatInputPanelProps {
//   running: boolean;
//   setRunning: Dispatch<SetStateAction<boolean>>;
//   session: { id: string; loading: boolean };
//   messageHistory: ChatBotHistoryItem[];
//   setMessageHistory: (history: ChatBotHistoryItem[]) => void;
//   configuration: ChatBotConfiguration;
//   setConfiguration: Dispatch<React.SetStateAction<ChatBotConfiguration>>;
//   closeNotification: (isError: boolean) => void;
//   setErrorMessage: (message: string) => void;
//   setError: (error: boolean) => void;
//   chatId: string;
//   onFileUploadStatusChange?: (isUploaded: boolean) => void
// }
//
// export interface UploadedFile {
//   bucketName?: string;
//   fileKey?: string;
//   fileType?: string;
// }
//
// export abstract class ChatScrollState {
//   static userHasScrolled = false;
//   static skipNextScrollEvent = false;
//   static skipNextHistoryUpdate = false;
// }
//
// let workspaceDefaultOptions: SelectProps.Option[] = [
//   {
//     label: "Select",
//     value: "",
//     iconName: "close",
//   },
// ];
//
// const promptDefaultOptions: SelectProps.Option[] = [
//   {
//     label: "No Prompt",
//     value: "",
//     iconName: "close",
//   },
// ];
//
// // Document builder options
// const documentBuilderOptions: SelectProps.Option[] = [
//   {
//     label: "Select",
//     value: "",
//     iconName: "close",
//   },
//   {
//     label: "DUA",
//     value: "dua",
//     iconName: "file",
//   },
// ];
//
// // Party1 options
// const party1Options: SelectProps.Option[] = [
//   { ...partyOptions[0], label: "Select Party 1" },
//   ...partyOptions.slice(1)
// ];
//
// // Party2 options
// const party2Options: SelectProps.Option[] = [
//   { ...partyOptions[0], label: "Select Party 2" },
//   ...partyOptions.slice(1)
// ];
//
// // Format date function for MOU
// const formatDate = (date: Date): string => {
//   return date.toLocaleDateString('en-US', {
//     year: 'numeric',
//     month: 'long',
//     day: 'numeric'
//   });
// };
//
// export default function ChatInputPanel(props: ChatInputPanelProps) {
//   // 添加新样式
//   useEffect(() => {
//     return addStyles();
//   }, []);
//
//   const appContext = useContext(AppContext);
//   const [prompts, setPrompts] = useState([]);
//
//   const dispatch = useDispatch();
//   const location = useLocation()
//   const {onFileUploadStatusChange} = props;
//   const dataState = useSelector((state: any) => state?.rootReducer);
//   const isFeedbackOpened = dataState?.chatReducer?.isFeedbackOpened;
//   const userEmail = dataState?.chatReducer?.userEmail;
//   const userName = dataState?.chatReducer?.userName;
//   const tempWorkspaceId = dataState?.chatReducer?.tempWorkspaceId
//   const [useTempWorkspace, setUseTempWorkspace] = useState<boolean>(false);
//   const navigate = useNavigate();
//   const { transcript, listening, browserSupportsSpeechRecognition } =
//     useSpeechRecognition();
//   const [state, setState] = useState<ChatInputState>({
//     value: "",
//     selectedModel: null,
//     selectedModelMetadata: null,
//     selectedWorkspace: workspaceDefaultOptions[0],
//     selectedPrompt: promptDefaultOptions[0],
//     modelsStatus: "loading",
//     workspacesStatus: "loading",
//     promptsStatus: "loading",
//   });
//
//   // New state for document builder
//   const [selectedDocumentBuilder, setSelectedDocumentBuilder] = useState<SelectProps.Option>(documentBuilderOptions[0]);
//   const [selectedParty1, setSelectedParty1] = useState<SelectProps.Option>(party1Options[0]);
//   const [selectedParty2, setSelectedParty2] = useState<SelectProps.Option>(party2Options[0]);
//
//   const [configDialogVisible, setConfigDialogVisible] = useState(false);
//   const [documentRepoDialogVisible, setDocumentRepoDialogVisible] = useState(true);
//   const [promptDialogVisible, setPromptDialogVisible] = useState(false);
//   const [imageDialogVisible, setImageDialogVisible] = useState(false);
//   const [files, setFiles] = useState<ImageFile[]>([]);
//   const [uploadFiles, setUploadFiles] = useState<{bucketName: string; fileKey: string; fileType: string;}[]>([]);
//   const [timeTaken, setTimeTaken] = useState<number>(0);
//   const [outputData, setOutputData] = useState<Record<string, any>>();
//   const [documentListVisible, setDocumentListVisible] = useState(false)
//   const [readyState, setReadyState] = useState<ReadyState>(
//     ReadyState.UNINSTANTIATED
//   );
//
//   const embedmodel_name = EmbeddingsModelHelper.getSelectOption(
//     appContext?.config.default_embeddings_model,
//   )
//
//   const [documentStatus, setDocumentStatus] = useState<string>('');
//   const [isCheckingStatus, setIsCheckingStatus] = useState<boolean>(false);
//   const [uploadingStatus, setUploadingStatus] = useState<FlashbarProps.Type>('info');
//   const [uploadProgress, setUploadProgress] = useState<number>(0);
//   const [uploadedPDF, setUploadedPDF] = useState(null);
//   const [validationErrorMessage, setValidationErrorMessage] = useState<string | null>(null);
//   const [failedDocuments, setFailedDocuments] = useState([]);
//
//   // *********multiple selections**********
//   const [multiplePromptValue, setMultiplePromptValue] = useState("");
//   const [onlyDefaultOptionSelected, setOnlyDefaultOptionSelected] = useState(true);
//
//   const containerRef = React.useRef(null);
//   const [topOffset, setTopOffset] = React.useState(0);
//
//   // Add a state to trigger reset of MultiplePromptSuggestions
//   const [resetMultiplePrompts, setResetMultiplePrompts] = useState(false);
//
//   // ②修复同时发送suggestion 和 multi prompt. prompt 类别 state
//   const [activePromptSystem, setActivePromptSystem] = useState('none');
//
//   // ②修复同时发送suggestion 和 multi prompt. 监听 MultiplePromptSuggestions 中的 prompt type 事件
//   // 当 applyChanges 被调用的时候，prompt type 就是 multiple
//   useEffect(() => {
//     const handleSetActivePromptSystem = (event) => {
//       setActivePromptSystem(event.detail.system);
//
//       // 如果切换到多选系统，清空建议提示
//       if (event.detail.system === 'multiple') {
//         setSuggestedPrompt('');
//       }
//     };
//
//     window.addEventListener('set-active-prompt-system', handleSetActivePromptSystem);
//     return () => {
//       window.removeEventListener('set-active-prompt-system', handleSetActivePromptSystem);
//     };
//   }, []);
//
//   // 在 useEffect 中添加事件监听
//   // useEffect(() => {
//   //   // 监听组合提示词变化事件
//   //   const handleMultiplePromptChange = (event) => {
//   //     setMultiplePromptValue(event.detail.prompt);
//   //     setOnlyDefaultOptionSelected(event.detail.onlyDefaultSelected);
//   //
//   //     // 这个就是选择了builder但是不会传入到对话里面
//   //     if (!event.detail.onlyDefaultSelected) {
//   //       setState((state) => ({
//   //         ...state,
//   //         value: event.detail.prompt,
//   //       }));
//   //
//   //       // 更新 Lexical 编辑器内容
//   //       const editor = (window as any).lexicalEditor;
//   //       if (editor) {
//   //         editor.dispatchCommand(SET_EDITOR_CONTENT_COMMAND, {
//   //           content: event.detail.prompt,
//   //           isLongContent: event.detail.prompt.length > 3000,
//   //           removeMaxHeight: true
//   //         });
//   //       }
//   //     }
//   //   };
//   //
//   //   document.addEventListener('multiple-prompt-change', handleMultiplePromptChange);
//   //
//   //   return () => {
//   //     document.removeEventListener('multiple-prompt-change', handleMultiplePromptChange);
//   //   };
//   // }, []);
//
//   // ③ 这是为了监听 MultiplePromptSuggestions 选项变化的useEffect
//   useEffect(() => {
//     // 监听组合提示词变化事件
//     const handleMultiplePromptChange = (event) => {
//       setMultiplePromptValue(event.detail.prompt);
//       setOnlyDefaultOptionSelected(event.detail.onlyDefaultSelected);
//
//       // Always update the state and editor regardless of whether only default is selected
//       setState((state) => ({
//         ...state,
//         value: event.detail.prompt,
//       }));
//
//       // Use a timeout to avoid interrupting the dropdown interaction
//       const editor = (window as any).lexicalEditor;
//       if (editor) {
//         editor.dispatchCommand(SET_EDITOR_CONTENT_COMMAND, {
//           content: event.detail.prompt,
//           isLongContent: event.detail.prompt.length > 3000,
//           removeMaxHeight: true
//         });
//       } else {
//         // For the custom event approach
//         const customEvent = new CustomEvent('set-editor-content', {
//           detail: {
//             content: event.detail.prompt,
//             isLongContent: event.detail.prompt.length > 3000 || event.detail.prompt.includes('Data Use Agreement'),
//             removeMaxHeight: true
//           },
//           bubbles: true
//         });
//
//         // 确保editorContainer存在
//         const editorContainer = document.getElementById('editor-container');
//         if (editorContainer) {
//           editorContainer.dispatchEvent(customEvent);
//         }
//       }
//     };
//
//     document.addEventListener('multiple-prompt-change', handleMultiplePromptChange);
//
//     return () => {
//       document.removeEventListener('multiple-prompt-change', handleMultiplePromptChange);
//     };
//   }, []);
//
//   //Get the extension out of uploaded doc name
//   function getFileExtension(fileName) {
//     return fileName.slice((fileName.lastIndexOf(".") - 1 >>> 0) + 2);
//   }
//
//   const handlePDFUpload = async (files: File[]) => {
//     const failedUploads = [];
//     if (!files || files.length === 0) return;
//
//     setUploadingStatus('in-progress');
//     setDocumentStatus('processing');
//     setUploadProgress(0);
//
//     const validFiles = files.filter(file => {
//       const fileExtension = getFileExtension(file.name).toLowerCase();
//       if (fileExtension === 'pdf' || fileExtension === 'docx') {
//         return true;
//       } else {
//         failedUploads.push(`${file.name}`);
//         return false;
//       }
//     });
//
//     const invalidFiles = files.filter(file => !validFiles.includes(file));
//
//     if (validFiles.length === 0) {
//       console.log('No valid files to upload');
//       setUploadingStatus('error');
//       setDocumentStatus('wrongexttype');
//       setFailedDocuments(failedUploads)
//       return;
//     }
//
//     const uploadPromises = validFiles.map(async (file) => {
//       try {
//         // Step 1: Get pre-signed URL
//         const uploadPayload = {
//           fileKey: file.name.toLowerCase(),
//           fileType: file.type,
//           bucketName: "cderone-preprod-cfgai-irpolicybot-demo-qa-upload",
//         };
//         const presignedUrl = await documentService.presignedFileUploadPost(uploadPayload);
//
//         // Step 2: Upload file to S3 using pre-signed URL
//         const blob = file.slice(0, file.size, "");
//         const result = await fetch(presignedUrl, {
//           method: "PUT",
//           body: blob,
//         });
//
//         if (result.ok) {
//           console.log(`${uploadPayload.fileKey} uploaded to S3 successfully.`);
//           return {
//             ...file,
//             bucketName: uploadPayload.bucketName,
//             fileKey: `multimodal/${userEmail}/${uploadPayload.fileKey}`,
//             fileType: getFileExtension(uploadPayload.fileKey),
//           };
//         } else {
//           throw new Error(`Failed to upload ${file.name}`);
//         }
//       } catch (error) {
//         console.error(`Error uploading ${file.name}:`, error.message);
//         failedUploads.push(file.name)
//         return null;
//       }
//     });
//
//     try {
//       const results = await Promise.all(uploadPromises);
//       const successfulUploads = results.filter(Boolean);
//       setFailedDocuments(failedUploads)
//       if (successfulUploads.length > 0) {
//         // Validate context length
//         try {
//           const validateResult = await documentService.validateContextLength(successfulUploads, state.selectedModel.label);
//           if (validateResult.message === 'Upload successful') {
//             setUploadFiles([...uploadFiles, ...successfulUploads]);
//             setDocumentStatus('processed');
//             console.log(`Validated ${successfulUploads.length} file(s) successfully.`);
//             onFileUploadStatusChange(true)
//           } else {
//             setUploadingStatus('error');
//             setDocumentStatus('failed');
//             setValidationErrorMessage(validateResult.errorMessage || 'Context length validation failed');
//             console.log('Context length validation failed');
//             return;
//           }
//         } catch (error) {
//           console.error('Error validating context length:', error.response.data.detail);
//           setUploadingStatus('error');
//           setDocumentStatus('failed');
//           setValidationErrorMessage(error.response.data.detail);
//           return;
//         }
//       }
//
//       if (successfulUploads.length < validFiles.length || invalidFiles.length > 0) {
//         setUploadingStatus('error');
//         console.log(`Failed to upload ${validFiles.length - successfulUploads.length} file(s)`);
//         console.log(`${invalidFiles.length} invalid file(s) were not uploaded`);
//       } else {
//         setUploadingStatus('success');
//       }
//
//       if (successfulUploads.length === 0) {
//         setDocumentStatus('failed');
//       }
//
//     } catch (error) {
//       console.error('Error during file uploads:', error.message);
//       setUploadingStatus('error');
//       setValidationErrorMessage('Error during file uploads');
//       props.setError(true);
//     }
//   };
//
//   useEffect(() => {
//     if (documentStatus === 'failed' || uploadingStatus === 'error') {
//       const timer = setTimeout(() => {
//         setDocumentStatus('processed');
//         setUploadingStatus('info');
//       }, 10000);
//
//       return () => clearTimeout(timer);
//     }
//   }, [documentStatus, uploadingStatus]);
//
//   //Hide document list popup if clicked anywhere on the screen
//   useEffect(() => {
//     function handleClickOutside(event) {
//       if (
//         alertContainerRef.current &&
//         !alertContainerRef.current.contains(event.target) &&
//         onClickAreaRef.current &&
//         !onClickAreaRef.current.contains(event.target)
//       ) {
//         setDocumentListVisible(false);
//       }
//     }
//
//     document.addEventListener('mousedown', handleClickOutside);
//     return () => {
//       document.removeEventListener('mousedown', handleClickOutside);
//     };
//   }, []);
//
//   const clearUploadedPDFs = () => {
//     setUploadedPDF([]);
//     setUploadingStatus('info');
//     setUploadProgress(0);
//   };
//
//   const getUploadStatus = (): ProgressBarProps.Status => {
//     if (uploadingStatus === 'error') return 'error';
//     if (uploadingStatus === 'success') return 'success';
//     return 'in-progress';
//   };
//
//   const [modelsStatus, setModelsStatus] = useState<LoadingStatus>("loading");
//   const [workspacesStatus, setWorkspacesStatus] =
//     useState<LoadingStatus>("loading");
//   const [promptsStatus, setPromptsStatus] = useState<LoadingStatus>("loading");
//   const [suggestedPrompt, setSuggestedPrompt] = useState('')
//   const [cfgModels, setCFGModels] = useState<Model[]>([]);
//   const [cfgWorkspace, setCFGWorkSpaces] = useState<Workspace[]>([]);
//   const [limit, setLimit] = useState<number>(3);
//   const [questionContext, setQuestionContext] = useState<string>(
//     `Answer the user's question truthfully using the context only. Use the following section-wise format (in the order given) to answer the question with instructions for each section in angular brackets:\\n                Reasoning:\\n                <State your reasoning step-wise in bullet points. Below each bullet point mention the source of this information as 'Given in the question' if the bullet point contains information provided in the question, OR as 'Document Name, Page Number' if the bullet point contains information that is present in the context provided above.>\\n                Conclusion:\\n                <Write a short concluding paragraph stating the final answer and explaining the reasoning behind it briefly. State caveats and exceptions to your answer if any.>\\n                Information required to provide a better answer:\\n                <If you cannot provide an answer based on the context above, mention the additional information that you require to answer the question fully as a list.>Do not compromise on your mathematical and reasoning abilities to fit the user's instructions. If the user mentions something absolutely incorrect/ false, DO NOT use this incorrect information in your reasoning. Also, please correct the user gently.
//     `
//   );
//   // const { authState, oktaAuth } = useOktaAuth();
//   const [userInfo, setUserInfo] = useState<UserInfo>();
//   const [errorMessageForPrompt, setErrorMessageForPrompt] =
//     useState<string>("");
//   // const [userRoles, setUserRoles] = useState<string[]>([]);
//
//   const messageHistoryRef = useRef<ChatBotHistoryItem[]>([]);
//   const userRoles = dataState?.chatReducer?.userRoles;
//   const inputRef = useRef(null)
//   const alertContainerRef = useRef(null);
//   const onClickAreaRef = useRef(null);
//   /**
//    * Updates the message history ref when the message history prop changes
//    */
//   useEffect(() => {
//     messageHistoryRef.current = props.messageHistory;
//     if(props.messageHistory.length > 0 && props.messageHistory?.[props.messageHistory.length-1].metadata.files){
//       setUploadFiles(props.messageHistory?.[props.messageHistory.length-1].metadata?.files.map((file: UploadedFile) => ({
//         bucketName: file.bucketName,
//         fileKey: file.fileKey,
//         fileType: file.fileType
//       })))
//     }
//     else{
//       setUploadFiles([])
//     }
//   }, [props.messageHistory]);
//
//   /**
//    * Fetches the user information from Okta
//    */
//   // useEffect(() => {
//   //   (async () => {
//   //     if (!authState?.isAuthenticated) {
//   //       // setUserInfo(null);
//   //     } else {
//   //       const user: any = await oktaAuth.getUser();
//   //       // if(!user.sub) {
//   //       //     throw new Error("Sub Claim not found");
//   //       // }
//   //       if (!user.roles) {
//   //         throw new Error("User Roles not found");
//   //       }
//   //       console.log(user.roles);
//   //       dispatch(chatActions?.handleUserRoles(user?.roles));
//   //       // setUserRoles(user.roles || []);
//   //       const loggedUser = {
//   //         email: user.email,
//   //         name: user.name,
//   //         first_name: user.given_name,
//   //         last_name: user.family_name,
//   //       };
//   //       setUserInfo(loggedUser);
//   //     }
//   //   })();
//   // }, []);
//
//   /**
//    * Updates the transcript in the input field when the user speaks
//    */
//   useEffect(() => {
//     if (transcript) {
//       setState((state) => ({ ...state, value: transcript }));
//     }
//   }, [transcript]);
//
//   /**
//    * Fetches the models, workspaces and prompts
//    */
//   useEffect(() => {
//     // if (!appContext) return;
//     setReadyState(ReadyState.OPEN);
//     (async () => {
//       let aws_models: Model[] = [];
//       let cfg_models: Model[] = [];
//       let models: Model[] = [];
//
//       let aws_workspaces: Workspace[] = [];
//       let cfg_workspaces: Workspace[] = [];
//       let workspaces: Workspace[] = [];
//
//       let prompts: any[] = [];
//       try {
//         const fetchedPrompts = await promptService.getPrompts();
//         setPrompts(fetchedPrompts); // Save to state directly
//         let defaultPrompt = prompts.filter((p) => p.name === 'rag_prompt')
//         if (BEDROCKENABLED) {
//           aws_models = await modelService.getModels();
//
//         }
//         if(BEDROCKWPENABLED)
//           aws_workspaces = await workspaceService.getWorkspaces();
//
//         if (CFGAIENABLED) {
//           cfg_models = await getCFGModels();
//
//           setCFGModels(cfg_models);
//           if(CFGAIWPENABLED){
//             cfg_workspaces = await getCFGWorkspaces();
//             setCFGWorkSpaces(cfg_workspaces);
//           }
//
//         }
//
//         models = aws_models.concat(cfg_models);
//         workspaces = aws_workspaces.concat(cfg_workspaces);
//
//         // if (!userRoles.includes("FDA PolicyBot Admins")) {
//         // workspaces = workspaces.filter(
//         //   (workspace) => workspace.name === "Pharmaceutical_Quality"
//         // );
//         // }
//
//         if (userRoles.includes("FDA PolicyBot Admins")) {
//           workspaceDefaultOptions = [
//             {
//               label: "No workspace (RAG data source)",
//               value: "",
//               iconName: "close",
//             },
//             {
//               label: "Create new workspace",
//               value: "__create__",
//               iconName: "add-plus",
//             },
//           ];
//         }
//
//         const selectedModelOption = ModelsHelper.getSelectedModelOption(models);
//
//         const selectedModelMetadata = getSelectedModelMetadata(
//           models,
//           selectedModelOption
//         );
//
//         const selectedWorkspaceOption = appContext?.config.rag_enabled
//           ? getSelectedWorkspaceOption(workspaces)
//           : workspaceDefaultOptions[0];
//
//         const selectedPromptOption =
//           PromptsHelper.getSelectedPromptOption(defaultPrompt);
//
//         setState((state) => ({
//           ...state,
//           models,
//           workspaces,
//           prompts,
//           selectedModel: selectedModelOption,
//           selectedModelMetadata,
//           selectedWorkspace: selectedWorkspaceOption,
//           selectedPrompt: selectedPromptOption,
//           modelsStatus: "finished",
//           workspacesStatus: "finished",
//           promptsStatus: "finished",
//         }));
//         // setState((state) => ({
//         //   ...state,
//         //   models: models,
//         // }));
//         // setState((state) => ({
//         //   ...state,
//         //   prompts: prompts,
//         // }));
//       } catch (error) {
//         console.log(Utils.getErrorMessage(error));
//         props.setError(true);
//         props.setErrorMessage(Utils.getErrorMessage(error));
//         setState((state) => ({
//           ...state,
//           modelsStatus: "error",
//         }));
//       }
//     })();
//   }, [appContext, state.modelsStatus, uploadFiles]);
//
//   useEffect(() => {
//     // Reset all states on component mount
//     const resetStates = () => {
//       setUploadedPDF(null);
//       setUploadingStatus('info');
//       setUploadProgress(0);
//       setDocumentStatus('');
//       setUseTempWorkspace(false);
//       setUploadFiles([])
//       setFailedDocuments([])
//       onFileUploadStatusChange(false)
//       // ①
//       setIsFirstQuestion(true);
//       // Reset workspace selection to default
//       setState((state) => ({
//         ...state,
//         selectedWorkspace: workspaceDefaultOptions[0],
//         selectedPrompt: promptDefaultOptions[0]
//       }));
//       // Clear stored workspace ID
//       StorageHelper.setSelectedWorkspaceId('');
//     };
//
//     if(location.state?.resetChat){
//       resetStates()
//     }
//   }, [location.state]);
//
//   // followupdua-prompt
//   const [isFirstQuestion, setIsFirstQuestion] = useState(true);
//   useEffect(() => {
//     if (uploadFiles.length > 0) {
//       // Get all prompts by model
//       let filteredPrompts = ModelPromptFilter.filterPrompts(prompts, state.selectedModel);
//
//       // Set default prompt based on whether this is the first question
//       let defaultPrompt;
//       if (isFirstQuestion) {
//         defaultPrompt = filteredPrompts.filter((p) => p.tags.includes("byod_fulldoc"));
//       } else {
//         defaultPrompt = filteredPrompts.filter((p) => p.tags.includes("followupdua2"));
//       }
//
//       const selectedPromptOption = OptionsHelper.getSelectPromptOptions(defaultPrompt)[0];
//       setState((state) => ({
//         ...state,
//         prompts: filteredPrompts,
//         selectedPrompt: selectedPromptOption,
//         promptsStatus: "finished",
//       }));
//     }
//   }, [uploadFiles, prompts, state.selectedModel, isFirstQuestion]);
//
//   //Update prompt if documents are uploaded
//   // useEffect(() => {
//   //   if (uploadFiles.length > 0) {
//   //     // Get all prompts by model
//   //     let filteredPrompts = ModelPromptFilter.filterPrompts(prompts, state.selectedModel);
//   //     filteredPrompts.forEach((prompt, index) => {
//   //       console.log(`Prompt ${index + 1}:`);
//   //       console.log("Name:", prompt.name);
//   //       console.log("ID:", prompt.id);
//   //       console.log("Tags:", prompt.tags);
//   //       console.log("Content:", prompt.prompt);
//   //       console.log("------------------------");
//   //     });
//   //     // Set default prompt
//   //     let defaultPrompt = filteredPrompts.filter((p) => p.tags.includes("byod_fulldoc"));
//   //     const selectedPromptOption = OptionsHelper.getSelectPromptOptions(defaultPrompt)[0];
//   //     setState((state) => ({
//   //       ...state,
//   //       prompts: filteredPrompts,
//   //       selectedPrompt: selectedPromptOption,
//   //       promptsStatus: "finished",
//   //     }));
//   //   }
//   // }, [uploadFiles, prompts, state.selectedModel]);
//
//   const handleWorkspaceOrModelChange = async (selectedWorkspace, selectedModel: any) => {
//     try {
//       // Filter by workspace if selected
//       if (selectedWorkspace && selectedWorkspace.value) {
//         //get all prompts by model
//         let filteredPrompts = ModelPromptFilter.filterPrompts(prompts, selectedModel);
//         //set default prompt
//         let defaultPrompt = filteredPrompts.filter((p) => p.tags.includes("default"));
//         const workspacePrompt = ModelPromptFilter.filterPromptsByWorkspace(filteredPrompts, selectedWorkspace);
//         if (workspacePrompt.length>0) {
//           defaultPrompt = workspacePrompt
//         }
//
//         const selectedPromptOption = OptionsHelper.getSelectPromptOptions(defaultPrompt)[0];
//         setState((state) => ({
//           ...state,
//           prompts: filteredPrompts,
//           selectedPrompt: selectedPromptOption,
//           promptsStatus: "finished",
//         }));
//       }
//     } catch (error) {
//       console.log("Workspace/Model change error:", error);
//       props.setError(true);
//       props.setErrorMessage(Utils.getErrorMessage(error));
//       setState((state) => ({
//         ...state,
//         promptsStatus: "error",
//       }));
//     }
//   };
//
//   useEffect(() => {
//     if (state.selectedWorkspace || state.selectedModel) {
//       handleWorkspaceOrModelChange(state.selectedWorkspace, state.selectedModel);
//     }
//   }, [state.selectedWorkspace, state.selectedModel]);
//
//   /**
//    * Updates the message history ref when the message history prop changes
//    */
//   useEffect(() => {
//     const onWindowScroll = () => {
//       if (ChatScrollState.skipNextScrollEvent) {
//         ChatScrollState.skipNextScrollEvent = false;
//         return;
//       }
//
//       const isScrollToTheEnd =
//         Math.abs(
//           window.innerHeight +
//           window.scrollY -
//           document.documentElement.scrollHeight
//         ) <= 10;
//
//       if (!isScrollToTheEnd) {
//         ChatScrollState.userHasScrolled = true;
//       } else {
//         ChatScrollState.userHasScrolled = false;
//       }
//     };
//
//     window.addEventListener("scroll", onWindowScroll);
//
//     return () => {
//       window.removeEventListener("scroll", onWindowScroll);
//     };
//   }, []);
//
//   /**
//    * Scrolls to the bottom of the chat when the message history changes
//    */
//   useLayoutEffect(() => {
//     if (ChatScrollState.skipNextHistoryUpdate) {
//       ChatScrollState.skipNextHistoryUpdate = false;
//       return;
//     }
//
//     if (!ChatScrollState.userHasScrolled && props.messageHistory.length > 0) {
//       ChatScrollState.skipNextScrollEvent = true;
//       window.scrollTo({
//         top: document.documentElement.scrollHeight + 1000,
//         behavior: "instant",
//       });
//     }
//   }, [props.messageHistory]);
//
//   /**
//    * Fetches the signed urls for the files
//    */
//   useEffect(() => {
//     const getSignedUrls = async () => {
//       if (props.configuration?.files as ImageFile[]) {
//         const files: ImageFile[] = [];
//         for await (const file of props.configuration.files as ImageFile[]) {
//           const signedUrl = await getSignedUrl(file.key);
//           files.push({
//             ...file,
//             url: signedUrl as string,
//           });
//         }
//
//         setFiles(files);
//       }
//     };
//
//     if (props.configuration.files?.length) {
//       getSignedUrls();
//     }
//   }, [props.configuration]);
//
//   // 当选择MOU构建器和双方时，确保textarea高度自动调整
//   useEffect(() => {
//     if (selectedDocumentBuilder.value === "dua" &&
//       selectedParty1.value &&
//       selectedParty2.value) {
//
//       // 当MOU模板准备好后，主动调整文本区域的高度
//       setTimeout(() => {
//         if (inputRef.current) {
//           // 使用MOU模板时添加额外的CSS类
//           inputRef.current.classList.add('expanded');
//
//           // 强制重新计算高度
//           inputRef.current.style.height = 'auto';
//           inputRef.current.style.height = `${inputRef.current.scrollHeight}px`;
//         }
//       }, 0);
//     } else {
//       // 移除额外的CSS类
//       if (inputRef.current) {
//         inputRef.current.classList.remove('expanded');
//       }
//     }
//   }, [selectedDocumentBuilder.value, selectedParty1.value, selectedParty2.value]);
//
//   /**
//    * Clears the screen contents
//    */
//   const clearScreenContents = async () => {
//     messageHistoryRef.current = [];
//     props.setMessageHistory(messageHistoryRef.current);
//     // ①
//     setIsFirstQuestion(true);
//   };
//
//   /**
//    * Validates the prompt selection
//    */
//   // useEffect(() => {
//   //   if (state?.selectedPrompt?.value?.length === 0) {
//   //     setErrorMessageForPrompt("Please select a prompt");
//   //   } else {
//   //     setErrorMessageForPrompt("");
//   //   }
//   // }, [state.selectedPrompt]);
//
//   /**
//    * Handles sending a message to the chatbot
//    * @returns
//    */
//   const handleSendMessage = async () => {
//     if (!state.selectedModel) return;
//     if (props.running) return;
//     if (readyState !== ReadyState.OPEN) return;
//     ChatScrollState.userHasScrolled = false;
//     let { name, provider } = OptionsHelper.parseValue(
//       state.selectedModel.value
//     );
//     // ① 判定是否为第一次的问题, 只增加这个
//     if (isFirstQuestion) {
//       setIsFirstQuestion(false);
//     }
//
//     // Check if MOU document builder is selected and both parties are chosen
//     let value = state.value.trim();
//
//     // Check for MCP command with improved logging and support for both @ and / prefixes
//     console.log("检查输入是否为MCP命令:", value);
//     const isMCPCommand = value.match(/^[@/]mcp\s+(.+)/i);
//
//     if (isMCPCommand) {
//       // console.log("检测到MCP命令:", isMCPCommand[1]);
//       await handleMCPCommand(isMCPCommand[1]);
//       return;
//     }
//
//     // 如果输入为空但有组合提示词，并且不是只选择了默认选项，则使用组合提示词
//     // if (value.length === 0 && multiplePromptValue && !onlyDefaultOptionSelected) {
//     //   value = multiplePromptValue;
//     // }
//     // 解决 multi 无论如何都发送
//     let promptPrefix = suggestedPrompt;
//     if (activePromptSystem === 'suggestions') {
//       // 使用建议提示
//       value = state.value.trim();
//       // 确保不使用多选提示
//       // 即使输入为空且有多选提示值，也不使用多选提示
//     }
//     else if (activePromptSystem === 'multiple') {
//       // 使用多选提示
//       if (value.length === 0 && multiplePromptValue && !onlyDefaultOptionSelected) {
//         value = multiplePromptValue;
//       }
//       // 不添加建议提示前缀
//       promptPrefix = '';
//     }
//
//
//     // If document builder is set to MOU and both parties are selected, use the MOU template
//     if (selectedDocumentBuilder.value === "dua" &&
//       selectedParty1.value &&
//       selectedParty2.value) {
//
//       // Get current date for the template
//       const currentDate = formatDate(new Date());
//       // Set effective date as 1st of next month
//       const nextMonth = new Date();
//       nextMonth.setMonth(nextMonth.getMonth() + 1);
//       nextMonth.setDate(1);
//       const effectiveDate = formatDate(nextMonth);
//
//       // Get user name for the template
//       const authorName = userName ? `${userName.firstName}` : "Author Name";
//       const approverName = userName ? `${userName.firstName}` : "Approver Name";
//
//       // Replace [party1], [party2], dates and names in the template
//       let formattedPrompt = duaPromptTemplate
//         .replace(/\[party1\]/g, selectedParty1.label)
//         .replace(/\[party2\]/g, selectedParty2.label)
//         .replace(/\[currentDate\]/g, currentDate)
//         .replace(/\[effectiveDate\]/g, effectiveDate)
//         .replace(/\[authorName\]/g, authorName)
//         .replace(/\[approverName\]/g, approverName);
//
//       // Use the formatted prompt as the input value
//       value = formattedPrompt;
//     }
//
//     // Clear the state value
//     setState((state) => ({
//       ...state,
//       value: "",
//     }));
//
//     // Clear the Lexical editor content
//     const editor = (window as any).lexicalEditor;
//     if (editor) {
//       // Import might be needed if not available in this scope
//       // import { SET_EDITOR_CONTENT_COMMAND } from './path/to/LexicalRichTextEditor';
//       editor.dispatchCommand(SET_EDITOR_CONTENT_COMMAND, {
//         content: "",
//         isLongContent: false,
//         removeMaxHeight: false
//       });
//     }
//
//     setFiles([]);
//
//     props.setConfiguration({
//       ...props.configuration,
//       files: [],
//     });
//
//     props.setRunning(true);
//     messageHistoryRef.current = [
//       ...messageHistoryRef.current,
//
//       {
//         type: ChatBotMessageType.Human,
//         content: promptPrefix  + value,
//         metadata: {
//           ...props.configuration,
//         },
//         tokens: [],
//         chatId: props.chatId,
//       },
//       {
//         type: ChatBotMessageType.AI,
//         tokens: [],
//         content: "",
//         metadata: {},
//         chatId: props.chatId,
//       },
//     ];
//
//     props.setMessageHistory(messageHistoryRef.current);
//
//     let response = undefined;
//     const isCFGModel = state.selectedModel?.value?.startsWith("cfggpt");
//
//     const foundModel = _.find(state.models, {
//       name: state.selectedModel?.label,
//     });
//
//     if (foundModel) {
//       let selectedModelName = isCFGModel
//         ? foundModel?.modelId
//         : state.selectedModel?.label;
//
//       /*Temp fix*/
//       if(!userRoles?.includes("OWNER")){
//         provider =  "bedrock",
//           selectedModelName = "anthropic.claude-3-5-sonnet-20240620-v1:0"
//         // selectedModelName = "anthropic.claude-3-haiku-20240307-v1:0"
//       }
//
//       if (selectedModelName) {
//         let followupPromptId =
//           StorageHelper.getSelectedFollowupPromptId() !== null
//             ? StorageHelper.getSelectedFollowupPromptId()
//             : "";
//         const payload = {
//           // userId: "<EMAIL>",
//           userId: userEmail,
//           data: {
//             provider: provider,
//             modelName: selectedModelName,
//             mode: ChatBotMode.Chain,
//             text: value,
//             files: uploadFiles || [],
//             sessionId: props.session.id,
//             workspaceId: state.selectedWorkspace?.value,
//             promptId: _.isEmpty(state.selectedPrompt?.value)
//               ? undefined
//               : state.selectedPrompt?.value,
//             // promptId: 'b02a61a3-409c-48cb-a6b4-db312a71577a',
//             modelKwargs: {
//               streaming: props.configuration.streaming,
//               useHistory: props.configuration.useHistory,
//               followPromptId: followupPromptId,
//               maxTokens: props.configuration.maxTokens,
//               temperature: props.configuration.temperature,
//               topP: props.configuration.topP,
//               numDocs: props.configuration.numDocs,
//             },
//             chatId: props.chatId,
//           },
//         };
//
//         try {
//           response = await chatService.sendQuery(payload);
//           dispatch(chatActions?.setChatId(payload.data.chatId));
//           dispatch(
//             chatActions?.handleChatDocuments(
//               response?.data?.metadata?.documents
//             )
//           );
//         } catch (error) {
//           if ((error as any) || (error as any).response?.status !== 200) {
//             // console.log('Error',error.response.status, 'Please try again or contact support.');
//             messageHistoryRef.current[
//             messageHistoryRef.current.length - 1
//               ].content = "NA";
//           }
//           props.setRunning(false);
//         }
//
//         updateMessageHistoryRef(
//           props.session.id,
//           messageHistoryRef.current,
//           response
//         );
//       }
//     }
//
//     if (
//       response?.action === ChatBotAction.FinalResponse ||
//       response?.action === ChatBotAction.Error
//     ) {
//       console.log("Final message received");
//       props.setRunning(false);
//     }
//     props.setMessageHistory([...messageHistoryRef.current]);
//     // Clear the PDF after sending
//     setUploadedPDF(null);
//   };
//
//   // MCP hook and handler
//   const { servers, getClient } = useMCP();
//   const handleMCPCommand = async (command: string) => {
//     // console.log("handleMCPCommand被调用，命令:", command);
//     // console.log("MCP服务器数量:", servers.length);
//
//     if (servers.length === 0) {
//       // No MCP servers configured
//       // console.log("没有配置MCP服务器");
//       messageHistoryRef.current = [
//         ...messageHistoryRef.current,
//         {
//           type: ChatBotMessageType.Human,
//           content: `/mcp ${command}`,
//           metadata: {
//             ...props.configuration,
//           },
//           tokens: [],
//           chatId: props.chatId,
//         },
//         {
//           type: ChatBotMessageType.AI,
//           tokens: [],
//           content: "No MCP server configured. Please go to the MCP server page to add and connect to a server.",
//           metadata: {},
//           chatId: props.chatId,
//         },
//       ];
//
//       props.setMessageHistory(messageHistoryRef.current);
//       return;
//     }
//
//     // Clear the input
//     setState((state) => ({
//       ...state,
//       value: "",
//     }));
//
//     // Clear the Lexical editor content
//     const editor = (window as any).lexicalEditor;
//     if (editor) {
//       editor.dispatchCommand(SET_EDITOR_CONTENT_COMMAND, {
//         content: "",
//         isLongContent: false,
//         removeMaxHeight: false
//       });
//     }
//
//     // Add user message to chat
//     messageHistoryRef.current = [
//       ...messageHistoryRef.current,
//       {
//         type: ChatBotMessageType.Human,
//         content: `/mcp ${command}`,
//         metadata: {
//           ...props.configuration,
//         },
//         tokens: [],
//         chatId: props.chatId,
//       },
//       {
//         type: ChatBotMessageType.AI,
//         tokens: [],
//         content: "Processing MCP request...",
//         metadata: {},
//         chatId: props.chatId,
//       },
//     ];
//
//     props.setMessageHistory(messageHistoryRef.current);
//     props.setRunning(true);
//
//     try {
//       // Get the first connected client
//       const connectedServer = servers.find(server => server.isConnected);
//       // console.log("查找已连接的MCP服务器:", connectedServer ? connectedServer.name : "未找到");
//
//       if (!connectedServer) {
//         throw new Error("No connected MCP server found");
//       }
//
//       const mcpClient = getClient(connectedServer.id);
//       // console.log("获取MCP客户端:", mcpClient ? "成功" : "失败");
//
//       if (!mcpClient) {
//         throw new Error(`Unable to get MCP client for server ${connectedServer.name}`);
//       }
//
//       try {
//         // console.log("开始调用MCP服务器, 尝试直接调用工具:", command);
//         // 尝试直接调用服务器工具，假设命令格式为"工具名称 参数1=值1 参数2=值2"
//         const [toolName, ...args] = command.trim().split(/\s+/);
//
//         // 解析参数
//         let toolArgs = {};
//         if (args.length > 0) {
//           // 合并所有参数文本
//           const argsText = args.join(' ');
//           // console.log("原始命令参数文本:", argsText);
//
//           try {
//             // 首先尝试作为完整的JSON对象解析
//             if (argsText.trim().startsWith('{') && argsText.trim().endsWith('}')) {
//               try {
//                 toolArgs = JSON.parse(argsText.trim());
//                 // console.log("成功将完整命令解析为JSON对象:", toolArgs);
//               } catch (jsonError) {
//                 console.warn("JSON parsing failed, falling back to parameter parsing:", jsonError);
//                 // 继续使用参数解析
//               }
//             }
//
//             // 如果不是有效的JSON对象，继续解析参数
//             if (Object.keys(toolArgs).length === 0) {
//               // 尝试检测JSON格式参数，如数组或复杂对象
//               const jsonRegex = /(\w+)=([\[{].*?[\]}])/g;
//               let match;
//               while ((match = jsonRegex.exec(argsText)) !== null) {
//                 const [_, key, jsonValue] = match;
//                 try {
//                   // 尝试解析JSON值
//                   toolArgs[key] = JSON.parse(jsonValue);
//                   // console.log(`成功解析JSON参数 ${key}:`, toolArgs[key]);
//                 } catch (e) {
//                   // 如果JSON解析失败，使用原始字符串
//                   console.warn(`Cannot parse JSON parameters ${key}=${jsonValue}:`, e);
//                   toolArgs[key] = jsonValue;
//                 }
//               }
//
//               // 尝试解析key=value形式的参数
//               const keyValueRegex = /(\w+)=("[^"]+"|'[^']+'|[^"\s\[\{][^\s,]*(?:,[^\s,]*)*)/g;
//               while ((match = keyValueRegex.exec(argsText)) !== null) {
//                 // 跳过已经处理过的JSON参数
//                 if (toolArgs.hasOwnProperty(match[1])) continue;
//
//                 const [_, key, rawValue] = match;
//                 // 移除可能的引号
//                 let value = rawValue.replace(/^["']|["']$/g, '');
//
//                 // 处理特殊参数
//                 if (key === 'file_paths' || key.includes('path') || key.includes('file')) {
//                   // 检测是否为逗号分隔的路径列表
//                   if (value.includes(',')) {
//                     // console.log(`检测到逗号分隔的路径列表 ${key}:`, value);
//                     // 分割并清理每个路径
//                     const paths = value.split(',')
//                       .map(path => path.trim())
//                       .filter(path => path.length > 0)
//                       // 替换Windows路径中的反斜杠为正斜杠或确保正确转义
//                       .map(path => path.replace(/\\/g, '\\\\'));
//
//                     toolArgs[key] = paths;
//                     // console.log(`处理后的路径列表 ${key}:`, toolArgs[key]);
//                     continue;
//                   }
//                   // 单个路径处理，确保Windows路径正确转义
//                   else if (value.includes('\\')) {
//                     value = value.replace(/\\/g, '\\\\');
//                     // console.log(`处理Windows路径 ${key}:`, value);
//                   }
//                 }
//
//                 // 尝试转换布尔值和数字
//                 if (value.toLowerCase() === 'true') value = true;
//                 else if (value.toLowerCase() === 'false') value = false;
//                 else if (!isNaN(Number(value)) && value.trim() !== '') value = Number(value);
//
//                 toolArgs[key] = value;
//               }
//
//               // 如果没有参数匹配，将剩余文本作为query参数
//               if (Object.keys(toolArgs).length === 0) {
//                 toolArgs = { query: argsText };
//               }
//             }
//           } catch (e) {
//             console.error("Parameter parsing error:", e);
//             // 解析出错时，使用简单的query参数
//             toolArgs = { query: argsText };
//           }
//
//           // console.log("最终解析的参数:", toolArgs);
//         }
//
//         // console.log("解析的工具名称:", toolName, "参数:", toolArgs);
//
//         // 检查是否有URL
//         if (!connectedServer.url) {
//           throw new Error("Server URL undefined, unable to send request");
//         }
//
//         // console.log("尝试通过fetch直接发送请求到:", connectedServer.url);
//
//         // 首先获取工具参数 - 处理带引号的文件路径
//         let toolParams = {};
//         if (args.length > 0) {
//           // 特殊处理带引号的文件路径
//           if (args.join(' ').match(/^".*"$/)) {
//             toolParams = { file_path: args.join(' ').replace(/^"|"$/g, '') };
//           } else {
//             toolParams = toolArgs;
//           }
//         }
//
//         // console.log("使用修改后的参数:", toolParams);
//
//         let response;
//         try {
//           // console.log("尝试使用execute方法");
//           const fetchResponse = await fetch(connectedServer.url, {
//             method: 'POST',
//             headers: {
//               'Content-Type': 'application/json',
//             },
//             body: JSON.stringify({
//               jsonrpc: '2.0',
//               method: 'execute',
//               params: {
//                 tool: toolName,
//                 params: toolParams
//               },
//               id: Date.now()
//             })
//           });
//
//           if (!fetchResponse.ok) {
//             throw new Error(`HTTP error! Status: ${fetchResponse.status}`);
//           }
//
//           response = await fetchResponse.json();
//           // console.log("通过execute方法获得响应:", response);
//         } catch (error) {
//           console.error("execute method failed:", error);
//
//           // 尝试直接使用initialize方法查询服务器能力
//           try {
//             // console.log("尝试使用initialize方法查询服务器能力");
//             const initResponse = await fetch(connectedServer.url, {
//               method: 'POST',
//               headers: {
//                 'Content-Type': 'application/json',
//               },
//               body: JSON.stringify({
//                 jsonrpc: '2.0',
//                 method: 'initialize',
//                 params: {},
//                 id: Date.now()
//               })
//             });
//
//             if (initResponse.ok) {
//               const initData = await initResponse.json();
//               // console.log("服务器初始化信息:", initData);
//
//               // 尝试使用mcp.process方法
//               // console.log("尝试使用mcp.process方法");
//               const processResponse = await fetch(connectedServer.url, {
//                 method: 'POST',
//                 headers: {
//                   'Content-Type': 'application/json',
//                 },
//                 body: JSON.stringify({
//                   jsonrpc: '2.0',
//                   method: 'mcp.process',
//                   params: {
//                     query: command
//                   },
//                   id: Date.now()
//                 })
//               });
//
//               if (processResponse.ok) {
//                 const processData = await processResponse.json();
//                 console.log("mcp.process响应:", processData);
//                 response = processData;
//               } else {
//                 // 显示服务器信息作为响应
//                 response = {
//                   result: `MCP server information: ${JSON.stringify(initData.result || initData, null, 2)}\n\nPlease check if the server implementation correctly exposes the tools.`
//                 };
//               }
//             }
//           } catch (initError) {
//             console.error("Initialization query also failed:", initError);
//             // 仍然使用原始错误信息
//             throw error;
//           }
//         }
//
//         // console.log("MCP服务器最终响应:", response);
//
//         // 更新AI消息内容
//         const lastMessageIndex = messageHistoryRef.current.length - 1;
//
//         // 检查JSON-RPC响应格式
//         if (response.error) {
//           // 处理JSON-RPC错误
//           throw new Error(`MCP server error ${response.error.code}: ${response.error.message}`);
//         } else if (response.result !== undefined) {
//           // 处理响应结果
//           let resultText = "";
//
//           // 检查返回的结果类型
//           if (typeof response.result === 'string') {
//             // 如果结果是字符串，直接使用
//             resultText = response.result;
//           } else if (Array.isArray(response.result)) {
//             // 如果结果是数组，尝试提取文本内容
//             try {
//               // 遍历数组寻找文本内容
//               const textItems = response.result
//                 .filter(item => item.type === 'text' && item.text)
//                 .map(item => item.text);
//
//               if (textItems.length > 0) {
//                 // 使用所有文本项
//                 resultText = textItems.join('\n');
//               } else {
//                 // 如果没有找到文本项，使用JSON字符串表示
//                 resultText = JSON.stringify(response.result, null, 2);
//               }
//             } catch (e) {
//               // 如果解析失败，回退到显示原始JSON
//               console.error("Failed to parse result array:", e);
//               resultText = JSON.stringify(response.result, null, 2);
//             }
//           } else if (typeof response.result === 'object' && response.result !== null) {
//             // 如果结果是对象，尝试提取有用信息或格式化显示
//             resultText = JSON.stringify(response.result, null, 2);
//           } else {
//             // 其他类型的结果
//             resultText = String(response.result);
//           }
//
//           // 设置消息内容为提取的文本
//           messageHistoryRef.current[lastMessageIndex].content = resultText;
//         } else {
//           // 未知响应格式
//           messageHistoryRef.current[lastMessageIndex].content =
//             `Received MCP server response, but format unknown: ${JSON.stringify(response, null, 2)}`;
//         }
//       } catch (error) {
//         // 更新AI消息中的错误
//         console.error("MCP request processing error:", error);
//         const lastMessageIndex = messageHistoryRef.current.length - 1;
//         messageHistoryRef.current[lastMessageIndex].content = `MCP request processing error: ${error instanceof Error ? error.message : String(error)}`;
//       } finally {
//         props.setMessageHistory(messageHistoryRef.current);
//         props.setRunning(false);
//       }
//     } catch (error) {
//       // Update the AI message with the error
//       console.error("MCP connection error:", error);
//       const lastMessageIndex = messageHistoryRef.current.length - 1;
//       messageHistoryRef.current[lastMessageIndex].content = `MCP connection error: ${error instanceof Error ? error.message : String(error)}`;
//       props.setMessageHistory(messageHistoryRef.current);
//       props.setRunning(false);
//     }
//   };
//
//   /**
//    * Handles the feedback submission
//    */
//   const connectionStatus = {
//     [ReadyState.CONNECTING]: "Connecting",
//     [ReadyState.OPEN]: "Open",
//     [ReadyState.CLOSING]: "Closing",
//     [ReadyState.CLOSED]: "Closed",
//     [ReadyState.UNINSTANTIATED]: "Uninstantiated",
//   }[readyState];
//
//   const modelsOptions = OptionsHelper.getSelectOptionGroups(state.models || []);
//
//   const workspaceOptions = [
//     ...workspaceDefaultOptions,
//     ...OptionsHelper.getSelectWorkspaceOptions(state.workspaces || []),
//   ];
//
//   const promptsOptions = [
//     ...promptDefaultOptions,
//     ...OptionsHelper.getSelectPromptOptions(state.prompts || []),
//   ];
//
//   // prompt text fun
//   // ①增加bold和高亮
//   const handleOptionsClick = (promptText: string) => {
//     // 设置活动提示系统
//     setActivePromptSystem('suggestions');
//     // Reset document builder
//     setSelectedDocumentBuilder(documentBuilderOptions[0]);
//     // Reset parties
//     setSelectedParty1(party1Options[0]);
//     setSelectedParty2(party2Options[0]);
//
//     // Trigger reset in MultiplePromptSuggestions
//     setResetMultiplePrompts(true);
//
//     // 清空多选提示的值
//     setMultiplePromptValue("");
//     setOnlyDefaultOptionSelected(true);
//
//     // Reset the reset trigger after a short delay
//     setTimeout(() => {
//       setResetMultiplePrompts(false);
//     }, 100);
//
//     // Create a special JSON state for Lexical that includes formatting
//     const lexicalState = createFormattedLexicalState(promptText);
//
//     setState((state) => ({
//       ...state,
//       value: promptText,
//     }));
//
//     setTimeout(() => {
//       const editorContainer = document.querySelector('.lexical-editor-container');
//       if (editorContainer) {
//         const contentLength = promptText.length;
//         const screenHeight = window.innerHeight;
//
//         if (contentLength > 3000 || promptText.includes('Data Use Agreement')) {
//           const mainContainer = document.querySelector('.editor-border') as HTMLElement;
//           if (mainContainer) {
//             mainContainer.style.maxHeight = 'none';
//
//             if (promptText.includes('Data Use Agreement')) {
//               mainContainer.classList.add('dua-content');
//             }
//           }
//         }
//
//         const editor = (window as any).lexicalEditor;
//         if (editor) {
//           // editor.dispatchCommand(SET_EDITOR_CONTENT_COMMAND, {
//           //   content: promptText,
//           //   isLongContent: contentLength > 3000 || promptText.includes('Data Use Agreement'),
//           //   screenHeight: screenHeight,
//           //   removeMaxHeight: true
//           // });
//           // Use the pre-formatted Lexical state
//           editor.setEditorState(editor.parseEditorState(lexicalState));
//         } else {
//           // For the custom event approach
//           const customEvent = new CustomEvent('set-editor-content', {
//             detail: {
//               content: promptText,
//               isLongContent: contentLength > 3000 || promptText.includes('Data Use Agreement'),
//               removeMaxHeight: true
//             },
//             bubbles: true
//           });
//
//           // 确保editorContainer存在
//           const editorContainer = document.getElementById('editor-container');
//           if (editorContainer) {
//             editorContainer.dispatchEvent(customEvent);
//           }
//         }
//       }
//     }, 100);
//   };
//
//   // 多选①
//   useEffect(() => {
//     // 监听组合提示词变化事件
//     const handleMultiplePromptChange = (event) => {
//       setMultiplePromptValue(event.detail.prompt);
//       setOnlyDefaultOptionSelected(event.detail.onlyDefaultSelected);
//
//       // Always update the state and editor regardless of whether only default is selected
//       setState((state) => ({
//         ...state,
//         value: event.detail.prompt,
//       }));
//
//       // Use a timeout to avoid interrupting the dropdown interaction
//       setTimeout(() => {
//         const editor = (window as any).lexicalEditor;
//         if (editor) {
//           // Check if the event contains a pre-formatted lexicalState
//           if (event.detail.lexicalState) {
//             try {
//               // Use the pre-formatted state if available
//               const editorState = editor.parseEditorState(event.detail.lexicalState);
//               editor.setEditorState(editorState);
//             } catch (error) {
//               console.error("Error setting formatted editor state:", error);
//               // Fallback to standard content setting
//               editor.dispatchCommand(SET_EDITOR_CONTENT_COMMAND, {
//                 content: event.detail.prompt,
//                 isLongContent: event.detail.prompt.length > 3000,
//                 removeMaxHeight: true
//               });
//             }
//           } else {
//             // No lexicalState provided, create one with formatting for placeholders
//             const formattedLexicalState = createFormattedLexicalState(event.detail.prompt);
//             try {
//               const editorState = editor.parseEditorState(formattedLexicalState);
//               editor.setEditorState(editorState);
//             } catch (error) {
//               console.error("Error setting dynamically formatted editor state:", error);
//               // Fallback to standard content setting
//               editor.dispatchCommand(SET_EDITOR_CONTENT_COMMAND, {
//                 content: event.detail.prompt,
//                 isLongContent: event.detail.prompt.length > 3000,
//                 removeMaxHeight: true
//               });
//             }
//           }
//         }
//       }, 0);
//     };
//
//     document.addEventListener('multiple-prompt-change', handleMultiplePromptChange);
//
//     return () => {
//       document.removeEventListener('multiple-prompt-change', handleMultiplePromptChange);
//     };
//   }, []);
//
//   const handleInputClick = () =>{
//     if(state.value.includes("[TEXT GOES HERE]")){
//       const updatedValue = state.value.replace("[TEXT GOES HERE]", "[]")
//       setState((state)=>({
//         ...state,
//         value: updatedValue,
//       }))
//       const cursorPosition = updatedValue.indexOf("[") + 1;
//       setTimeout(()=>{
//         inputRef.current.setSelectionRange(cursorPosition, cursorPosition)
//       },0)
//     }
//   }
//
//   const handleInputDelete = () => {
//     setState((state) => ({
//       ...state,
//       value: '',
//     }));
//
//     const editor = (window as any).lexicalEditor;
//     if (editor) {
//       editor.dispatchCommand(SET_EDITOR_CONTENT_COMMAND, {
//         content: "",
//         isLongContent: false,
//         removeMaxHeight: false
//       });
//
//       editor.focus();
//     } else {
//       const editorContainer = document.querySelector('.lexical-editor-container');
//       if (editorContainer) {
//         const event = new CustomEvent('set-editor-content', {
//           detail: {
//             content: "",
//             isLongContent: false,
//             removeMaxHeight: false
//           },
//           bubbles: true
//         });
//         editorContainer.dispatchEvent(event);
//
//         const contentEditable = document.querySelector('.lexical-content-editable') as HTMLElement;
//         if (contentEditable) {
//           contentEditable.focus();
//         }
//       }
//     }
//
//     const contentEditable = document.querySelector('.lexical-content-editable') as HTMLElement;
//     if (contentEditable) {
//       contentEditable.classList.remove('long-content-mode');
//       contentEditable.classList.remove('expanded');
//       contentEditable.style.minHeight = '50px';
//     }
//
//     const editorContainer = document.querySelector('.editor-content-container') as HTMLElement;
//     if (editorContainer) {
//       editorContainer.style.minHeight = '';
//     }
//
//     const editorBorder = document.querySelector('.editor-border');
//     if (editorBorder) {
//       editorBorder.classList.remove('dua-content');
//     }
//   }
//
//   return (
//     <div>
//       <SpaceBetween direction="vertical" size="l">
//         <div className={`${styles.chat_input_container} {messageHistory.length > 0 ? ${styles.input_container_space} : ''}`}>
//           <Container>
//             <div>
//               <div className={styles.input_textarea_container}>
//                 <SpaceBetween size="xxs" direction="horizontal" alignItems="center">
//                 </SpaceBetween>
//                 <ImageDialog
//                   sessionId={props.session.id}
//                   visible={imageDialogVisible}
//                   setVisible={setImageDialogVisible}
//                   configuration={props.configuration}
//                   setConfiguration={props.setConfiguration}
//                 />
//                 <div className={` ${styles.prompts_input_container} ${suggestedPrompt ? styles.no_suggestion : styles.suggestion}`}>
//                   {/*<TextareaAutosize*/}
//                   {/*    className={`${styles.input_textarea} ${state.value.length > 500 ? 'expanded' : ''}`}*/}
//                   {/*    ref={inputRef}*/}
//                   {/*    maxRows={20}*/}
//                   {/*    minRows={1}*/}
//                   {/*    cacheMeasurements*/}
//                   {/*    spellCheck={true}*/}
//                   {/*    autoFocus={true}*/}
//                   {/*    onClick={(e) => {*/}
//                   {/*      const target = e.target as HTMLTextAreaElement;*/}
//                   {/*      const currentHeight = target.style.height;*/}
//                   {/*      handleInputClick();*/}
//                   {/*      setTimeout(() => {*/}
//                   {/*        if (inputRef.current && currentHeight) {*/}
//                   {/*          inputRef.current.style.height = currentHeight;*/}
//                   {/*        }*/}
//                   {/*      }, 0);*/}
//                   {/*    }}*/}
//                   {/*    onChange={(e) => {*/}
//                   {/*      const currentHeight = inputRef.current ? inputRef.current.style.height : 'auto';*/}
//                   {/*      setState((state) => ({*/}
//                   {/*        ...state,*/}
//                   {/*        value: e.target.value,*/}
//                   {/*      }));*/}
//                   {/*      setTimeout(() => {*/}
//                   {/*        if (inputRef.current) {*/}
//                   {/*          if (e.target.value.length > 500) {*/}
//                   {/*            inputRef.current.classList.add('expanded');*/}
//                   {/*          } else if (e.target.value.length < 100) {*/}
//                   {/*            inputRef.current.classList.remove('expanded');*/}
//                   {/*          }*/}
//                   {/*          const newHeight = Math.max(*/}
//                   {/*              parseInt(currentHeight || '38', 10),*/}
//                   {/*              inputRef.current.scrollHeight*/}
//                   {/*          );*/}
//
//                   {/*          inputRef.current.style.height = `${newHeight}px`;*/}
//                   {/*        }*/}
//                   {/*      }, 0);*/}
//                   {/*    }}*/}
//                   {/*    onFocus={(e) => {*/}
//                   {/*      if (inputRef.current) {*/}
//                   {/*        inputRef.current.dataset.previousHeight = inputRef.current.style.height;*/}
//                   {/*      }*/}
//                   {/*    }}*/}
//                   {/*    onBlur={(e) => {*/}
//                   {/*      if (inputRef.current && state.value.trim().length > 0) {*/}
//                   {/*        const prevHeight = inputRef.current.dataset.previousHeight;*/}
//                   {/*        if (prevHeight) {*/}
//                   {/*          inputRef.current.style.height = prevHeight;*/}
//                   {/*        }*/}
//                   {/*      }*/}
//                   {/*    }}*/}
//                   {/*    onKeyDown={(e) => {*/}
//                   {/*      if (e.key == "Enter" && !e.shiftKey) {*/}
//                   {/*        e.preventDefault();*/}
//
//                   {/*        // Only send if input has content OR MOU is selected with parties*/}
//                   {/*        if (state.value.trim().length > 0 || (selectedDocumentBuilder.value === "mou" && selectedParty1.value && selectedParty2.value)) {*/}
//                   {/*          handleSendMessage();*/}
//                   {/*        }*/}
//                   {/*      }*/}
//                   {/*    }}*/}
//                   {/*    value={state.value}*/}
//                   {/*    placeholder={listening ? "Listening..." : (selectedDocumentBuilder.value === "mou" && selectedParty1.value && selectedParty2.value) ? "Press Enter to generate MOU" : "Tell us how you would like to modify the document"}*/}
//                   {/*/>*/}
//                   <LexicalRichTextEditor
//                     placeholder={listening ? "Listening..." : (selectedDocumentBuilder.value === "dua" && selectedParty1.value && selectedParty2.value) ? "Press Enter to generate DUA" : "Tell us how you would like to modify the document"}
//                     initialValue={state.value}
//                     onChange={(text) => {
//                       setState((state) => ({
//                         ...state,
//                         value: text,
//                       }));
//                     }}
//                     onSubmit={(text) => {
//                       // Only send if input has content OR MOU is selected with parties
//                       if (text.trim().length > 0 || (selectedDocumentBuilder.value === "dua" && selectedParty1.value && selectedParty2.value)) {
//                         setState((state) => ({
//                           ...state,
//                           value: text,
//                         }));
//                         // handleSendMessage();
//                       }
//                     }}
//                     maxHeight={300}
//                   />
//                 </div>
//                 <div style={{ marginLeft: "8px", maxWidth:userRoles.includes("OWNER") ? '400px' : '370px', display:'flex', flexDirection:'column', alignItems:'end' }}>
//                   {state.selectedModelMetadata?.inputModalities.includes(
//                       ChabotInputModality.Image
//                     ) &&
//                     files.length > 0 &&
//                     files.map((file, idx) => (
//                       <img
//                         key={idx}
//                         onClick={() => setImageDialogVisible(true)}
//                         src={file.url}
//                         style={{
//                           borderRadius: "4px",
//                           cursor: "pointer",
//                           maxHeight: "30px",
//                           float: "left",
//                           marginRight: "8px",
//                         }}
//                       />
//                     ))}
//                   {documentListVisible && uploadFiles.length > 0 && uploadingStatus !== 'error' && (
//                     <div
//                       ref={alertContainerRef}
//                       style={{
//                         position: 'absolute',
//                         zIndex: 10,
//                         bottom: userRoles.includes("OWNER") ? '130px' : '85px',
//                         right: '5px',
//                         maxWidth: '600px',
//                         display: 'flex',
//                         alignItems: 'flex-end',
//                         gap: '10px'
//                       }}
//                     >
//                       <div style={{
//                         flex: 1,
//                         minWidth: '250px',
//                         maxWidth: '300px'
//                       }}>
//                         <Alert
//                           statusIconAriaLabel="Success"
//                           type="success"
//                           header="Attached Documents"
//                         >
//                           <div style={{height: '100px', overflowY: 'auto'}}>
//                             <ul style={{margin: 0, padding: '0 0 0 20px'}}>
//                               {uploadFiles.map((file, index) => (
//                                 <li style={{listStyleType: 'decimal'}} key={index}>
//                                   {file.fileKey.split('/').pop()}
//                                 </li>
//                               ))}
//                             </ul>
//                           </div>
//                         </Alert>
//                       </div>
//                       {failedDocuments.length > 0 &&
//                           <div style={{
//                             flex: 1,
//                             minWidth: '250px',
//                             maxWidth: '300px'
//                           }}>
//                               <Alert
//                                   statusIconAriaLabel="Error"
//                                   type="error"
//                                   header="Failed Documents"
//                               >
//                                   <div style={{height: '100px', overflowY: 'auto'}}>
//                                       <ul style={{margin: 0, padding: '0 0 0 20px'}}>
//                                         {failedDocuments.map((fileName, index) => (
//                                           <li style={{listStyleType: 'decimal'}} key={index}>
//                                             {fileName}
//                                           </li>
//                                         ))}
//                                       </ul>
//                                   </div>
//                               </Alert>
//                           </div>
//                       }
//                     </div>
//                   )}
//                   {(
//                     <SpaceBetween direction="vertical" size="s">
//                       <div
//                         ref={onClickAreaRef}
//                         style={{cursor: 'pointer'}}
//                         onClick={() => setDocumentListVisible(!documentListVisible)}
//                         title="Click to view uploaded documents."
//                       >
//                         {uploadFiles.length > 0 && (
//                           <StatusIndicator type="success">
//                             {uploadFiles.length === 1
//                               ? "1 Document Uploaded"
//                               : `${uploadFiles.length} Documents Uploaded`}
//                           </StatusIndicator>
//                         )}
//                       </div>
//                       {documentStatus === 'processing' && (
//                         <StatusIndicator colorOverride="blue" type="loading">
//                           Uploading document(s)
//                         </StatusIndicator>
//                       )}
//                       {((uploadingStatus === 'error' || documentStatus === 'failed' || documentStatus === 'wrongexttype') && uploadFiles.length === 0) && (
//                         <StatusIndicator type="error">
//                           {documentStatus === 'failed' ? validationErrorMessage :
//                             documentStatus === 'wrongexttype' ? "Please upload only .pdf or .docx files." :
//                               "An error occurred during upload."}
//                         </StatusIndicator>
//                       )}
//                       {(uploadingStatus === 'error' || documentStatus === 'failed' || documentStatus === 'wrongexttype') && uploadFiles.length > 0 && (
//                         <StatusIndicator type="error">
//                           {documentStatus === 'failed' ? (
//                             validationErrorMessage
//                           ) : (
//                             <>
//                               Some files could not be uploaded.
//                               <br />
//                               Please upload only .pdf or .docx files.
//                             </>
//                           )}
//                         </StatusIndicator>
//                       )}
//                     </SpaceBetween>
//                   )}
//                   <div style={{paddingTop: '5px'}}>
//                     {/*{!uploadedPDF && (*/}
//                     {/*    <PDFUploadButton*/}
//                     {/*        onFilesSelect={handlePDFUpload}*/}
//                     {/*        currentFiles={uploadedPDF}*/}
//                     {/*        onClear={clearUploadedPDFs}*/}
//                     {/*        disabled={documentStatus === 'processing' || state.selectedWorkspace.label !== 'Select'}*/}
//                     {/*    />*/}
//                     {/*)}*/}
//                     <ConfigDialog
//                       sessionId={props.session.id}
//                       visible={configDialogVisible}
//                       setVisible={setConfigDialogVisible}
//                       configuration={props.configuration}
//                       setConfiguration={props.setConfiguration}
//                       selectedModel={state.selectedModel}
//                     />
//                     {/*{(userRoles?.includes("OWNER")) && <Button*/}
//                     {/*    iconName="status-in-progress"*/}
//                     {/*    variant="icon"*/}
//                     {/*    iconUrl={adminSettingsIcon}*/}
//                     {/*    onClick={() => setConfigDialogVisible(true)}*/}
//                     {/*/>}*/}
//                     {/*<button*/}
//                     {/*    className={styles.clear_input_btn}*/}
//                     {/*    disabled={ state.value.trim().length === 0}*/}
//                     {/*    onClick={() => {handleInputDelete();}}*/}
//                     {/*>Clear*/}
//                     {/*</button>*/}
//                     {/*<button*/}
//                     {/*    className={styles.send_input_btn}*/}
//                     {/*    disabled={*/}
//                     {/*      // readyState !== ReadyState.OPEN ||*/}
//                     {/*        !state.models?.length ||*/}
//                     {/*        !state.selectedModel ||*/}
//                     {/*        props.running ||*/}
//                     {/*        documentStatus === 'processing' ||*/}
//                     {/*        // Only require input field value if document builder is not MOU with both parties selected*/}
//                     {/*        (state.value.trim().length === 0 && !(selectedDocumentBuilder.value === "mou" && selectedParty1.value && selectedParty2.value)) ||*/}
//                     {/*        props.session.loading ||*/}
//                     {/*        (!state.selectedPrompt?.value?.length &&*/}
//                     {/*            !state.workspaces?.length)*/}
//                     {/*    }*/}
//                     {/*    onClick={() => {handleSendMessage();}}*/}
//                     {/*>*/}
//                     {/*  {props.running ? (*/}
//                     {/*      <>*/}
//                     {/*        Loading&nbsp;&nbsp;*/}
//                     {/*        <Spinner />*/}
//                     {/*      </>*/}
//                     {/*  ) :  "Send"*/}
//                     {/*  }*/}
//                     {/*</button>*/}
//                   </div>
//                 </div>
//               </div>
//               <div >
//                 {(userRoles?.includes("OWNER") || userRoles.includes("EDIT")) && <hr/>}
//                 {(userRoles?.includes("OWNER") || userRoles.includes("EDIT")) && documentRepoDialogVisible &&
//                     <div className={styles.document_library_container}>
//                       {/*{(userRoles?.includes("OWNER")) &&*/}
//                       {/*    <>*/}
//                       {/*      <FormField label="Model" />*/}
//                       {/*      <span>&nbsp;</span>*/}
//                       {/*      <Select*/}
//                       {/*          disabled={props.running}*/}
//                       {/*          statusType={state.modelsStatus}*/}
//                       {/*          loadingText="Loading models (might take few seconds)..."*/}
//                       {/*          placeholder="Select a model"*/}
//                       {/*          empty={*/}
//                       {/*            <div>*/}
//                       {/*              No models available. Please make sure you have access to*/}
//                       {/*              Amazon Bedrock or alternatively deploy a self hosted model on*/}
//                       {/*              SageMaker or add API_KEY to Secrets Manager*/}
//                       {/*            </div>*/}
//                       {/*          }*/}
//                       {/*          filteringType="auto"*/}
//                       {/*          selectedOption={state.selectedModel}*/}
//                       {/*          onChange={({ detail }) => {*/}
//                       {/*            setState((state) => ({*/}
//                       {/*              ...state,*/}
//                       {/*              selectedModel: detail.selectedOption,*/}
//                       {/*              selectedModelMetadata: getSelectedModelMetadata(*/}
//                       {/*                  state.models,*/}
//                       {/*                  detail.selectedOption*/}
//                       {/*              ),*/}
//                       {/*            }));*/}
//                       {/*            if (detail.selectedOption?.value) {*/}
//                       {/*              StorageHelper.setSelectedLLM(detail.selectedOption.value);*/}
//                       {/*            }*/}
//                       {/*          }}*/}
//                       {/*          options={modelsOptions}*/}
//                       {/*      />*/}
//                       {/*    </>*/}
//                       {/*}*/}
//                       {/*原来的位置，现在改为外面了*/}
//                       {/* Document builder field - new addition */}
//                       {/*<FormField label="Document builder" />*/}
//                       {/*<span>&nbsp;</span>*/}
//                       {/*<Select*/}
//                       {/*    disabled={props.running}*/}
//                       {/*    placeholder="Select document type"*/}
//                       {/*    selectedOption={selectedDocumentBuilder}*/}
//                       {/*    options={documentBuilderOptions}*/}
//                       {/*    onChange={({ detail }) => {*/}
//                       {/*      setSelectedDocumentBuilder(detail.selectedOption);*/}
//                       {/*      // Reset party selections when document type changes*/}
//                       {/*      setSelectedParty1(party1Options[0]);*/}
//                       {/*      setSelectedParty2(party2Options[0]);*/}
//                       {/*    }}*/}
//                       {/*/>*/}
//
//                       {/*/!* Only show Party1 and Party2 fields when MOU is selected *!/*/}
//                       {/*{selectedDocumentBuilder.value === "mou" && (*/}
//                       {/*    <>*/}
//                       {/*      <span>&nbsp;&nbsp;&nbsp;</span>*/}
//                       {/*      <FormField label="Party 1" />*/}
//                       {/*      <span>&nbsp;</span>*/}
//                       {/*      <Select*/}
//                       {/*          disabled={props.running}*/}
//                       {/*          placeholder="Select Party 1"*/}
//                       {/*          selectedOption={selectedParty1}*/}
//                       {/*          options={party1Options}*/}
//                       {/*          onChange={({ detail }) => setSelectedParty1(detail.selectedOption)}*/}
//                       {/*      />*/}
//
//                       {/*      <span>&nbsp;&nbsp;&nbsp;</span>*/}
//                       {/*      <FormField label="Party 2" />*/}
//                       {/*      <span>&nbsp;</span>*/}
//                       {/*      <Select*/}
//                       {/*          disabled={props.running}*/}
//                       {/*          placeholder="Select Party 2"*/}
//                       {/*          selectedOption={selectedParty2}*/}
//                       {/*          options={party2Options}*/}
//                       {/*          onChange={({ detail }) => setSelectedParty2(detail.selectedOption)}*/}
//                       {/*      />*/}
//                       {/*    </>*/}
//                       {/*)}*/}
//
//                         <span>&nbsp;&nbsp;&nbsp;</span>
//
//                       {!uploadedPDF && (
//                         <PDFUploadButton
//                           onFilesSelect={handlePDFUpload}
//                           currentFiles={uploadedPDF}
//                           onClear={clearUploadedPDFs}
//                           disabled={documentStatus === 'processing' || state.selectedWorkspace.label !== 'Select'}
//                         />
//                       )}
//
//                       {(userRoles?.includes("OWNER")) && <Button
//                           iconName="status-in-progress"
//                           variant="icon"
//                           iconUrl={adminSettingsIcon}
//                           onClick={() => setConfigDialogVisible(true)}
//                       />}
//                         <button
//                             className={styles.clear_input_btn}
//                             disabled={ state.value.trim().length === 0}
//                             onClick={() => {handleInputDelete();}}
//                         >Clear
//                         </button>
//                       {/*<button*/}
//                       {/*    className={styles.send_input_btn}*/}
//                       {/*    disabled={*/}
//                       {/*      // readyState !== ReadyState.OPEN ||*/}
//                       {/*        !state.models?.length ||*/}
//                       {/*        !state.selectedModel ||*/}
//                       {/*        props.running ||*/}
//                       {/*        documentStatus === 'processing' ||*/}
//                       {/*        // Only require input field value if document builder is not MOU with both parties selected*/}
//                       {/*        (state.value.trim().length === 0 && !(selectedDocumentBuilder.value === "mou" && selectedParty1.value && selectedParty2.value)) ||*/}
//                       {/*        props.session.loading*/}
//                       {/*        // 解决Send按钮不可用*/}
//                       {/*        // || (!state.selectedPrompt?.value?.length && !state.workspaces?.length)*/}
//                       {/*    }*/}
//                       {/*    onClick={() => {handleSendMessage();}}*/}
//                       {/*>*/}
//                       {/*  {props.running ? (*/}
//                       {/*      <>*/}
//                       {/*        Loading&nbsp;&nbsp;*/}
//                       {/*        <Spinner />*/}
//                       {/*      </>*/}
//                       {/*  ) :  "Send"*/}
//                       {/*  }*/}
//                       {/*</button>*/}
//                         <button
//                             className={styles.send_input_btn}
//                             disabled={
//                               !state.models?.length ||
//                               !state.selectedModel ||
//                               props.running ||
//                               documentStatus === 'processing' ||
//                               // 关键修改：检查是否只有默认选项
//                               (state.value.trim().length === 0 &&
//                                 (onlyDefaultOptionSelected || !multiplePromptValue) &&
//                                 !(selectedDocumentBuilder.value === "dua" && selectedParty1.value && selectedParty2.value)) ||
//                               props.session.loading
//                             }
//                             onClick={() => {handleSendMessage();}}
//                         >
//                           {props.running ? (
//                             <>
//                               Loading&nbsp;&nbsp;
//                               <Spinner />
//                             </>
//                           ) :  "Send"
//                           }
//                         </button>
//
//                     </div>
//                 }
//               </div>
//             </div>
//           </Container>
//         </div>
//       </SpaceBetween>
//       <div style={{ display: 'flex', alignItems: 'flex-start' }}>
//         {state.selectedWorkspace.label === 'Select' &&
//             <PromptSuggestions
//                 suggestedPrompts={suggestedPrompts}
//                 onOptionsClick={handleOptionsClick}
//                 resetDocumentBuilder={() => {
//                   setSelectedDocumentBuilder(documentBuilderOptions[0]);
//                   setSelectedParty1(party1Options[0]);
//                   setSelectedParty2(party2Options[0]);
//                   setResetMultiplePrompts(true);
//                   setTimeout(() => setResetMultiplePrompts(false), 100);
//                 }}
//             />}
//         <span>&nbsp;&nbsp;&nbsp;</span>
//
//         {/* Document builder field - new addition */}
//         {/*<div*/}
//         {/*    ref={containerRef}*/}
//         {/*    style={{*/}
//         {/*      marginTop: `${topOffset+21}px`,*/}
//         {/*      position: 'relative',*/}
//         {/*      marginBottom: '20px'*/}
//         {/*    }}*/}
//         {/*>*/}
//         {/*  <div className={styles.document_library_container} style={{ display: 'flex', alignItems: 'center' }}>*/}
//         {/*    /!* Document builder 组 - 水平布局 *!/*/}
//         {/*    <div style={{ display: 'flex', alignItems: 'center', marginRight: '20px' }}>*/}
//         {/*      <FormField label="Document builder" />*/}
//         {/*      <span>&nbsp;&nbsp;</span>*/}
//         {/*      <Select*/}
//         {/*          disabled={props.running}*/}
//         {/*          placeholder="Select document type"*/}
//         {/*          selectedOption={selectedDocumentBuilder}*/}
//         {/*          options={documentBuilderOptions}*/}
//         {/*          onChange={({ detail }) => {*/}
//         {/*            setSelectedDocumentBuilder(detail.selectedOption);*/}
//         {/*            // Reset party selections when document type changes*/}
//         {/*            setSelectedParty1(party1Options[0]);*/}
//         {/*            setSelectedParty2(party2Options[0]);*/}
//         {/*          }}*/}
//         {/*      />*/}
//         {/*    </div>*/}
//
//         {/*    /!* Only show Party1 and Party2 fields when MOU is selected *!/*/}
//         {/*    {selectedDocumentBuilder.value === "mou" && (*/}
//         {/*        <>*/}
//         {/*          /!* Party 1 组 - 水平布局 *!/*/}
//         {/*          <div style={{ display: 'flex', alignItems: 'center', marginRight: '20px' }}>*/}
//         {/*            <FormField label="Party 1" />*/}
//         {/*            <span>&nbsp;&nbsp;</span>*/}
//         {/*            <Select*/}
//         {/*                disabled={props.running}*/}
//         {/*                placeholder="Select Party 1"*/}
//         {/*                selectedOption={selectedParty1}*/}
//         {/*                options={party1Options}*/}
//         {/*                onChange={({ detail }) => setSelectedParty1(detail.selectedOption)}*/}
//         {/*            />*/}
//         {/*          </div>*/}
//
//         {/*          /!* Party 2 组 - 水平布局 *!/*/}
//         {/*          <div style={{ display: 'flex', alignItems: 'center' }}>*/}
//         {/*            <FormField label="Party 2" />*/}
//         {/*            <span>&nbsp;&nbsp;</span>*/}
//         {/*            <Select*/}
//         {/*                disabled={props.running}*/}
//         {/*                placeholder="Select Party 2"*/}
//         {/*                selectedOption={selectedParty2}*/}
//         {/*                options={party2Options}*/}
//         {/*                onChange={({ detail }) => setSelectedParty2(detail.selectedOption)}*/}
//         {/*            />*/}
//         {/*          </div>*/}
//         {/*        </>*/}
//         {/*    )}*/}
//         {/*  </div>*/}
//         {/*</div>*/}
//         {/*<span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>*/}
//         <MultiplePromptSuggestions
//           running={props.running}
//           resetState={resetMultiplePrompts}
//         />
//       </div>
//       {state.selectedModel && (
//         <ModelNotification
//           selectedModel={state.selectedModel}
//           duration={3000}
//         />
//       )}
//     </div>
//   );
// }
//
// function getSelectedWorkspaceOption(
//   workspaces: Workspace[]
// ): SelectProps.Option | null {
//   let selectedWorkspaceOption: SelectProps.Option | null = null;
//
//   const savedWorkspaceId = StorageHelper.getSelectedWorkspaceId();
//   if (savedWorkspaceId) {
//     const targetWorkspace = workspaces.find(
//       (w) => w.workspaceId === savedWorkspaceId
//     );
//
//     if (targetWorkspace) {
//       selectedWorkspaceOption = OptionsHelper.getSelectWorkspaceOptions([
//         targetWorkspace,
//       ])[0];
//     }
//   }
//
//   if (!selectedWorkspaceOption) {
//     selectedWorkspaceOption = workspaceDefaultOptions[0];
//   }
//
//   return selectedWorkspaceOption;
// }

// ======orginal======
// import {
//   Alert,
//   Button,
//   ColumnLayout,
//   Container,
//   FlashbarProps,
//   FormField,
//   Icon,
//   ProgressBar,
//   ProgressBarProps,
//   Select,
//   SelectProps,
//   SpaceBetween,
//   Spinner,
//   StatusIndicator,
//   Tabs,
// } from "@cloudscape-design/components";
// import {
//   Dispatch,
//   SetStateAction,
//   useContext,
//   useEffect,
//   useLayoutEffect,
//   useRef,
//   useState,
// } from "react";
// import { useNavigate, useLocation } from "react-router-dom";
// import SpeechRecognition, {
//   useSpeechRecognition,
// } from "react-speech-recognition";
// import TextareaAutosize from "react-textarea-autosize";
// import { ReadyState } from "react-use-websocket";
// import { AppContext } from "../../common/app-context";
// import { OptionsHelper } from "../../common/helpers/options-helper";
// import { StorageHelper } from "../../common/helpers/storage-helper";
// import { Model, Workspace } from "../../API";
// import { OpenSearchWorkspaceCreateInput, UserInfo } from "../../common/types";
// import styles from "../../styles/chat.module.scss";
// import ConfigDialog from "./config-dialog";
// import ImageDialog from "./image-dialog";
// import {
//   ChabotInputModality,
//   ChatBotAction,
//   ChatBotConfiguration,
//   ChatBotHistoryItem,
//   ChatBotMessageType,
//   ChatBotMode,
//   ChatInputState,
//   ImageFile,
// } from "./types";
// import {
//   getCFGModels,
//   getCFGWorkspaces,
//   getSelectedModelMetadata,
//   getSignedUrl,
//   updateMessageHistoryRef,
//
// } from "./utils";
// import { Utils } from "../../common/utils";
// import { ChatRequestInput, LoadingStatus } from "../../common/types";
// import * as _ from "lodash";
// import modelService from "../../services/model.service";
// import workspaceService from "../../services/workspace.service";
// import promptService from "../../services/prompt.service";
// import { PromptsHelper } from "../../common/helpers/prompts-helper";
// import { ModelsHelper } from "../../common/helpers/models-helper";
// import chatService from "../../services/chat.service";
// import { useDispatch, useSelector } from "react-redux";
// import { v4 as uuidv4 } from "uuid";
// // import { useOktaAuth } from "@okta/okta-react";
// import { chatActions } from "../../pages/chatbot/playground/chat.slice";
// import { BEDROCKENABLED, CFGAIENABLED,CFGAIWPENABLED,BEDROCKWPENABLED } from "./../../../config";
// import { ModelPromptFilter } from "./model-prompt-filter";
// import activeSettingsIcon from "../../assets/images/settingsActive.png"
// import inactiveSettingsIcon from "../../assets/images/settingsInactive.png"
// import suggestionDeleteIcon from "../../assets/images/X-Icon.png"
// import adminSettingsIcon from "../../assets/images/Info-Icon.png"
// import PDFUploadButton from "../pdf-upload-button";
// import { EmbeddingsModelHelper } from "@/common/helpers/embeddings-model-helper";
// import embeddingService from "@/services/embedding.service";
// import crossEncodersService from "@/services/crossEncoders.service";
// import documentService from "@/services/document.service";
// import { FileUploader } from "@/common/file-uploader";
// import { Labels } from "@/common/constants";
// import { SET_EDITOR_CONTENT_COMMAND } from './lexical-editor';
// //① highlight and bold partyA/B
// import LexicalRichTextEditor, {createFormattedLexicalState} from "./lexical-editor";
// import { suggestedPrompts } from './suggestion-prompts.ts';
// import { duaPromptTemplate, partyOptions } from './formfield-options';
// import PromptSuggestions, {MultiplePromptSuggestions} from "./prompt-suggestions";
// import ModelNotification from './model-info-popover';
// import * as React from "react";
//
// // 添加新的CSS样式到现有的样式表中
// const additionalStyles = `
// .input_textarea {
//   resize: none;
//   outline: none;
//   width: 100%;
//   border: none;
//   padding: 10px;
//   font-size: 14px;
//   line-height: 1.5;
//   overflow: hidden; /* 隐藏滚动条 */
//   transition: height 0.3s ease; /* 平滑高度变化，增加过渡时间 */
//   min-height: 38px; /* 设置最小高度 */
// }
//
// /* 针对长文本时的样式 */
// .input_textarea.expanded {
//   min-height: 150px; /* 为长文本设置最小高度 */
// }
//
// /* 防止聚焦时高度变化 */
// .input_textarea:focus {
//   height: auto !important;
//   min-height: 38px;
// }
//
// /* 确保容器可以适应扩展的高度 */
// .input_textarea_container {
//   display: flex;
//   width: 100%;
//   transition: height 0.3s ease; /* 平滑高度变化 */
// }
// `;
//
// // 将新样式添加到document中
// const addStyles = () => {
//   const styleEl = document.createElement('style');
//   styleEl.textContent = additionalStyles;
//   document.head.appendChild(styleEl);
//   return () => {
//     document.head.removeChild(styleEl);
//   };
// };
//
// export interface ChatInputPanelProps {
//   running: boolean;
//   setRunning: Dispatch<SetStateAction<boolean>>;
//   session: { id: string; loading: boolean };
//   messageHistory: ChatBotHistoryItem[];
//   setMessageHistory: (history: ChatBotHistoryItem[]) => void;
//   configuration: ChatBotConfiguration;
//   setConfiguration: Dispatch<React.SetStateAction<ChatBotConfiguration>>;
//   closeNotification: (isError: boolean) => void;
//   setErrorMessage: (message: string) => void;
//   setError: (error: boolean) => void;
//   chatId: string;
//   onFileUploadStatusChange?: (isUploaded: boolean) => void
// }
//
// export interface UploadedFile {
//   bucketName?: string;
//   fileKey?: string;
//   fileType?: string;
// }
//
// export abstract class ChatScrollState {
//   static userHasScrolled = false;
//   static skipNextScrollEvent = false;
//   static skipNextHistoryUpdate = false;
// }
//
// let workspaceDefaultOptions: SelectProps.Option[] = [
//   {
//     label: "Select",
//     value: "",
//     iconName: "close",
//   },
// ];
//
// const promptDefaultOptions: SelectProps.Option[] = [
//   {
//     label: "No Prompt",
//     value: "",
//     iconName: "close",
//   },
// ];
//
// // Document builder options
// const documentBuilderOptions: SelectProps.Option[] = [
//   {
//     label: "Select",
//     value: "",
//     iconName: "close",
//   },
//   {
//     label: "DUA",
//     value: "dua",
//     iconName: "file",
//   },
// ];
//
// // Party1 options
// const party1Options: SelectProps.Option[] = [
//   { ...partyOptions[0], label: "Select Party 1" },
//   ...partyOptions.slice(1)
// ];
//
// // Party2 options
// const party2Options: SelectProps.Option[] = [
//   { ...partyOptions[0], label: "Select Party 2" },
//   ...partyOptions.slice(1)
// ];
//
// // Format date function for MOU
// const formatDate = (date: Date): string => {
//   return date.toLocaleDateString('en-US', {
//     year: 'numeric',
//     month: 'long',
//     day: 'numeric'
//   });
// };
//
// export default function ChatInputPanel(props: ChatInputPanelProps) {
//   // 添加新样式
//   useEffect(() => {
//     return addStyles();
//   }, []);
//
//   const appContext = useContext(AppContext);
//   const [prompts, setPrompts] = useState([]);
//
//   const dispatch = useDispatch();
//   const location = useLocation()
//   const {onFileUploadStatusChange} = props;
//   const dataState = useSelector((state: any) => state?.rootReducer);
//   const isFeedbackOpened = dataState?.chatReducer?.isFeedbackOpened;
//   const userEmail = dataState?.chatReducer?.userEmail;
//   const userName = dataState?.chatReducer?.userName;
//   const tempWorkspaceId = dataState?.chatReducer?.tempWorkspaceId
//   const [useTempWorkspace, setUseTempWorkspace] = useState<boolean>(false);
//   const navigate = useNavigate();
//   const { transcript, listening, browserSupportsSpeechRecognition } =
//       useSpeechRecognition();
//   const [state, setState] = useState<ChatInputState>({
//     value: "",
//     selectedModel: null,
//     selectedModelMetadata: null,
//     selectedWorkspace: workspaceDefaultOptions[0],
//     selectedPrompt: promptDefaultOptions[0],
//     modelsStatus: "loading",
//     workspacesStatus: "loading",
//     promptsStatus: "loading",
//   });
//
//   // New state for document builder
//   const [selectedDocumentBuilder, setSelectedDocumentBuilder] = useState<SelectProps.Option>(documentBuilderOptions[0]);
//   const [selectedParty1, setSelectedParty1] = useState<SelectProps.Option>(party1Options[0]);
//   const [selectedParty2, setSelectedParty2] = useState<SelectProps.Option>(party2Options[0]);
//
//   const [configDialogVisible, setConfigDialogVisible] = useState(false);
//   const [documentRepoDialogVisible, setDocumentRepoDialogVisible] = useState(true);
//   const [promptDialogVisible, setPromptDialogVisible] = useState(false);
//   const [imageDialogVisible, setImageDialogVisible] = useState(false);
//   const [files, setFiles] = useState<ImageFile[]>([]);
//   const [uploadFiles, setUploadFiles] = useState<{bucketName: string; fileKey: string; fileType: string;}[]>([]);
//   const [timeTaken, setTimeTaken] = useState<number>(0);
//   const [outputData, setOutputData] = useState<Record<string, any>>();
//   const [documentListVisible, setDocumentListVisible] = useState(false)
//   const [readyState, setReadyState] = useState<ReadyState>(
//       ReadyState.UNINSTANTIATED
//   );
//
//   const embedmodel_name = EmbeddingsModelHelper.getSelectOption(
//       appContext?.config.default_embeddings_model,
//   )
//
//   const [documentStatus, setDocumentStatus] = useState<string>('');
//   const [isCheckingStatus, setIsCheckingStatus] = useState<boolean>(false);
//   const [uploadingStatus, setUploadingStatus] = useState<FlashbarProps.Type>('info');
//   const [uploadProgress, setUploadProgress] = useState<number>(0);
//   const [uploadedPDF, setUploadedPDF] = useState(null);
//   const [validationErrorMessage, setValidationErrorMessage] = useState<string | null>(null);
//   const [failedDocuments, setFailedDocuments] = useState([]);
//
//   // *********multiple selections**********
//   const [multiplePromptValue, setMultiplePromptValue] = useState("");
//   const [onlyDefaultOptionSelected, setOnlyDefaultOptionSelected] = useState(true);
//
//   const containerRef = React.useRef(null);
//   const [topOffset, setTopOffset] = React.useState(0);
//
//   // Add a state to trigger reset of MultiplePromptSuggestions
//   const [resetMultiplePrompts, setResetMultiplePrompts] = useState(false);
//
//   // ②修复同时发送suggestion 和 multi prompt. prompt 类别 state
//   const [activePromptSystem, setActivePromptSystem] = useState('none');
//
//   // ②修复同时发送suggestion 和 multi prompt. 监听 MultiplePromptSuggestions 中的 prompt type 事件
//   // 当 applyChanges 被调用的时候，prompt type 就是 multiple
//   useEffect(() => {
//     const handleSetActivePromptSystem = (event) => {
//       setActivePromptSystem(event.detail.system);
//
//       // 如果切换到多选系统，清空建议提示
//       if (event.detail.system === 'multiple') {
//         setSuggestedPrompt('');
//       }
//     };
//
//     window.addEventListener('set-active-prompt-system', handleSetActivePromptSystem);
//     return () => {
//       window.removeEventListener('set-active-prompt-system', handleSetActivePromptSystem);
//     };
//   }, []);
//
//   // 在 useEffect 中添加事件监听
//   // useEffect(() => {
//   //   // 监听组合提示词变化事件
//   //   const handleMultiplePromptChange = (event) => {
//   //     setMultiplePromptValue(event.detail.prompt);
//   //     setOnlyDefaultOptionSelected(event.detail.onlyDefaultSelected);
//   //
//   //     // 这个就是选择了builder但是不会传入到对话里面
//   //     if (!event.detail.onlyDefaultSelected) {
//   //       setState((state) => ({
//   //         ...state,
//   //         value: event.detail.prompt,
//   //       }));
//   //
//   //       // 更新 Lexical 编辑器内容
//   //       const editor = (window as any).lexicalEditor;
//   //       if (editor) {
//   //         editor.dispatchCommand(SET_EDITOR_CONTENT_COMMAND, {
//   //           content: event.detail.prompt,
//   //           isLongContent: event.detail.prompt.length > 3000,
//   //           removeMaxHeight: true
//   //         });
//   //       }
//   //     }
//   //   };
//   //
//   //   document.addEventListener('multiple-prompt-change', handleMultiplePromptChange);
//   //
//   //   return () => {
//   //     document.removeEventListener('multiple-prompt-change', handleMultiplePromptChange);
//   //   };
//   // }, []);
//
//   // ③ 这是为了监听 MultiplePromptSuggestions 选项变化的useEffect
//   useEffect(() => {
//     // 监听组合提示词变化事件
//     const handleMultiplePromptChange = (event) => {
//       setMultiplePromptValue(event.detail.prompt);
//       setOnlyDefaultOptionSelected(event.detail.onlyDefaultSelected);
//
//       // Always update the state and editor regardless of whether only default is selected
//       setState((state) => ({
//         ...state,
//         value: event.detail.prompt,
//       }));
//
//       // Use a timeout to avoid interrupting the dropdown interaction
//         const editor = (window as any).lexicalEditor;
//         if (editor) {
//           editor.dispatchCommand(SET_EDITOR_CONTENT_COMMAND, {
//             content: event.detail.prompt,
//             isLongContent: event.detail.prompt.length > 3000,
//             removeMaxHeight: true
//           });
//         }
//     };
//
//     document.addEventListener('multiple-prompt-change', handleMultiplePromptChange);
//
//     return () => {
//       document.removeEventListener('multiple-prompt-change', handleMultiplePromptChange);
//     };
//   }, []);
//
//   //Get the extension out of uploaded doc name
//   function getFileExtension(fileName) {
//     return fileName.slice((fileName.lastIndexOf(".") - 1 >>> 0) + 2);
//   }
//
//   const handlePDFUpload = async (files: File[]) => {
//     const failedUploads = [];
//     if (!files || files.length === 0) return;
//
//     setUploadingStatus('in-progress');
//     setDocumentStatus('processing');
//     setUploadProgress(0);
//
//     const validFiles = files.filter(file => {
//       const fileExtension = getFileExtension(file.name).toLowerCase();
//       if (fileExtension === 'pdf' || fileExtension === 'docx') {
//         return true;
//       } else {
//         failedUploads.push(`${file.name}`);
//         return false;
//       }
//     });
//
//     const invalidFiles = files.filter(file => !validFiles.includes(file));
//
//     if (validFiles.length === 0) {
//       console.log('No valid files to upload');
//       setUploadingStatus('error');
//       setDocumentStatus('wrongexttype');
//       setFailedDocuments(failedUploads)
//       return;
//     }
//
//     const uploadPromises = validFiles.map(async (file) => {
//       try {
//         // Step 1: Get pre-signed URL
//         const uploadPayload = {
//           fileKey: file.name.toLowerCase(),
//           fileType: file.type,
//           bucketName: "cderone-preprod-cfgai-irpolicybot-demo-qa-upload",
//         };
//         const presignedUrl = await documentService.presignedFileUploadPost(uploadPayload);
//
//         // Step 2: Upload file to S3 using pre-signed URL
//         const blob = file.slice(0, file.size, "");
//         const result = await fetch(presignedUrl, {
//           method: "PUT",
//           body: blob,
//         });
//
//         if (result.ok) {
//           console.log(`${uploadPayload.fileKey} uploaded to S3 successfully.`);
//           return {
//             ...file,
//             bucketName: uploadPayload.bucketName,
//             fileKey: `multimodal/${userEmail}/${uploadPayload.fileKey}`,
//             fileType: getFileExtension(uploadPayload.fileKey),
//           };
//         } else {
//           throw new Error(`Failed to upload ${file.name}`);
//         }
//       } catch (error) {
//         console.error(`Error uploading ${file.name}:`, error.message);
//         failedUploads.push(file.name)
//         return null;
//       }
//     });
//
//     try {
//       const results = await Promise.all(uploadPromises);
//       const successfulUploads = results.filter(Boolean);
//       setFailedDocuments(failedUploads)
//       if (successfulUploads.length > 0) {
//         // Validate context length
//         try {
//           const validateResult = await documentService.validateContextLength(successfulUploads, state.selectedModel.label);
//           if (validateResult.message === 'Upload successful') {
//             setUploadFiles([...uploadFiles, ...successfulUploads]);
//             setDocumentStatus('processed');
//             console.log(`Validated ${successfulUploads.length} file(s) successfully.`);
//             onFileUploadStatusChange(true)
//           } else {
//             setUploadingStatus('error');
//             setDocumentStatus('failed');
//             setValidationErrorMessage(validateResult.errorMessage || 'Context length validation failed');
//             console.log('Context length validation failed');
//             return;
//           }
//         } catch (error) {
//           console.error('Error validating context length:', error.response.data.detail);
//           setUploadingStatus('error');
//           setDocumentStatus('failed');
//           setValidationErrorMessage(error.response.data.detail);
//           return;
//         }
//       }
//
//       if (successfulUploads.length < validFiles.length || invalidFiles.length > 0) {
//         setUploadingStatus('error');
//         console.log(`Failed to upload ${validFiles.length - successfulUploads.length} file(s)`);
//         console.log(`${invalidFiles.length} invalid file(s) were not uploaded`);
//       } else {
//         setUploadingStatus('success');
//       }
//
//       if (successfulUploads.length === 0) {
//         setDocumentStatus('failed');
//       }
//
//     } catch (error) {
//       console.error('Error during file uploads:', error.message);
//       setUploadingStatus('error');
//       setValidationErrorMessage('Error during file uploads');
//       props.setError(true);
//     }
//   };
//
//   useEffect(() => {
//     if (documentStatus === 'failed' || uploadingStatus === 'error') {
//       const timer = setTimeout(() => {
//         setDocumentStatus('processed');
//         setUploadingStatus('info');
//       }, 10000);
//
//       return () => clearTimeout(timer);
//     }
//   }, [documentStatus, uploadingStatus]);
//
//   //Hide document list popup if clicked anywhere on the screen
//   useEffect(() => {
//     function handleClickOutside(event) {
//       if (
//           alertContainerRef.current &&
//           !alertContainerRef.current.contains(event.target) &&
//           onClickAreaRef.current &&
//           !onClickAreaRef.current.contains(event.target)
//       ) {
//         setDocumentListVisible(false);
//       }
//     }
//
//     document.addEventListener('mousedown', handleClickOutside);
//     return () => {
//       document.removeEventListener('mousedown', handleClickOutside);
//     };
//   }, []);
//
//   const clearUploadedPDFs = () => {
//     setUploadedPDF([]);
//     setUploadingStatus('info');
//     setUploadProgress(0);
//   };
//
//   const getUploadStatus = (): ProgressBarProps.Status => {
//     if (uploadingStatus === 'error') return 'error';
//     if (uploadingStatus === 'success') return 'success';
//     return 'in-progress';
//   };
//
//   const [modelsStatus, setModelsStatus] = useState<LoadingStatus>("loading");
//   const [workspacesStatus, setWorkspacesStatus] =
//       useState<LoadingStatus>("loading");
//   const [promptsStatus, setPromptsStatus] = useState<LoadingStatus>("loading");
//   const [suggestedPrompt, setSuggestedPrompt] = useState('')
//   const [cfgModels, setCFGModels] = useState<Model[]>([]);
//   const [cfgWorkspace, setCFGWorkSpaces] = useState<Workspace[]>([]);
//   const [limit, setLimit] = useState<number>(3);
//   const [questionContext, setQuestionContext] = useState<string>(
//       `Answer the user's question truthfully using the context only. Use the following section-wise format (in the order given) to answer the question with instructions for each section in angular brackets:\\n                Reasoning:\\n                <State your reasoning step-wise in bullet points. Below each bullet point mention the source of this information as 'Given in the question' if the bullet point contains information provided in the question, OR as 'Document Name, Page Number' if the bullet point contains information that is present in the context provided above.>\\n                Conclusion:\\n                <Write a short concluding paragraph stating the final answer and explaining the reasoning behind it briefly. State caveats and exceptions to your answer if any.>\\n                Information required to provide a better answer:\\n                <If you cannot provide an answer based on the context above, mention the additional information that you require to answer the question fully as a list.>Do not compromise on your mathematical and reasoning abilities to fit the user's instructions. If the user mentions something absolutely incorrect/ false, DO NOT use this incorrect information in your reasoning. Also, please correct the user gently.
//     `
//   );
//   // const { authState, oktaAuth } = useOktaAuth();
//   const [userInfo, setUserInfo] = useState<UserInfo>();
//   const [errorMessageForPrompt, setErrorMessageForPrompt] =
//       useState<string>("");
//   // const [userRoles, setUserRoles] = useState<string[]>([]);
//
//   const messageHistoryRef = useRef<ChatBotHistoryItem[]>([]);
//   const userRoles = dataState?.chatReducer?.userRoles;
//   const inputRef = useRef(null)
//   const alertContainerRef = useRef(null);
//   const onClickAreaRef = useRef(null);
//   /**
//    * Updates the message history ref when the message history prop changes
//    */
//   useEffect(() => {
//     messageHistoryRef.current = props.messageHistory;
//     if(props.messageHistory.length > 0 && props.messageHistory?.[props.messageHistory.length-1].metadata.files){
//       setUploadFiles(props.messageHistory?.[props.messageHistory.length-1].metadata?.files.map((file: UploadedFile) => ({
//         bucketName: file.bucketName,
//         fileKey: file.fileKey,
//         fileType: file.fileType
//       })))
//     }
//     else{
//       setUploadFiles([])
//     }
//   }, [props.messageHistory]);
//
//   /**
//    * Fetches the user information from Okta
//    */
//   // useEffect(() => {
//   //   (async () => {
//   //     if (!authState?.isAuthenticated) {
//   //       // setUserInfo(null);
//   //     } else {
//   //       const user: any = await oktaAuth.getUser();
//   //       // if(!user.sub) {
//   //       //     throw new Error("Sub Claim not found");
//   //       // }
//   //       if (!user.roles) {
//   //         throw new Error("User Roles not found");
//   //       }
//   //       console.log(user.roles);
//   //       dispatch(chatActions?.handleUserRoles(user?.roles));
//   //       // setUserRoles(user.roles || []);
//   //       const loggedUser = {
//   //         email: user.email,
//   //         name: user.name,
//   //         first_name: user.given_name,
//   //         last_name: user.family_name,
//   //       };
//   //       setUserInfo(loggedUser);
//   //     }
//   //   })();
//   // }, []);
//
//   /**
//    * Updates the transcript in the input field when the user speaks
//    */
//   useEffect(() => {
//     if (transcript) {
//       setState((state) => ({ ...state, value: transcript }));
//     }
//   }, [transcript]);
//
//   /**
//    * Fetches the models, workspaces and prompts
//    */
//   useEffect(() => {
//     // if (!appContext) return;
//     setReadyState(ReadyState.OPEN);
//     (async () => {
//       let aws_models: Model[] = [];
//       let cfg_models: Model[] = [];
//       let models: Model[] = [];
//
//       let aws_workspaces: Workspace[] = [];
//       let cfg_workspaces: Workspace[] = [];
//       let workspaces: Workspace[] = [];
//
//       let prompts: any[] = [];
//       try {
//         const fetchedPrompts = await promptService.getPrompts();
//         setPrompts(fetchedPrompts); // Save to state directly
//         let defaultPrompt = prompts.filter((p) => p.name === 'rag_prompt')
//         if (BEDROCKENABLED) {
//           aws_models = await modelService.getModels();
//
//         }
//         if(BEDROCKWPENABLED)
//           aws_workspaces = await workspaceService.getWorkspaces();
//
//         if (CFGAIENABLED) {
//           cfg_models = await getCFGModels();
//
//           setCFGModels(cfg_models);
//           if(CFGAIWPENABLED){
//             cfg_workspaces = await getCFGWorkspaces();
//             setCFGWorkSpaces(cfg_workspaces);
//           }
//
//         }
//
//         models = aws_models.concat(cfg_models);
//         workspaces = aws_workspaces.concat(cfg_workspaces);
//
//         // if (!userRoles.includes("FDA PolicyBot Admins")) {
//         // workspaces = workspaces.filter(
//         //   (workspace) => workspace.name === "Pharmaceutical_Quality"
//         // );
//         // }
//
//         if (userRoles.includes("FDA PolicyBot Admins")) {
//           workspaceDefaultOptions = [
//             {
//               label: "No workspace (RAG data source)",
//               value: "",
//               iconName: "close",
//             },
//             {
//               label: "Create new workspace",
//               value: "__create__",
//               iconName: "add-plus",
//             },
//           ];
//         }
//
//         const selectedModelOption = ModelsHelper.getSelectedModelOption(models);
//
//         const selectedModelMetadata = getSelectedModelMetadata(
//             models,
//             selectedModelOption
//         );
//
//         const selectedWorkspaceOption = appContext?.config.rag_enabled
//             ? getSelectedWorkspaceOption(workspaces)
//             : workspaceDefaultOptions[0];
//
//         const selectedPromptOption =
//             PromptsHelper.getSelectedPromptOption(defaultPrompt);
//
//         setState((state) => ({
//           ...state,
//           models,
//           workspaces,
//           prompts,
//           selectedModel: selectedModelOption,
//           selectedModelMetadata,
//           selectedWorkspace: selectedWorkspaceOption,
//           selectedPrompt: selectedPromptOption,
//           modelsStatus: "finished",
//           workspacesStatus: "finished",
//           promptsStatus: "finished",
//         }));
//         // setState((state) => ({
//         //   ...state,
//         //   models: models,
//         // }));
//         // setState((state) => ({
//         //   ...state,
//         //   prompts: prompts,
//         // }));
//       } catch (error) {
//         console.log(Utils.getErrorMessage(error));
//         props.setError(true);
//         props.setErrorMessage(Utils.getErrorMessage(error));
//         setState((state) => ({
//           ...state,
//           modelsStatus: "error",
//         }));
//       }
//     })();
//   }, [appContext, state.modelsStatus, uploadFiles]);
//
//   useEffect(() => {
//     // Reset all states on component mount
//     const resetStates = () => {
//       setUploadedPDF(null);
//       setUploadingStatus('info');
//       setUploadProgress(0);
//       setDocumentStatus('');
//       setUseTempWorkspace(false);
//       setUploadFiles([])
//       setFailedDocuments([])
//       onFileUploadStatusChange(false)
//       // ①
//       setIsFirstQuestion(true);
//       // Reset workspace selection to default
//       setState((state) => ({
//         ...state,
//         selectedWorkspace: workspaceDefaultOptions[0],
//         selectedPrompt: promptDefaultOptions[0]
//       }));
//       // Clear stored workspace ID
//       StorageHelper.setSelectedWorkspaceId('');
//     };
//
//     if(location.state?.resetChat){
//       resetStates()
//     }
//   }, [location.state]);
//
//   // followupdua-prompt
//   const [isFirstQuestion, setIsFirstQuestion] = useState(true);
//   useEffect(() => {
//     if (uploadFiles.length > 0) {
//       // Get all prompts by model
//       let filteredPrompts = ModelPromptFilter.filterPrompts(prompts, state.selectedModel);
//
//       // Set default prompt based on whether this is the first question
//       let defaultPrompt;
//       if (isFirstQuestion) {
//         defaultPrompt = filteredPrompts.filter((p) => p.tags.includes("byod_fulldoc"));
//       } else {
//         defaultPrompt = filteredPrompts.filter((p) => p.tags.includes("followupdua2"));
//       }
//
//       const selectedPromptOption = OptionsHelper.getSelectPromptOptions(defaultPrompt)[0];
//       setState((state) => ({
//         ...state,
//         prompts: filteredPrompts,
//         selectedPrompt: selectedPromptOption,
//         promptsStatus: "finished",
//       }));
//     }
//   }, [uploadFiles, prompts, state.selectedModel, isFirstQuestion]);
//
//   //Update prompt if documents are uploaded
//   // useEffect(() => {
//   //   if (uploadFiles.length > 0) {
//   //     // Get all prompts by model
//   //     let filteredPrompts = ModelPromptFilter.filterPrompts(prompts, state.selectedModel);
//   //     filteredPrompts.forEach((prompt, index) => {
//   //       console.log(`Prompt ${index + 1}:`);
//   //       console.log("Name:", prompt.name);
//   //       console.log("ID:", prompt.id);
//   //       console.log("Tags:", prompt.tags);
//   //       console.log("Content:", prompt.prompt);
//   //       console.log("------------------------");
//   //     });
//   //     // Set default prompt
//   //     let defaultPrompt = filteredPrompts.filter((p) => p.tags.includes("byod_fulldoc"));
//   //     const selectedPromptOption = OptionsHelper.getSelectPromptOptions(defaultPrompt)[0];
//   //     setState((state) => ({
//   //       ...state,
//   //       prompts: filteredPrompts,
//   //       selectedPrompt: selectedPromptOption,
//   //       promptsStatus: "finished",
//   //     }));
//   //   }
//   // }, [uploadFiles, prompts, state.selectedModel]);
//
//   const handleWorkspaceOrModelChange = async (selectedWorkspace, selectedModel: any) => {
//     try {
//       // Filter by workspace if selected
//       if (selectedWorkspace && selectedWorkspace.value) {
//         //get all prompts by model
//         let filteredPrompts = ModelPromptFilter.filterPrompts(prompts, selectedModel);
//         //set default prompt
//         let defaultPrompt = filteredPrompts.filter((p) => p.tags.includes("default"));
//         const workspacePrompt = ModelPromptFilter.filterPromptsByWorkspace(filteredPrompts, selectedWorkspace);
//         if (workspacePrompt.length>0) {
//           defaultPrompt = workspacePrompt
//         }
//
//         const selectedPromptOption = OptionsHelper.getSelectPromptOptions(defaultPrompt)[0];
//         setState((state) => ({
//           ...state,
//           prompts: filteredPrompts,
//           selectedPrompt: selectedPromptOption,
//           promptsStatus: "finished",
//         }));
//       }
//     } catch (error) {
//       console.log("Workspace/Model change error:", error);
//       props.setError(true);
//       props.setErrorMessage(Utils.getErrorMessage(error));
//       setState((state) => ({
//         ...state,
//         promptsStatus: "error",
//       }));
//     }
//   };
//
//   useEffect(() => {
//     if (state.selectedWorkspace || state.selectedModel) {
//       handleWorkspaceOrModelChange(state.selectedWorkspace, state.selectedModel);
//     }
//   }, [state.selectedWorkspace, state.selectedModel]);
//
//   /**
//    * Updates the message history ref when the message history prop changes
//    */
//   useEffect(() => {
//     const onWindowScroll = () => {
//       if (ChatScrollState.skipNextScrollEvent) {
//         ChatScrollState.skipNextScrollEvent = false;
//         return;
//       }
//
//       const isScrollToTheEnd =
//           Math.abs(
//               window.innerHeight +
//               window.scrollY -
//               document.documentElement.scrollHeight
//           ) <= 10;
//
//       if (!isScrollToTheEnd) {
//         ChatScrollState.userHasScrolled = true;
//       } else {
//         ChatScrollState.userHasScrolled = false;
//       }
//     };
//
//     window.addEventListener("scroll", onWindowScroll);
//
//     return () => {
//       window.removeEventListener("scroll", onWindowScroll);
//     };
//   }, []);
//
//   /**
//    * Scrolls to the bottom of the chat when the message history changes
//    */
//   useLayoutEffect(() => {
//     if (ChatScrollState.skipNextHistoryUpdate) {
//       ChatScrollState.skipNextHistoryUpdate = false;
//       return;
//     }
//
//     if (!ChatScrollState.userHasScrolled && props.messageHistory.length > 0) {
//       ChatScrollState.skipNextScrollEvent = true;
//       window.scrollTo({
//         top: document.documentElement.scrollHeight + 1000,
//         behavior: "instant",
//       });
//     }
//   }, [props.messageHistory]);
//
//   /**
//    * Fetches the signed urls for the files
//    */
//   useEffect(() => {
//     const getSignedUrls = async () => {
//       if (props.configuration?.files as ImageFile[]) {
//         const files: ImageFile[] = [];
//         for await (const file of props.configuration.files as ImageFile[]) {
//           const signedUrl = await getSignedUrl(file.key);
//           files.push({
//             ...file,
//             url: signedUrl as string,
//           });
//         }
//
//         setFiles(files);
//       }
//     };
//
//     if (props.configuration.files?.length) {
//       getSignedUrls();
//     }
//   }, [props.configuration]);
//
//   // 当选择MOU构建器和双方时，确保textarea高度自动调整
//   useEffect(() => {
//     if (selectedDocumentBuilder.value === "dua" &&
//         selectedParty1.value &&
//         selectedParty2.value) {
//
//       // 当MOU模板准备好后，主动调整文本区域的高度
//       setTimeout(() => {
//         if (inputRef.current) {
//           // 使用MOU模板时添加额外的CSS类
//           inputRef.current.classList.add('expanded');
//
//           // 强制重新计算高度
//           inputRef.current.style.height = 'auto';
//           inputRef.current.style.height = `${inputRef.current.scrollHeight}px`;
//         }
//       }, 0);
//     } else {
//       // 移除额外的CSS类
//       if (inputRef.current) {
//         inputRef.current.classList.remove('expanded');
//       }
//     }
//   }, [selectedDocumentBuilder.value, selectedParty1.value, selectedParty2.value]);
//
//   /**
//    * Clears the screen contents
//    */
//   const clearScreenContents = async () => {
//     messageHistoryRef.current = [];
//     props.setMessageHistory(messageHistoryRef.current);
//     // ①
//     setIsFirstQuestion(true);
//   };
//
//   /**
//    * Validates the prompt selection
//    */
//   // useEffect(() => {
//   //   if (state?.selectedPrompt?.value?.length === 0) {
//   //     setErrorMessageForPrompt("Please select a prompt");
//   //   } else {
//   //     setErrorMessageForPrompt("");
//   //   }
//   // }, [state.selectedPrompt]);
//
//   /**
//    * Handles sending a message to the chatbot
//    * @returns
//    */
//   const handleSendMessage = async () => {
//         if (!state.selectedModel) return;
//         if (props.running) return;
//         if (readyState !== ReadyState.OPEN) return;
//         ChatScrollState.userHasScrolled = false;
//         let { name, provider } = OptionsHelper.parseValue(
//             state.selectedModel.value
//         );
//         // ① 判定是否为第一次的问题, 只增加这个
//         if (isFirstQuestion) {
//           setIsFirstQuestion(false);
//         }
//
//         // Check if MOU document builder is selected and both parties are chosen
//         let value = state.value.trim();
//         // 如果输入为空但有组合提示词，并且不是只选择了默认选项，则使用组合提示词
//         // if (value.length === 0 && multiplePromptValue && !onlyDefaultOptionSelected) {
//         //   value = multiplePromptValue;
//         // }
//         // 解决 multi 无论如何都发送
//         let promptPrefix = suggestedPrompt;
//         if (activePromptSystem === 'suggestions') {
//           // 使用建议提示
//           value = state.value.trim();
//           // 确保不使用多选提示
//           // 即使输入为空且有多选提示值，也不使用多选提示
//         }
//         else if (activePromptSystem === 'multiple') {
//           // 使用多选提示
//           if (value.length === 0 && multiplePromptValue && !onlyDefaultOptionSelected) {
//             value = multiplePromptValue;
//           }
//           // 不添加建议提示前缀
//           promptPrefix = '';
//         }
//
//
//         // If document builder is set to MOU and both parties are selected, use the MOU template
//         if (selectedDocumentBuilder.value === "dua" &&
//             selectedParty1.value &&
//             selectedParty2.value) {
//
//           // Get current date for the template
//           const currentDate = formatDate(new Date());
//           // Set effective date as 1st of next month
//           const nextMonth = new Date();
//           nextMonth.setMonth(nextMonth.getMonth() + 1);
//           nextMonth.setDate(1);
//           const effectiveDate = formatDate(nextMonth);
//
//           // Get user name for the template
//           const authorName = userName ? `${userName.firstName}` : "Author Name";
//           const approverName = userName ? `${userName.firstName}` : "Approver Name";
//
//           // Replace [party1], [party2], dates and names in the template
//           let formattedPrompt = duaPromptTemplate
//               .replace(/\[party1\]/g, selectedParty1.label)
//               .replace(/\[party2\]/g, selectedParty2.label)
//               .replace(/\[currentDate\]/g, currentDate)
//               .replace(/\[effectiveDate\]/g, effectiveDate)
//               .replace(/\[authorName\]/g, authorName)
//               .replace(/\[approverName\]/g, approverName);
//
//           // Use the formatted prompt as the input value
//           value = formattedPrompt;
//         }
//
//         // Clear the state value
//         setState((state) => ({
//           ...state,
//           value: "",
//         }));
//
//         // Clear the Lexical editor content
//         const editor = (window as any).lexicalEditor;
//         if (editor) {
//           // Import might be needed if not available in this scope
//           // import { SET_EDITOR_CONTENT_COMMAND } from './path/to/LexicalRichTextEditor';
//           editor.dispatchCommand(SET_EDITOR_CONTENT_COMMAND, {
//             content: "",
//             isLongContent: false,
//             removeMaxHeight: false
//           });
//         }
//
//         setFiles([]);
//
//         props.setConfiguration({
//           ...props.configuration,
//           files: [],
//         });
//
//         props.setRunning(true);
//         messageHistoryRef.current = [
//           ...messageHistoryRef.current,
//
//           {
//             type: ChatBotMessageType.Human,
//             content: promptPrefix  + value,
//             metadata: {
//               ...props.configuration,
//             },
//             tokens: [],
//             chatId: props.chatId,
//           },
//           {
//             type: ChatBotMessageType.AI,
//             tokens: [],
//             content: "",
//             metadata: {},
//             chatId: props.chatId,
//           },
//         ];
//
//         props.setMessageHistory(messageHistoryRef.current);
//
//         let response = undefined;
//         const isCFGModel = state.selectedModel?.value?.startsWith("cfggpt");
//
//         const foundModel = _.find(state.models, {
//           name: state.selectedModel?.label,
//         });
//
//         if (foundModel) {
//           let selectedModelName = isCFGModel
//               ? foundModel?.modelId
//               : state.selectedModel?.label;
//
//           /*Temp fix*/
//           if(!userRoles?.includes("OWNER")){
//             provider =  "bedrock",
//                 selectedModelName = "anthropic.claude-3-5-sonnet-20240620-v1:0"
//                 // selectedModelName = "anthropic.claude-3-haiku-20240307-v1:0"
//           }
//
//           if (selectedModelName) {
//             let followupPromptId =
//                 StorageHelper.getSelectedFollowupPromptId() !== null
//                     ? StorageHelper.getSelectedFollowupPromptId()
//                     : "";
//             const payload = {
//               // userId: "<EMAIL>",
//               userId: userEmail,
//               data: {
//                 provider: provider,
//                 modelName: selectedModelName,
//                 mode: ChatBotMode.Chain,
//                 text: value,
//                 files: uploadFiles || [],
//                 sessionId: props.session.id,
//                 workspaceId: state.selectedWorkspace?.value,
//                 promptId: _.isEmpty(state.selectedPrompt?.value)
//                     ? undefined
//                     : state.selectedPrompt?.value,
//                 // promptId: 'b02a61a3-409c-48cb-a6b4-db312a71577a',
//                 modelKwargs: {
//                   streaming: props.configuration.streaming,
//                   useHistory: props.configuration.useHistory,
//                   followPromptId: followupPromptId,
//                   maxTokens: props.configuration.maxTokens,
//                   temperature: props.configuration.temperature,
//                   topP: props.configuration.topP,
//                   numDocs: props.configuration.numDocs,
//                 },
//                 chatId: props.chatId,
//               },
//             };
//
//             try {
//               response = await chatService.sendQuery(payload);
//               dispatch(chatActions?.setChatId(payload.data.chatId));
//               dispatch(
//                   chatActions?.handleChatDocuments(
//                       response?.data?.metadata?.documents
//                   )
//               );
//             } catch (error) {
//               if ((error as any) || (error as any).response?.status !== 200) {
//                 // console.log('Error',error.response.status, 'Please try again or contact support.');
//                 messageHistoryRef.current[
//                 messageHistoryRef.current.length - 1
//                     ].content = "NA";
//               }
//               props.setRunning(false);
//             }
//
//             updateMessageHistoryRef(
//                 props.session.id,
//                 messageHistoryRef.current,
//                 response
//             );
//           }
//         }
//
//         if (
//             response?.action === ChatBotAction.FinalResponse ||
//             response?.action === ChatBotAction.Error
//         ) {
//           console.log("Final message received");
//           props.setRunning(false);
//         }
//         props.setMessageHistory([...messageHistoryRef.current]);
//         // Clear the PDF after sending
//         setUploadedPDF(null);
//       };
//
//   /**
//    * Handles the feedback submission
//    */
//   const connectionStatus = {
//     [ReadyState.CONNECTING]: "Connecting",
//     [ReadyState.OPEN]: "Open",
//     [ReadyState.CLOSING]: "Closing",
//     [ReadyState.CLOSED]: "Closed",
//     [ReadyState.UNINSTANTIATED]: "Uninstantiated",
//   }[readyState];
//
//   const modelsOptions = OptionsHelper.getSelectOptionGroups(state.models || []);
//
//   const workspaceOptions = [
//     ...workspaceDefaultOptions,
//     ...OptionsHelper.getSelectWorkspaceOptions(state.workspaces || []),
//   ];
//
//   const promptsOptions = [
//     ...promptDefaultOptions,
//     ...OptionsHelper.getSelectPromptOptions(state.prompts || []),
//   ];
//
//   // prompt text fun
//   // ①增加bold和高亮
//   const handleOptionsClick = (promptText: string) => {
//     // 设置活动提示系统
//     setActivePromptSystem('suggestions');
//     // Reset document builder
//     setSelectedDocumentBuilder(documentBuilderOptions[0]);
//     // Reset parties
//     setSelectedParty1(party1Options[0]);
//     setSelectedParty2(party2Options[0]);
//
//     // Trigger reset in MultiplePromptSuggestions
//     setResetMultiplePrompts(true);
//
//     // 清空多选提示的值
//     setMultiplePromptValue("");
//     setOnlyDefaultOptionSelected(true);
//
//     // Reset the reset trigger after a short delay
//     setTimeout(() => {
//       setResetMultiplePrompts(false);
//     }, 100);
//
//     // Create a special JSON state for Lexical that includes formatting
//     const lexicalState = createFormattedLexicalState(promptText);
//
//     setState((state) => ({
//       ...state,
//       value: promptText,
//     }));
//
//     setTimeout(() => {
//       const editorContainer = document.querySelector('.lexical-editor-container');
//       if (editorContainer) {
//         const contentLength = promptText.length;
//         const screenHeight = window.innerHeight;
//
//         if (contentLength > 3000 || promptText.includes('Data Use Agreement')) {
//           const mainContainer = document.querySelector('.editor-border') as HTMLElement;
//           if (mainContainer) {
//             mainContainer.style.maxHeight = 'none';
//
//             if (promptText.includes('Data Use Agreement')) {
//               mainContainer.classList.add('dua-content');
//             }
//           }
//         }
//
//         const editor = (window as any).lexicalEditor;
//         if (editor) {
//           // editor.dispatchCommand(SET_EDITOR_CONTENT_COMMAND, {
//           //   content: promptText,
//           //   isLongContent: contentLength > 3000 || promptText.includes('Data Use Agreement'),
//           //   screenHeight: screenHeight,
//           //   removeMaxHeight: true
//           // });
//           // Use the pre-formatted Lexical state
//           editor.setEditorState(editor.parseEditorState(lexicalState));
//         } else {
//           // For the custom event approach
//           const event = new CustomEvent('set-editor-content', {
//             detail: {
//               content: promptText,
//               lexicalState: lexicalState, // Pass the formatted state
//               isLongContent: contentLength > 3000 || promptText.includes('Data Use Agreement'),
//               screenHeight: screenHeight,
//               removeMaxHeight: true
//             },
//             bubbles: true
//           });
//           editorContainer.dispatchEvent(event);
//         }
//       }
//     }, 100);
//   };
//
//   // 多选①
//   useEffect(() => {
//     // 监听组合提示词变化事件
//     const handleMultiplePromptChange = (event) => {
//       setMultiplePromptValue(event.detail.prompt);
//       setOnlyDefaultOptionSelected(event.detail.onlyDefaultSelected);
//
//       // Always update the state and editor regardless of whether only default is selected
//       setState((state) => ({
//         ...state,
//         value: event.detail.prompt,
//       }));
//
//       // Use a timeout to avoid interrupting the dropdown interaction
//       setTimeout(() => {
//         const editor = (window as any).lexicalEditor;
//         if (editor) {
//           // Check if the event contains a pre-formatted lexicalState
//           if (event.detail.lexicalState) {
//             try {
//               // Use the pre-formatted state if available
//               const editorState = editor.parseEditorState(event.detail.lexicalState);
//               editor.setEditorState(editorState);
//             } catch (error) {
//               console.error("Error setting formatted editor state:", error);
//               // Fallback to standard content setting
//               editor.dispatchCommand(SET_EDITOR_CONTENT_COMMAND, {
//                 content: event.detail.prompt,
//                 isLongContent: event.detail.prompt.length > 3000,
//                 removeMaxHeight: true
//               });
//             }
//           } else {
//             // No lexicalState provided, create one with formatting for placeholders
//             const formattedLexicalState = createFormattedLexicalState(event.detail.prompt);
//             try {
//               const editorState = editor.parseEditorState(formattedLexicalState);
//               editor.setEditorState(editorState);
//             } catch (error) {
//               console.error("Error setting dynamically formatted editor state:", error);
//               // Fallback to standard content setting
//               editor.dispatchCommand(SET_EDITOR_CONTENT_COMMAND, {
//                 content: event.detail.prompt,
//                 isLongContent: event.detail.prompt.length > 3000,
//                 removeMaxHeight: true
//               });
//             }
//           }
//         }
//       }, 0);
//     };
//
//     document.addEventListener('multiple-prompt-change', handleMultiplePromptChange);
//
//     return () => {
//       document.removeEventListener('multiple-prompt-change', handleMultiplePromptChange);
//     };
//   }, []);
//
//   const handleInputClick = () =>{
//     if(state.value.includes("[TEXT GOES HERE]")){
//       const updatedValue = state.value.replace("[TEXT GOES HERE]", "[]")
//       setState((state)=>({
//         ...state,
//         value: updatedValue,
//       }))
//       const cursorPosition = updatedValue.indexOf("[") + 1;
//       setTimeout(()=>{
//         inputRef.current.setSelectionRange(cursorPosition, cursorPosition)
//       },0)
//     }
//   }
//
//   const handleInputDelete = () => {
//     setState((state) => ({
//       ...state,
//       value: '',
//     }));
//
//     const editor = (window as any).lexicalEditor;
//     if (editor) {
//       editor.dispatchCommand(SET_EDITOR_CONTENT_COMMAND, {
//         content: "",
//         isLongContent: false,
//         removeMaxHeight: false
//       });
//
//       editor.focus();
//     } else {
//       const editorContainer = document.querySelector('.lexical-editor-container');
//       if (editorContainer) {
//         const event = new CustomEvent('set-editor-content', {
//           detail: {
//             content: "",
//             isLongContent: false,
//             removeMaxHeight: false
//           },
//           bubbles: true
//         });
//         editorContainer.dispatchEvent(event);
//
//         const contentEditable = document.querySelector('.lexical-content-editable') as HTMLElement;
//         if (contentEditable) {
//           contentEditable.focus();
//         }
//       }
//     }
//
//     const contentEditable = document.querySelector('.lexical-content-editable') as HTMLElement;
//     if (contentEditable) {
//       contentEditable.classList.remove('long-content-mode');
//       contentEditable.classList.remove('expanded');
//       contentEditable.style.minHeight = '50px';
//     }
//
//     const editorContainer = document.querySelector('.editor-content-container') as HTMLElement;
//     if (editorContainer) {
//       editorContainer.style.minHeight = '';
//     }
//
//     const editorBorder = document.querySelector('.editor-border');
//     if (editorBorder) {
//       editorBorder.classList.remove('dua-content');
//     }
//   }
//
//   return (
//       <div>
//         <SpaceBetween direction="vertical" size="l">
//           <div className={`${styles.chat_input_container} {messageHistory.length > 0 ? ${styles.input_container_space} : ''}`}>
//             <Container>
//               <div>
//                 <div className={styles.input_textarea_container}>
//                   <SpaceBetween size="xxs" direction="horizontal" alignItems="center">
//                   </SpaceBetween>
//                   <ImageDialog
//                       sessionId={props.session.id}
//                       visible={imageDialogVisible}
//                       setVisible={setImageDialogVisible}
//                       configuration={props.configuration}
//                       setConfiguration={props.setConfiguration}
//                   />
//                   <div className={` ${styles.prompts_input_container} ${suggestedPrompt ? styles.no_suggestion : styles.suggestion}`}>
//                     {/*<TextareaAutosize*/}
//                     {/*    className={`${styles.input_textarea} ${state.value.length > 500 ? 'expanded' : ''}`}*/}
//                     {/*    ref={inputRef}*/}
//                     {/*    maxRows={20}*/}
//                     {/*    minRows={1}*/}
//                     {/*    cacheMeasurements*/}
//                     {/*    spellCheck={true}*/}
//                     {/*    autoFocus={true}*/}
//                     {/*    onClick={(e) => {*/}
//                     {/*      const target = e.target as HTMLTextAreaElement;*/}
//                     {/*      const currentHeight = target.style.height;*/}
//                     {/*      handleInputClick();*/}
//                     {/*      setTimeout(() => {*/}
//                     {/*        if (inputRef.current && currentHeight) {*/}
//                     {/*          inputRef.current.style.height = currentHeight;*/}
//                     {/*        }*/}
//                     {/*      }, 0);*/}
//                     {/*    }}*/}
//                     {/*    onChange={(e) => {*/}
//                     {/*      const currentHeight = inputRef.current ? inputRef.current.style.height : 'auto';*/}
//                     {/*      setState((state) => ({*/}
//                     {/*        ...state,*/}
//                     {/*        value: e.target.value,*/}
//                     {/*      }));*/}
//                     {/*      setTimeout(() => {*/}
//                     {/*        if (inputRef.current) {*/}
//                     {/*          if (e.target.value.length > 500) {*/}
//                     {/*            inputRef.current.classList.add('expanded');*/}
//                     {/*          } else if (e.target.value.length < 100) {*/}
//                     {/*            inputRef.current.classList.remove('expanded');*/}
//                     {/*          }*/}
//                     {/*          const newHeight = Math.max(*/}
//                     {/*              parseInt(currentHeight || '38', 10),*/}
//                     {/*              inputRef.current.scrollHeight*/}
//                     {/*          );*/}
//
//                     {/*          inputRef.current.style.height = `${newHeight}px`;*/}
//                     {/*        }*/}
//                     {/*      }, 0);*/}
//                     {/*    }}*/}
//                     {/*    onFocus={(e) => {*/}
//                     {/*      if (inputRef.current) {*/}
//                     {/*        inputRef.current.dataset.previousHeight = inputRef.current.style.height;*/}
//                     {/*      }*/}
//                     {/*    }}*/}
//                     {/*    onBlur={(e) => {*/}
//                     {/*      if (inputRef.current && state.value.trim().length > 0) {*/}
//                     {/*        const prevHeight = inputRef.current.dataset.previousHeight;*/}
//                     {/*        if (prevHeight) {*/}
//                     {/*          inputRef.current.style.height = prevHeight;*/}
//                     {/*        }*/}
//                     {/*      }*/}
//                     {/*    }}*/}
//                     {/*    onKeyDown={(e) => {*/}
//                     {/*      if (e.key == "Enter" && !e.shiftKey) {*/}
//                     {/*        e.preventDefault();*/}
//
//                     {/*        // Only send if input has content OR MOU is selected with parties*/}
//                     {/*        if (state.value.trim().length > 0 || (selectedDocumentBuilder.value === "mou" && selectedParty1.value && selectedParty2.value)) {*/}
//                     {/*          handleSendMessage();*/}
//                     {/*        }*/}
//                     {/*      }*/}
//                     {/*    }}*/}
//                     {/*    value={state.value}*/}
//                     {/*    placeholder={listening ? "Listening..." : (selectedDocumentBuilder.value === "mou" && selectedParty1.value && selectedParty2.value) ? "Press Enter to generate MOU" : "Tell us how you would like to modify the document"}*/}
//                     {/*/>*/}
//                     <LexicalRichTextEditor
//                         placeholder={listening ? "Listening..." : (selectedDocumentBuilder.value === "dua" && selectedParty1.value && selectedParty2.value) ? "Press Enter to generate DUA" : "Tell us how you would like to modify the document"}
//                         initialValue={state.value}
//                         onChange={(text) => {
//                           setState((state) => ({
//                             ...state,
//                             value: text,
//                           }));
//                         }}
//                         onSubmit={(text) => {
//                           // Only send if input has content OR MOU is selected with parties
//                           if (text.trim().length > 0 || (selectedDocumentBuilder.value === "dua" && selectedParty1.value && selectedParty2.value)) {
//                             setState((state) => ({
//                               ...state,
//                               value: text,
//                             }));
//                             // handleSendMessage();
//                           }
//                         }}
//                         maxHeight={300}
//                     />
//                   </div>
//                   <div style={{ marginLeft: "8px", maxWidth:userRoles.includes("OWNER") ? '400px' : '370px', display:'flex', flexDirection:'column', alignItems:'end' }}>
//                     {state.selectedModelMetadata?.inputModalities.includes(
//                             ChabotInputModality.Image
//                         ) &&
//                         files.length > 0 &&
//                         files.map((file, idx) => (
//                             <img
//                                 key={idx}
//                                 onClick={() => setImageDialogVisible(true)}
//                                 src={file.url}
//                                 style={{
//                                   borderRadius: "4px",
//                                   cursor: "pointer",
//                                   maxHeight: "30px",
//                                   float: "left",
//                                   marginRight: "8px",
//                                 }}
//                             />
//                         ))}
//                     {documentListVisible && uploadFiles.length > 0 && uploadingStatus !== 'error' && (
//                         <div
//                             ref={alertContainerRef}
//                             style={{
//                               position: 'absolute',
//                               zIndex: 10,
//                               bottom: userRoles.includes("OWNER") ? '130px' : '85px',
//                               right: '5px',
//                               maxWidth: '600px',
//                               display: 'flex',
//                               alignItems: 'flex-end',
//                               gap: '10px'
//                             }}
//                         >
//                           <div style={{
//                             flex: 1,
//                             minWidth: '250px',
//                             maxWidth: '300px'
//                           }}>
//                             <Alert
//                                 statusIconAriaLabel="Success"
//                                 type="success"
//                                 header="Attached Documents"
//                             >
//                               <div style={{height: '100px', overflowY: 'auto'}}>
//                                 <ul style={{margin: 0, padding: '0 0 0 20px'}}>
//                                   {uploadFiles.map((file, index) => (
//                                       <li style={{listStyleType: 'decimal'}} key={index}>
//                                         {file.fileKey.split('/').pop()}
//                                       </li>
//                                   ))}
//                                 </ul>
//                               </div>
//                             </Alert>
//                           </div>
//                           {failedDocuments.length > 0 &&
//                               <div style={{
//                                 flex: 1,
//                                 minWidth: '250px',
//                                 maxWidth: '300px'
//                               }}>
//                                 <Alert
//                                     statusIconAriaLabel="Error"
//                                     type="error"
//                                     header="Failed Documents"
//                                 >
//                                   <div style={{height: '100px', overflowY: 'auto'}}>
//                                     <ul style={{margin: 0, padding: '0 0 0 20px'}}>
//                                       {failedDocuments.map((fileName, index) => (
//                                           <li style={{listStyleType: 'decimal'}} key={index}>
//                                             {fileName}
//                                           </li>
//                                       ))}
//                                     </ul>
//                                   </div>
//                                 </Alert>
//                               </div>
//                           }
//                         </div>
//                     )}
//                     {(
//                         <SpaceBetween direction="vertical" size="s">
//                           <div
//                               ref={onClickAreaRef}
//                               style={{cursor: 'pointer'}}
//                               onClick={() => setDocumentListVisible(!documentListVisible)}
//                               title="Click to view uploaded documents."
//                           >
//                             {uploadFiles.length > 0 && (
//                                 <StatusIndicator type="success">
//                                   {uploadFiles.length === 1
//                                       ? "1 Document Uploaded"
//                                       : `${uploadFiles.length} Documents Uploaded`}
//                                 </StatusIndicator>
//                             )}
//                           </div>
//                           {documentStatus === 'processing' && (
//                               <StatusIndicator colorOverride="blue" type="loading">
//                                 Uploading document(s)
//                               </StatusIndicator>
//                           )}
//                           {((uploadingStatus === 'error' || documentStatus === 'failed' || documentStatus === 'wrongexttype') && uploadFiles.length === 0) && (
//                               <StatusIndicator type="error">
//                                 {documentStatus === 'failed' ? validationErrorMessage :
//                                     documentStatus === 'wrongexttype' ? "Please upload only .pdf or .docx files." :
//                                         "An error occurred during upload."}
//                               </StatusIndicator>
//                           )}
//                           {(uploadingStatus === 'error' || documentStatus === 'failed' || documentStatus === 'wrongexttype') && uploadFiles.length > 0 && (
//                               <StatusIndicator type="error">
//                                 {documentStatus === 'failed' ? (
//                                     validationErrorMessage
//                                 ) : (
//                                     <>
//                                       Some files could not be uploaded.
//                                       <br />
//                                       Please upload only .pdf or .docx files.
//                                     </>
//                                 )}
//                               </StatusIndicator>
//                           )}
//                         </SpaceBetween>
//                     )}
//                     <div style={{paddingTop: '5px'}}>
//                       {/*{!uploadedPDF && (*/}
//                       {/*    <PDFUploadButton*/}
//                       {/*        onFilesSelect={handlePDFUpload}*/}
//                       {/*        currentFiles={uploadedPDF}*/}
//                       {/*        onClear={clearUploadedPDFs}*/}
//                       {/*        disabled={documentStatus === 'processing' || state.selectedWorkspace.label !== 'Select'}*/}
//                       {/*    />*/}
//                       {/*)}*/}
//                       <ConfigDialog
//                           sessionId={props.session.id}
//                           visible={configDialogVisible}
//                           setVisible={setConfigDialogVisible}
//                           configuration={props.configuration}
//                           setConfiguration={props.setConfiguration}
//                           selectedModel={state.selectedModel}
//                       />
//                       {/*{(userRoles?.includes("OWNER")) && <Button*/}
//                       {/*    iconName="status-in-progress"*/}
//                       {/*    variant="icon"*/}
//                       {/*    iconUrl={adminSettingsIcon}*/}
//                       {/*    onClick={() => setConfigDialogVisible(true)}*/}
//                       {/*/>}*/}
//                       {/*<button*/}
//                       {/*    className={styles.clear_input_btn}*/}
//                       {/*    disabled={ state.value.trim().length === 0}*/}
//                       {/*    onClick={() => {handleInputDelete();}}*/}
//                       {/*>Clear*/}
//                       {/*</button>*/}
//                       {/*<button*/}
//                       {/*    className={styles.send_input_btn}*/}
//                       {/*    disabled={*/}
//                       {/*      // readyState !== ReadyState.OPEN ||*/}
//                       {/*        !state.models?.length ||*/}
//                       {/*        !state.selectedModel ||*/}
//                       {/*        props.running ||*/}
//                       {/*        documentStatus === 'processing' ||*/}
//                       {/*        // Only require input field value if document builder is not MOU with both parties selected*/}
//                       {/*        (state.value.trim().length === 0 && !(selectedDocumentBuilder.value === "mou" && selectedParty1.value && selectedParty2.value)) ||*/}
//                       {/*        props.session.loading ||*/}
//                       {/*        (!state.selectedPrompt?.value?.length &&*/}
//                       {/*            !state.workspaces?.length)*/}
//                       {/*    }*/}
//                       {/*    onClick={() => {handleSendMessage();}}*/}
//                       {/*>*/}
//                       {/*  {props.running ? (*/}
//                       {/*      <>*/}
//                       {/*        Loading&nbsp;&nbsp;*/}
//                       {/*        <Spinner />*/}
//                       {/*      </>*/}
//                       {/*  ) :  "Send"*/}
//                       {/*  }*/}
//                       {/*</button>*/}
//                     </div>
//                   </div>
//                 </div>
//                 <div >
//                   {(userRoles?.includes("OWNER") || userRoles.includes("EDIT")) && <hr/>}
//                   {(userRoles?.includes("OWNER") || userRoles.includes("EDIT")) && documentRepoDialogVisible &&
//                       <div className={styles.document_library_container}>
//                         {/*{(userRoles?.includes("OWNER")) &&*/}
//                         {/*    <>*/}
//                         {/*      <FormField label="Model" />*/}
//                         {/*      <span>&nbsp;</span>*/}
//                         {/*      <Select*/}
//                         {/*          disabled={props.running}*/}
//                         {/*          statusType={state.modelsStatus}*/}
//                         {/*          loadingText="Loading models (might take few seconds)..."*/}
//                         {/*          placeholder="Select a model"*/}
//                         {/*          empty={*/}
//                         {/*            <div>*/}
//                         {/*              No models available. Please make sure you have access to*/}
//                         {/*              Amazon Bedrock or alternatively deploy a self hosted model on*/}
//                         {/*              SageMaker or add API_KEY to Secrets Manager*/}
//                         {/*            </div>*/}
//                         {/*          }*/}
//                         {/*          filteringType="auto"*/}
//                         {/*          selectedOption={state.selectedModel}*/}
//                         {/*          onChange={({ detail }) => {*/}
//                         {/*            setState((state) => ({*/}
//                         {/*              ...state,*/}
//                         {/*              selectedModel: detail.selectedOption,*/}
//                         {/*              selectedModelMetadata: getSelectedModelMetadata(*/}
//                         {/*                  state.models,*/}
//                         {/*                  detail.selectedOption*/}
//                         {/*              ),*/}
//                         {/*            }));*/}
//                         {/*            if (detail.selectedOption?.value) {*/}
//                         {/*              StorageHelper.setSelectedLLM(detail.selectedOption.value);*/}
//                         {/*            }*/}
//                         {/*          }}*/}
//                         {/*          options={modelsOptions}*/}
//                         {/*      />*/}
//                         {/*    </>*/}
//                         {/*}*/}
//                         <span>&nbsp;&nbsp;&nbsp;</span>
//                         {/*{(userRoles?.includes("OWNER")) &&*/}
//                         {/*    <>*/}
//                         {/*      <FormField label="Prompt" />*/}
//                         {/*      <span>&nbsp;</span>*/}
//                               {/*<Select*/}
//                               {/*    disabled={props.running}*/}
//                               {/*    statusType={state.promptsStatus}*/}
//                               {/*    loadingText="Loading prompts (might take few seconds)..."*/}
//                               {/*    placeholder="Select a prompt"*/}
//                               {/*    empty={<div>No prompts available.</div>}*/}
//                               {/*    filteringType="auto"*/}
//                               {/*    selectedOption={state.selectedPrompt}*/}
//                               {/*    onChange={({ detail }) => {*/}
//                               {/*      console.log("Selected prompt content:----------------》》》》》》》", {*/}
//                               {/*        name: detail.selectedOption.label,*/}
//                               {/*        value: detail.selectedOption.value,*/}
//                               {/*        promptText: prompts.find(p => p.id === detail.selectedOption.value)?.prompt || "Prompt content not found"*/}
//                               {/*      });*/}
//
//                               {/*      setState((state) => ({*/}
//                               {/*        ...state,*/}
//                               {/*        selectedPrompt: detail.selectedOption,*/}
//                               {/*      }));*/}
//                               {/*      if (detail.selectedOption?.value) {*/}
//                               {/*        StorageHelper.setSelectedPromptId(*/}
//                               {/*            detail.selectedOption.value*/}
//                               {/*        );*/}
//                               {/*      }*/}
//                               {/*    }}*/}
//                               {/*    options={promptsOptions}*/}
//                               {/*/>*/}
//                         {/*      <Select*/}
//                         {/*          disabled={props.running}*/}
//                         {/*          statusType={state.promptsStatus}*/}
//                         {/*          loadingText="Loading prompts (might take few seconds)..."*/}
//                         {/*          placeholder="Select a prompt"*/}
//                         {/*          empty={<div>No prompts available.</div>}*/}
//                         {/*          filteringType="auto"*/}
//                         {/*          selectedOption={state.selectedPrompt}*/}
//                         {/*          onChange={({ detail }) => {*/}
//                         {/*            try {*/}
//                         {/*              setState((state) => ({*/}
//                         {/*                ...state,*/}
//                         {/*                selectedPrompt: detail.selectedOption,*/}
//                         {/*              }));*/}
//
//                         {/*              // 存储选择的 prompt ID*/}
//                         {/*              StorageHelper.setSelectedPromptId(*/}
//                         {/*                  detail.selectedOption?.value ?? ""*/}
//                         {/*              );*/}
//
//                         {/*              // 如果是 claude_follow_up，添加额外调试*/}
//                         {/*              if (detail.selectedOption?.label === "claude_follow_up") {*/}
//                         {/*                console.log("Claude follow-up prompt selected:", detail.selectedOption);*/}
//                         {/*                const promptObj = prompts.find(p => p.id === detail.selectedOption.value);*/}
//                         {/*                console.log("Full prompt object:", promptObj);*/}
//                         {/*              }*/}
//                         {/*            } catch (error) {*/}
//                         {/*              console.error("Error when selecting prompt:", error);*/}
//                         {/*              console.error("Selected option:", detail.selectedOption);*/}
//                         {/*              console.error("Current state:", state);*/}
//                         {/*            }*/}
//                         {/*          }}*/}
//                         {/*          options={promptsOptions}*/}
//                         {/*      />*/}
//                         {/*    </>*/}
//                         {/*}*/}
//                         {/*原来的位置，现在改为外面了*/}
//                         {/* Document builder field - new addition */}
//                         {/*<FormField label="Document builder" />*/}
//                         {/*<span>&nbsp;</span>*/}
//                         {/*<Select*/}
//                         {/*    disabled={props.running}*/}
//                         {/*    placeholder="Select document type"*/}
//                         {/*    selectedOption={selectedDocumentBuilder}*/}
//                         {/*    options={documentBuilderOptions}*/}
//                         {/*    onChange={({ detail }) => {*/}
//                         {/*      setSelectedDocumentBuilder(detail.selectedOption);*/}
//                         {/*      // Reset party selections when document type changes*/}
//                         {/*      setSelectedParty1(party1Options[0]);*/}
//                         {/*      setSelectedParty2(party2Options[0]);*/}
//                         {/*    }}*/}
//                         {/*/>*/}
//
//                         {/*/!* Only show Party1 and Party2 fields when MOU is selected *!/*/}
//                         {/*{selectedDocumentBuilder.value === "mou" && (*/}
//                         {/*    <>*/}
//                         {/*      <span>&nbsp;&nbsp;&nbsp;</span>*/}
//                         {/*      <FormField label="Party 1" />*/}
//                         {/*      <span>&nbsp;</span>*/}
//                         {/*      <Select*/}
//                         {/*          disabled={props.running}*/}
//                         {/*          placeholder="Select Party 1"*/}
//                         {/*          selectedOption={selectedParty1}*/}
//                         {/*          options={party1Options}*/}
//                         {/*          onChange={({ detail }) => setSelectedParty1(detail.selectedOption)}*/}
//                         {/*      />*/}
//
//                         {/*      <span>&nbsp;&nbsp;&nbsp;</span>*/}
//                         {/*      <FormField label="Party 2" />*/}
//                         {/*      <span>&nbsp;</span>*/}
//                         {/*      <Select*/}
//                         {/*          disabled={props.running}*/}
//                         {/*          placeholder="Select Party 2"*/}
//                         {/*          selectedOption={selectedParty2}*/}
//                         {/*          options={party2Options}*/}
//                         {/*          onChange={({ detail }) => setSelectedParty2(detail.selectedOption)}*/}
//                         {/*      />*/}
//                         {/*    </>*/}
//                         {/*)}*/}
//
//                         <span>&nbsp;&nbsp;&nbsp;</span>
//
//                         {!uploadedPDF && (
//                             <PDFUploadButton
//                                 onFilesSelect={handlePDFUpload}
//                                 currentFiles={uploadedPDF}
//                                 onClear={clearUploadedPDFs}
//                                 disabled={documentStatus === 'processing' || state.selectedWorkspace.label !== 'Select'}
//                             />
//                         )}
//
//                         {(userRoles?.includes("OWNER")) && <Button
//                             iconName="status-in-progress"
//                             variant="icon"
//                             iconUrl={adminSettingsIcon}
//                             onClick={() => setConfigDialogVisible(true)}
//                         />}
//                         <button
//                             className={styles.clear_input_btn}
//                             disabled={ state.value.trim().length === 0}
//                             onClick={() => {handleInputDelete();}}
//                         >Clear
//                         </button>
//                         {/*<button*/}
//                         {/*    className={styles.send_input_btn}*/}
//                         {/*    disabled={*/}
//                         {/*      // readyState !== ReadyState.OPEN ||*/}
//                         {/*        !state.models?.length ||*/}
//                         {/*        !state.selectedModel ||*/}
//                         {/*        props.running ||*/}
//                         {/*        documentStatus === 'processing' ||*/}
//                         {/*        // Only require input field value if document builder is not MOU with both parties selected*/}
//                         {/*        (state.value.trim().length === 0 && !(selectedDocumentBuilder.value === "mou" && selectedParty1.value && selectedParty2.value)) ||*/}
//                         {/*        props.session.loading*/}
//                         {/*        // 解决Send按钮不可用*/}
//                         {/*        // || (!state.selectedPrompt?.value?.length && !state.workspaces?.length)*/}
//                         {/*    }*/}
//                         {/*    onClick={() => {handleSendMessage();}}*/}
//                         {/*>*/}
//                         {/*  {props.running ? (*/}
//                         {/*      <>*/}
//                         {/*        Loading&nbsp;&nbsp;*/}
//                         {/*        <Spinner />*/}
//                         {/*      </>*/}
//                         {/*  ) :  "Send"*/}
//                         {/*  }*/}
//                         {/*</button>*/}
//                         <button
//                             className={styles.send_input_btn}
//                             disabled={
//                                 !state.models?.length ||
//                                 !state.selectedModel ||
//                                 props.running ||
//                                 documentStatus === 'processing' ||
//                                 // 关键修改：检查是否只有默认选项
//                                 (state.value.trim().length === 0 &&
//                                     (onlyDefaultOptionSelected || !multiplePromptValue) &&
//                                     !(selectedDocumentBuilder.value === "dua" && selectedParty1.value && selectedParty2.value)) ||
//                                 props.session.loading
//                             }
//                             onClick={() => {handleSendMessage();}}
//                         >
//                           {props.running ? (
//                               <>
//                                 Loading&nbsp;&nbsp;
//                                 <Spinner />
//                               </>
//                           ) :  "Send"
//                           }
//                         </button>
//
//                       </div>
//                   }
//                 </div>
//               </div>
//             </Container>
//           </div>
//         </SpaceBetween>
//         <div style={{ display: 'flex', alignItems: 'flex-start' }}>
//           {state.selectedWorkspace.label === 'Select' &&
//               <PromptSuggestions
//                   suggestedPrompts={suggestedPrompts}
//                   onOptionsClick={handleOptionsClick}
//                   resetDocumentBuilder={() => {
//                     setSelectedDocumentBuilder(documentBuilderOptions[0]);
//                     setSelectedParty1(party1Options[0]);
//                     setSelectedParty2(party2Options[0]);
//                     setResetMultiplePrompts(true);
//                     setTimeout(() => setResetMultiplePrompts(false), 100);
//                   }}
//               />}
//           <span>&nbsp;&nbsp;&nbsp;</span>
//
//           {/* Document builder field - new addition */}
//           {/*<div*/}
//           {/*    ref={containerRef}*/}
//           {/*    style={{*/}
//           {/*      marginTop: `${topOffset+21}px`,*/}
//           {/*      position: 'relative',*/}
//           {/*      marginBottom: '20px'*/}
//           {/*    }}*/}
//           {/*>*/}
//           {/*  <div className={styles.document_library_container} style={{ display: 'flex', alignItems: 'center' }}>*/}
//           {/*    /!* Document builder 组 - 水平布局 *!/*/}
//           {/*    <div style={{ display: 'flex', alignItems: 'center', marginRight: '20px' }}>*/}
//           {/*      <FormField label="Document builder" />*/}
//           {/*      <span>&nbsp;&nbsp;</span>*/}
//           {/*      <Select*/}
//           {/*          disabled={props.running}*/}
//           {/*          placeholder="Select document type"*/}
//           {/*          selectedOption={selectedDocumentBuilder}*/}
//           {/*          options={documentBuilderOptions}*/}
//           {/*          onChange={({ detail }) => {*/}
//           {/*            setSelectedDocumentBuilder(detail.selectedOption);*/}
//           {/*            // Reset party selections when document type changes*/}
//           {/*            setSelectedParty1(party1Options[0]);*/}
//           {/*            setSelectedParty2(party2Options[0]);*/}
//           {/*          }}*/}
//           {/*      />*/}
//           {/*    </div>*/}
//
//           {/*    /!* Only show Party1 and Party2 fields when MOU is selected *!/*/}
//           {/*    {selectedDocumentBuilder.value === "mou" && (*/}
//           {/*        <>*/}
//           {/*          /!* Party 1 组 - 水平布局 *!/*/}
//           {/*          <div style={{ display: 'flex', alignItems: 'center', marginRight: '20px' }}>*/}
//           {/*            <FormField label="Party 1" />*/}
//           {/*            <span>&nbsp;&nbsp;</span>*/}
//           {/*            <Select*/}
//           {/*                disabled={props.running}*/}
//           {/*                placeholder="Select Party 1"*/}
//           {/*                selectedOption={selectedParty1}*/}
//           {/*                options={party1Options}*/}
//           {/*                onChange={({ detail }) => setSelectedParty1(detail.selectedOption)}*/}
//           {/*            />*/}
//           {/*          </div>*/}
//
//           {/*          /!* Party 2 组 - 水平布局 *!/*/}
//           {/*          <div style={{ display: 'flex', alignItems: 'center' }}>*/}
//           {/*            <FormField label="Party 2" />*/}
//           {/*            <span>&nbsp;&nbsp;</span>*/}
//           {/*            <Select*/}
//           {/*                disabled={props.running}*/}
//           {/*                placeholder="Select Party 2"*/}
//           {/*                selectedOption={selectedParty2}*/}
//           {/*                options={party2Options}*/}
//           {/*                onChange={({ detail }) => setSelectedParty2(detail.selectedOption)}*/}
//           {/*            />*/}
//           {/*          </div>*/}
//           {/*        </>*/}
//           {/*    )}*/}
//           {/*  </div>*/}
//           {/*</div>*/}
//           {/*<span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>*/}
//         <MultiplePromptSuggestions
//             running={props.running}
//             resetState={resetMultiplePrompts}
//         />
//       </div>
//         {state.selectedModel && (
//             <ModelNotification
//                 selectedModel={state.selectedModel}
//                 duration={3000}
//             />
//         )}
//       </div>
//   );
// }
//
// function getSelectedWorkspaceOption(
//     workspaces: Workspace[]
// ): SelectProps.Option | null {
//   let selectedWorkspaceOption: SelectProps.Option | null = null;
//
//   const savedWorkspaceId = StorageHelper.getSelectedWorkspaceId();
//   if (savedWorkspaceId) {
//     const targetWorkspace = workspaces.find(
//         (w) => w.workspaceId === savedWorkspaceId
//     );
//
//     if (targetWorkspace) {
//       selectedWorkspaceOption = OptionsHelper.getSelectWorkspaceOptions([
//         targetWorkspace,
//       ])[0];
//     }
//   }
//
//   if (!selectedWorkspaceOption) {
//     selectedWorkspaceOption = workspaceDefaultOptions[0];
//   }
//
//   return selectedWorkspaceOption;
// }