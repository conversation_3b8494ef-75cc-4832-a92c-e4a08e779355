import {
  <PERSON>,
  Button,
  Container,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Spinner,
  StatusIndicator,
  Tabs,
  TextContent,
  Textarea,
  SpaceBetween,
  FormField,
  Checkbox,
} from "@cloudscape-design/components";
import { useEffect, useState } from "react";
import { JsonView, darkStyles } from "react-json-view-lite";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import styles from "../../styles/chat.module.scss";
import {
  ChatBotConfiguration,
  ChatBotHistoryItem,
  ChatBotMessageType,
  ImageFile,
  RagDocument,
} from "./types";
import { getSignedUrl } from "./utils";
import rehypeRaw from 'rehype-raw';
import { Document, Paragraph, TextRun, Packer, AlignmentType, HeadingLevel, Table, TableRow, TableCell, BorderStyle } from 'docx';

import "react-json-view-lite/dist/index.css";
import "../../styles/app.scss";
import { <PERSON>a<PERSON>hum<PERSON>Up, FaThumbsDown } from "react-icons/fa";
import documentService from "../../services/document.service";
// ①
import MSWordDocService from "../../services/exportMS.service";
import { WorkspaceFileDownloadPresignedUrlRequest } from "../../API";
import { Viewer } from "@react-pdf-viewer/core";
import "@react-pdf-viewer/core/lib/styles/index.css";
import { searchPlugin, FlagKeyword } from "@react-pdf-viewer/search";
import "@react-pdf-viewer/search/lib/styles/index.css";
import { defaultLayoutPlugin } from "@react-pdf-viewer/default-layout";
import "@react-pdf-viewer/default-layout/lib/styles/index.css";
import { useDispatch, useSelector } from "react-redux";
import { chatActions } from "../../pages/chatbot/playground/chat.slice";
import { FEEDBACK_REASONS, REQUIRED_TEXT } from "@/common/constants";
import { useLocation } from "react-router-dom";
import cdergptActiveIcon from "../../assets/images/CDERGPT-Active.png"
import cdergptInactiveIcon from "../../assets/images/CDERGPT-Inactive.png"
import userInactive from "../../assets/images/User-Inactive.png"
import copyToClipboardIcon from "../../assets/images/Copy-Icon.png"
import NotificationBell from "../../assets/images/notification-bell.png";
import { saveAs } from "file-saver";
import documentsIcon from "../../assets/images/Documents-Icon.png"
import downloadIcon from "../../assets/images/Download-Icon.png"
import { markdownToDocxAndDownload } from '@/common/download-docx'

export interface ChatMessageProps {
  message: ChatBotHistoryItem;
  configuration?: ChatBotConfiguration;
  showSource?: boolean;
  showMetadata?: boolean;
  chatId: string;
  lastAiResponse: any;
  onThumbsUp: (comment: string) => void;
  onThumbsDown?: (comment: string, selectedFeedbackReasons: string[]) => void;
  hasUploadedFiles?: boolean;
}

export default function ChatMessage(props: ChatMessageProps) {
  const [loading, setLoading] = useState<boolean>(false);
  const dispatch = useDispatch();
  const dataState = useSelector((state: any) => state?.rootReducer);
  const [message] = useState<ChatBotHistoryItem>(props.message);
  const [files, setFiles] = useState<ImageFile[]>([] as ImageFile[]);
  const [documentIndex, setDocumentIndex] = useState("0");
  const [promptIndex, setPromptIndex] = useState("0");
  const [selectedIcon, setSelectedIcon] = useState<1 | 0 | null>(null);
  const [visible, setVisible] = useState(false);
  const [pdfUrl, setPDFUrl] = useState("");
  const [isDocumentLoaded, setDocumentLoaded] = useState(false);
  const [highlightContent, setHighlightContent] = useState("");
  const [initialPage, setInitialPage] = useState(2);
  const [comment, setComment] = useState("");
  const [negativeFeedbackComment, setNegativeFeedbackComment] = useState("");
  const [thumbsUpClicked, setThumbsUpClicked] = useState(false);
  const [thumbsDownClicked, setThumbsDownClicked] = useState(false);
  const [feedbackreasons, setFeedbackReasons] = useState(FEEDBACK_REASONS);
  const [selectedFeedbackReasons, setSelectedFeedbackReasons] = useState([]);
  const showFeedbackMessage = dataState?.chatReducer?.showFeedbackMessage;
  const [hasError, setHasError] = useState(false);
  const [feedbackComment, setFeedbackComment] = useState("");
  const [errorMessage, setErrorMessage] = useState("");
  const [showSourceFeedback,setShowSourceFeedback] = useState({showSources: false, showFeedback: false})
  const {hasUploadedFiles} = props;
  const location = useLocation();
  // ①ms download
  const userName = dataState?.chatReducer?.userName;

  const handleDocumentLoad = () => {
    setDocumentLoaded(true);
  };

  const [currentKeyword, setCurrentKeyword] = useState<FlagKeyword>({
    keyword: "",
    matchCase: false,
    wholeWords: false,
  });

  const searchPluginInstance = searchPlugin();
  const { highlight } = searchPluginInstance;

  const defaultLayoutPluginInstance = defaultLayoutPlugin();

  const closerViewer = () => {
    // setPDFViewerVisible(false)
    setDocumentLoaded(false);
  };

  /**
   * Open Document for Preview
   * @param workspaceId
   * @param fileName
   * @param pageContent
   */
  const openDocument = async (
      workspaceId?: string,
      fileName?: string,
      pageContent?: string
  ) => {
    // open file in a new browser.
    if(!workspaceId){
      workspaceId = props.message.metadata.documents[0].metadata.workspace_id;

    }
    const payload: WorkspaceFileDownloadPresignedUrlRequest = {
      workspaceId: workspaceId,
      fileName: fileName,
    };
    const url = await documentService.presignedWorkspaceFileDownloadPost(payload);
    const response = await fetch(url);
    const blob = await response.blob();
    const pdfBlob = new Blob([blob], { type: "application/pdf" });
    const blobUrl = window.URL.createObjectURL(pdfBlob);
    window.open(blobUrl, "_blank");
    // setPDFViewerVisible(true)
    // setHighlightContent(pageContent)
    // setPDFUrl(url)
  };
  useEffect(() => {
    // Define the click handler function
    const handleClick = (e) => {
      e.preventDefault(); // Prevent default navigation
      const doc = e.currentTarget.getAttribute("data-doc");
      openDocument(null, doc);
    };

    // Attach listener after rendering
    const links = document.querySelectorAll("a[data-doc]");
    links.forEach((link) =>
        link.addEventListener("click", handleClick)
    );

    // Cleanup on unmount
    return () => {
      links.forEach((link) =>
          link.removeEventListener("click", handleClick)
      );
    };
  }, [props.message.content]);

  /**
   * Should Metadata be shown
   * @returns true or false
   */
  const isMetadataShown = () => {
    return (
        (props.showMetadata && props.message.metadata) ||
        (props.message.metadata && props.configuration?.showMetadata)
    );
  };

  /**
   * Is Source Shown
   * @returns true or false
   */
  const isSourceShown = () => {
    return props.showSource || props.configuration?.showSource;
  };

  const handleCheckBoxChange = (id: number) => {
    setFeedbackReasons((prevFeedbackReasons) =>
        prevFeedbackReasons.map((checkbox) =>
            checkbox.id === id
                ? { ...checkbox, checked: !checkbox.checked }
                : checkbox
        )
    );
  };

  /**
   * Highlight the keyword in the document
   */
  useEffect(() => {
    if (isDocumentLoaded) {
      highlight({
        keyword: highlightContent,
        matchCase: true,
        wholeWords: false,
      });
    }
  }, [isDocumentLoaded]);

  /**
   * Get Signed Urls for the files
   */
  useEffect(() => {
    const getSignedUrls = async () => {
      setLoading(true);
      if (message.metadata?.files as ImageFile[]) {
        const files: ImageFile[] = [];
        for await (const file of message.metadata?.files as ImageFile[]) {
          const signedUrl = await getSignedUrl(file.key);
          files.push({
            ...file,
            url: signedUrl as string,
          });
        }

        setLoading(false);
        setFiles(files);
      }
    };

    if (message && (message.metadata?.files as ImageFile[])) {
      getSignedUrls();
    }
  }, [message]);

  /**
   * Reset Feedback
   */
  const handleResetPositiveFeedback = () => {
    setThumbsUpClicked(false);
    setSelectedIcon(null);
    setComment("");
    dispatch(chatActions?.feedbackOpened(false));
    dispatch(chatActions?.setComments(""));
    setErrorMessage("");
  };

  /**
   * Reset Negative Feedback
   */
  const handleResetNegativeFeedback = () => {
    setThumbsDownClicked(false);
    setHasError(false);
    setSelectedIcon(null);
    setNegativeFeedbackComment("");
    setErrorMessage("");
    dispatch(chatActions?.setComments(""));
    setFeedbackReasons((prevFeedbackReasons) =>
        prevFeedbackReasons.map((feedbackReason) => ({
          ...feedbackReason,
          checked: false,
        }))
    );
  };

  /**
   * Handle Thumbs Up Submit
   * @param comment
   */
  const handleThumbsUpSubmit = (comment: string) => {
    if (!errorMessage) {
      props.onThumbsUp(comment);
      setThumbsUpClicked(false);
      setFeedbackComment(comment);
      dispatch(chatActions?.feedbackOpened(false));
      dispatch(chatActions?.showFeedbackMessage(false));
      dispatch(chatActions?.setComments(comment));
      comment?.length > 0 && dispatch(chatActions?.setFeedbackSubmitted(true));
    }
  };

  /**
   * Handle Thumbs Down Submit
   * @param comment
   * @returns
   */
  const handleThumbsDownSubmit = (comment) => {
    const isAnyCheckboxChecked = feedbackreasons.some(
        (checkbox) => checkbox.checked
    );
    if (!isAnyCheckboxChecked || comment?.trim()?.length <= 0) {
      setHasError(true);
      return;
    } else {
      const selectedFeedbackReasons = feedbackreasons
          .filter((checkbox) => checkbox.checked)
          .map((checkbox) => checkbox.label);
      setSelectedFeedbackReasons(selectedFeedbackReasons);
      setFeedbackComment(comment);

      if (!errorMessage) {
        if (
            comment?.trim()?.length > 0 &&
            selectedFeedbackReasons?.length > 0
        ) {
          props.onThumbsDown(comment, selectedFeedbackReasons);
          setThumbsDownClicked(false);
          dispatch(chatActions?.setComments(comment));
          setHasError(false);
        }
      }
    }
  };

  /**
   * Handle Thumbs Up Click
   */
  const handleThumbsUpClick = () => {
    setSelectedIcon(1);
    setThumbsUpClicked(true);
    setThumbsDownClicked(false);
    dispatch(chatActions?.feedbackOpened(true));
    dispatch(chatActions?.handleMessageContent(props.message.content));
    setNegativeFeedbackComment("");
    dispatch(chatActions?.setComments(""));
    setHasError(false);
    setErrorMessage("");
    setFeedbackReasons((prevFeedbackReasons) =>
        prevFeedbackReasons.map((feedbackReason) => ({
          ...feedbackReason,
          checked: false,
        }))
    );
    setSelectedFeedbackReasons([]);
  };

  const handleThumbsDownClick = () => {
    setSelectedIcon(0);
    setThumbsDownClicked(true);
    dispatch(chatActions?.feedbackOpened(true));
    setVisible(true);
    setThumbsUpClicked(false);
    setComment("");
    setHasError(false);
    setErrorMessage("");
  };

  const handleThumbsUpFeedbackChange = (event) => {
    const newValue = event.detail.value;
    setComment(newValue);

    if (newValue?.length <= 4096) {
      setErrorMessage("");
      dispatch(chatActions?.setComments(event.detail.value));
    } else {
      setErrorMessage(
          "Please limit feedback to under 4,096 characters before submitting"
      );
    }
    // setComment(event.detail.value);
  };

  const handleThumbsDownFeedbackChange = (event) => {
    const newValue = event.detail.value;
    setNegativeFeedbackComment(newValue);

    if (newValue?.length <= 4096) {
      setErrorMessage("");
      dispatch(chatActions?.setComments(event.detail.value));
    } else {
      setErrorMessage(
          "Please limit feedback to under 4,096 characters before submitting"
      );
    }
  };

  /**
   * Extract text only from page_content
   */
  const extractText = (content) => {
    const textMatch = content.match(/"Text":\s*"([^"]*)"/);
    return textMatch ? textMatch[1] : null;
  }

  /**
   * Remove HTML tags and keep source link(for chat response 'copy' button)
   */
  const parseHTMLAndKeepLinks = (htmlString) => {
    const parser = new DOMParser()
    const doc = parser.parseFromString(htmlString, "text/html")

    doc.querySelectorAll("a").forEach((anchor) => {
      anchor.replaceWith(`${anchor.textContent} (${anchor.href})`)
    })
    return doc.body.textContent || ""
  }
  const plainText = parseHTMLAndKeepLinks(props.message.content)

  // ①增加新的 download， but use api, this is not good lol
  const downloadMSExport = async () => {
    const payload = {
      content: props.message.content,
      user_name: userName
    };

    try {
      const response = await MSWordDocService.uploadAIResponse(payload);

      const blob = new Blob([response.data], {
        type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'    });

      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      const now = new Date();
      const timestamp = now.toLocaleString('en-US', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      }).replace(/(\d+)\/(\d+)\/(\d+),\s/, '$3-$1-$2 ').replace(',', '');
      a.download = `DUA_output_${timestamp}.docx`;
      document.body.appendChild(a);
      a.click();
      a.remove();
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Download failed:', error);
    }
  };


  /**
   * Render Chat Message
   */
  return (
      <div>
        {props.message?.type === ChatBotMessageType.AI && (
            <div style={{display: 'flex', marginBottom: '25px', marginTop: '-8px'}}>
              {/*<img src={userInactive} alt="" height='45px' style={{marginRight: '8px'}}/>*/}
              <TextContent>
                <strong style={{whiteSpace: "pre-wrap", wordWrap: "break-word"}}>
                  {!props.message.content || props.message.content.length === 0 ? (
                      <div style={{display: 'flex', alignItems: 'center'}}>
                        <span>DUA builder is currently in progress</span>
                        <span className={styles.loadingDots}>
                <span className={styles.dot}></span>
                <span className={styles.dot}></span>
                <span className={styles.dot}></span>
              </span>
                      </div>
                  ) : (
                      "The DUA has been completed, and a Word document is ready for download (Beta)"
                  )}
                </strong>
              </TextContent>
            </div>
        )}
        {props.message?.type === ChatBotMessageType.AI && (
            <div style={{display: 'grid', gridTemplateColumns:'auto 1fr', gap:'8px', alignItems:'flex-start',maxWidth:'100%'}}>
              {/*{props.lastAiResponse ? <img src={cdergptActiveIcon} alt="" height='45px'/> :*/}
              {/*    <img src={cdergptInactiveIcon} alt="" height='45px'/>}*/}
              <div style={{flex:'1'}}>
                <Container disableContentPaddings>
                  <div style={{display:'flex', overflow: 'hidden', width:'100%', position:'relative', backgroundColor:'#f4f4f7', borderRadius:'15px'}}>
                    <div style={{backgroundColor:'white', flex: showSourceFeedback.showSources || showSourceFeedback.showFeedback ? '1': '2', padding:'20px', height:'fit-content', borderRadius: '10px', margin: '10px'}}>
                      {props.message.content === "NA" && (
                          <Box>Error: Please try again.</Box>
                      )}
                      {props.message.content.length === 0 ? (
                          <Box>
                            <Spinner />
                          </Box>
                      ) : null}
                      {props.message.content !== "NA" &&
                      props.message.content.length > 0 ? (
                          <div className={styles.btn_chabot_metadata_copy}>
                            <Popover
                                size="medium"
                                position="top"
                                triggerType="custom"
                                dismissButton={false}
                                content={
                                  <StatusIndicator type="success">
                                    Downloaded as Word document
                                  </StatusIndicator>
                                }
                            >
                              <div style={{display: 'flex', justifyContent:'space-between', alignItems:'center',paddingBottom:'20px'}}>
                              <Button
                                  variant="icon"
                                  iconName="download"
                                  ariaLabel="Download as Word document"
                                  iconUrl={downloadIcon}
                                  data-test-id="download-button"
                                  onClick={() => {
                                    // ①替換純前端，使用api下載
                                    navigator.clipboard.writeText(plainText);
                                    markdownToDocxAndDownload(
                                        props.message.content,
                                        `DUA_Builder_${new Date()
                                            .toISOString()
                                            .slice(0, 10)}.docx`,
                                    )
                                  }}
                                  // onClick={downloadMSExport}
                              />
                              </div>
                            </Popover>
                          </div>
                      ) : null}
                      {props.message.content !== "NA" && (
                          <ReactMarkdown
                              className={styles.markdownContent}
                              children={props.message.content}
                              remarkPlugins={[remarkGfm]}
                              rehypePlugins={[rehypeRaw]}

                              components={{
                                pre(props) {
                                  const { children, className, node, ...rest } = props;
                                  return (
                                      <pre {...rest} className={styles.codeMarkdown}>
                      {children}
                    </pre>
                                  );
                                },
                                table(props) {
                                  const { children, ...rest } = props;
                                  return (
                                      <table {...rest} className={styles.markdownTable}>
                                        {children}
                                      </table>
                                  );
                                },
                                th(props) {
                                  const { children, ...rest } = props;
                                  return (
                                      <th {...rest} className={styles.markdownTableCell}>
                                        {children}
                                      </th>
                                  );
                                },
                                td(props) {
                                  const { children, ...rest } = props;
                                  return (
                                      <td {...rest} className={styles.markdownTableCell}>
                                        {children}
                                      </td>
                                  );
                                },
                              }}
                          />
                      )}

                      {props.message.content !== "NA" && (
                          <div  className={styles.giveFeedbackThumbsContainer} style={{flexDirection: thumbsUpClicked || thumbsDownClicked || showSourceFeedback.showSources ? 'column':'row'}}>
                            <div className={styles.thumbsContainer}>
                              {selectedIcon === null && (
                                  <p className={styles.provideFeedbackMessage}>
                                    Provide Feedback
                                  </p>
                              )}
                              <FaThumbsUp
                                  className={`${styles.thumbsIcon} ${styles.thumbsUp} ${
                                      selectedIcon === 1 ? styles.selected : ""
                                  }`}
                                  style={{
                                    cursor:
                                        negativeFeedbackComment ||
                                        feedbackreasons.some((checkBox) => checkBox.checked)
                                            ? "default"
                                            : "pointer",
                                  }}
                                  onClick={(e) => {
                                    if (
                                        negativeFeedbackComment ||
                                        feedbackreasons.some((checkBox) => checkBox.checked)
                                    ) {
                                      e.preventDefault();
                                      return;
                                    }
                                    handleThumbsUpClick();
                                  }}
                              />
                              {/* {(selectedIcon === 0 || selectedIcon === null) && ( */}
                              <FaThumbsDown
                                  className={`${styles.thumbsIcon} ${styles.thumbsDown} ${
                                      selectedIcon === 0 ? styles.selected : ""
                                  }`}
                                  style={{
                                    cursor: comment ? "default" : "pointer",
                                  }}
                                  onClick={(e) => {
                                    if (comment) {
                                      e.preventDefault();
                                      return;
                                    }
                                    handleThumbsDownClick();
                                    // props.onThumbsDown();
                                    // setSelectedIcon(0);
                                    // setVisible(true);
                                    // setThumbsUpClicked(false);
                                    // setComment('');
                                  }}
                              />
                            </div>
                            {thumbsUpClicked && (
                                <div style={{ marginTop: "10px" }}>
                                  <SpaceBetween direction="vertical" size="l">
                                    <FormField
                                        stretch
                                        label="Additional feedback"
                                        errorText={errorMessage}
                                    >
                                      <Textarea
                                          // onChange={({ detail }) => {
                                          //   setComment(detail.value);
                                          //   dispatch(chatActions?.setComments(detail.value));
                                          // }}
                                          onChange={handleThumbsUpFeedbackChange}
                                          value={comment}
                                          placeholder=""
                                          invalid={!!errorMessage}
                                      />
                                    </FormField>
                                    <div
                                        style={{
                                          display: "flex",
                                          flexDirection: "row",
                                          justifyContent: "flex-end",
                                        }}
                                    >
                                      <SpaceBetween direction="horizontal" size="xs">
                                        <Button
                                            variant="link"
                                            onClick={handleResetPositiveFeedback}
                                        >
                                          Reset Feedback
                                        </Button>
                                        <Button
                                            variant="primary"
                                            onClick={() => {
                                              handleThumbsUpSubmit(comment);
                                            }}
                                            disabled={errorMessage ? true : false}
                                        >
                                          Submit
                                        </Button>
                                      </SpaceBetween>
                                    </div>
                                  </SpaceBetween>
                                </div>
                            )}
                            {thumbsDownClicked && (
                                <div style={{ marginTop: "20px" }}>
                                  <SpaceBetween direction="vertical" size="l">
                                    <FormField
                                        label="*What about the response could have been improved? (Select all that apply)."
                                        errorText={
                                          hasError &&
                                          !feedbackreasons.some((checkBox) => checkBox.checked)
                                              ? "Please select at least one checkbox"
                                              : ""
                                        }
                                    >
                                      <div style={{ marginTop: "5px" }}>
                                        {feedbackreasons.map((checkbox) => (
                                            <Checkbox
                                                key={checkbox.id}
                                                checked={checkbox.checked}
                                                onChange={() => {
                                                  handleCheckBoxChange(checkbox.id);
                                                }}
                                            >
                                              {checkbox.label}
                                            </Checkbox>
                                        ))}
                                      </div>
                                    </FormField>
                                    <FormField
                                        stretch
                                        label="*What changes would make the response very helpful? (If possible, please provide a description or example of an excellent response)."
                                        errorText={
                                          hasError && negativeFeedbackComment.trim().length <= 0
                                              ? "Please enter your feedback"
                                              : errorMessage
                                                  ? errorMessage
                                                  : ""
                                        }
                                    >
                                      <Textarea
                                          onChange={handleThumbsDownFeedbackChange}
                                          value={negativeFeedbackComment}
                                          invalid={!!errorMessage}
                                      />
                                    </FormField>
                                  </SpaceBetween>
                                  <div
                                      style={{
                                        display: "flex",
                                        flexDirection: "row",
                                        justifyContent: "space-between",
                                      }}
                                  >
                                    <div
                                        style={{
                                          marginTop: "7px",
                                          maxWidth:
                                              location.pathname === "/chatbot/multichat"
                                                  ? "185px"
                                                  : "70%",
                                        }}
                                    >
                                      {REQUIRED_TEXT}
                                    </div>
                                    <div
                                        style={{
                                          marginTop: "7px",
                                          display: "flex",
                                          flexDirection: "row",
                                          justifyContent: "flex-end",
                                        }}
                                    >
                                      <SpaceBetween direction="horizontal" size="xs">
                                        <Button
                                            variant="link"
                                            onClick={handleResetNegativeFeedback}
                                        >
                                          {location.pathname === "/chatbot/multichat" ? (
                                              <>
                                                Reset <br /> Feedback
                                              </>
                                          ) : (
                                              "Reset Feedback"
                                          )}
                                        </Button>
                                        <Button
                                            variant="primary"
                                            onClick={() => {
                                              handleThumbsDownSubmit(negativeFeedbackComment);
                                            }}
                                            disabled={errorMessage ? true : false}
                                        >
                                          Submit
                                        </Button>
                                      </SpaceBetween>
                                    </div>
                                  </div>
                                </div>
                            )}
                            {/*<p style={{display:'flex', color:'#0071BC', marginTop:'22px'}}><i>MOU builder can make mistakes. Please verify outputs.</i></p>*/}
                            <div className={styles.warningContainer}>
                              <details className={styles.warningDetails}>
                                <summary className={styles.warningSummary}>
                                  <img src={NotificationBell} alt="icon" height="16px" style={{ marginRight: '6px' }} />
                                  Tips for truncated output
                                </summary>
                                <div className={styles.warningContent}>
                                  <p>
                                    Due to token limitations, the response may be truncated and not include the full content.
                                    If this happens, you may continue by saying:
                                  </p>
                                  <ul className={styles.warningList}>
                                    <li>"Continue from where you left off"</li>
                                    <li>"Generate the rest"</li>
                                    <li>"Generate remaining contents"</li>
                                  </ul>
                                  <p>This will help the model resume and complete the output.</p>
                                </div>
                              </details>
                            </div>
                          </div>
                      )}
                    </div>
                    <div style={{flex:!showSourceFeedback.showSources ? '.5': '1.5', maxWidth:'600px', overflow:'auto', transition:'flex 500ms ease, max-width 500ms ease', padding:'5px', margin: '10px'}}>
                      {props.message.metadata.documents?.length > 0 && !hasUploadedFiles &&
                          <ExpandableSection variant="footer" headerText="Sources" onChange={(prevState) => setShowSourceFeedback((prevState) => ({...prevState, showSources: !prevState.showSources}))}>
                            {props.message.metadata.documents &&
                                props.message.metadata && (
                                    <>
                                      {<p style={{ fontSize: '13px', paddingLeft: '5px' }}>We identified these sources as most relevant to your question, and the application used  them to craft its response.</p>}
                                      <Tabs
                                          tabs={(
                                              props.message.metadata.documents as RagDocument[]
                                          ).map((p: any, i) => {
                                            return {
                                              id: `${i}`,
                                              label: (
                                                  <div className={styles.documentTab}>
                                                    <button className={i === Number(documentIndex) ? styles.documentNameActive : styles.documentName} title={p.metadata.path}>
                                                      {p.metadata.path?.substring(0,15)}...
                                                    </button>
                                                    <div className={styles.documentNewTabCopyContainer}>
                                                    </div>
                                                  </div>
                                              ),
                                              content: (
                                                  <div className={styles.documentContentRow}>
                                                    <div className={styles.documentContentArea}>
                                                      <div style={{display: 'flex', justifyContent:'space-between', alignItems:'center',paddingBottom:'20px'}}>
                                                        <Button
                                                            disabled
                                                            iconUrl={documentsIcon}
                                                        />
                                                        <h2 style={{ marginBottom: "4px",fontWeight:'600', fontSize:'14px', paddingLeft:'10px'}}>
                                                          {p.metadata.path}</h2>
                                                        <div className={styles.btn_chabot_metadata_copy}>
                                                          <Button
                                                              variant="inline-icon"
                                                              iconName="external"
                                                              ariaLabel="Open the source document."
                                                              onClick={() => {
                                                                openDocument(
                                                                    p.metadata.workspace_id,
                                                                    p.metadata.path,
                                                                    p.page_content
                                                                );
                                                              }}
                                                          />
                                                          <Popover
                                                              size="medium"
                                                              position="top"
                                                              triggerType="custom"
                                                              dismissButton={false}
                                                              content={
                                                                <StatusIndicator type="success">
                                                                  Downloaded as Word document
                                                                </StatusIndicator>
                                                              }
                                                          >
                                                            <div style={{display: 'flex', justifyContent:'space-between', alignItems:'center',paddingBottom:'20px'}}>
                                                            <Button
                                                                variant="icon"
                                                                iconName="download"
                                                                ariaLabel="Download as Word document"
                                                                iconUrl={downloadIcon}
                                                                onClick={() => {
                                                                  navigator.clipboard.writeText(extractText(p.page_content));
                                                                  markdownToDocxAndDownload(
                                                                      props.message.content,
                                                                      `DUA_Builder_${new Date()
                                                                          .toISOString()
                                                                          .slice(0, 10)}.docx`,
                                                                  )
                                                                }}
                                                            />
                                                            </div>
                                                          </Popover>
                                                        </div>
                                                      </div>
                                                      <div style={{ marginBottom: "4px",color: '#222c67',fontWeight:'700',fontSize:'14px' }}>
                                                        <span>Page</span>
                                                        <span style={{ marginLeft: "4px" }}>
                                      {p?.metadata?.page_number || ""}
                                    </span>
                                                      </div>
                                                      <p>
                                                        {extractText(p.page_content)}
                                                      </p>
                                                    </div>
                                                  </div>
                                              ),
                                            };
                                          })}
                                          activeTabId={documentIndex}
                                          onChange={({ detail }) =>
                                              setDocumentIndex(detail.activeTabId)
                                          }
                                      />
                                    </>
                                )}
                          </ExpandableSection>
                      }
                      <ExpandableSection variant="footer" headerText="Show Feedback" onChange={(prevState) => setShowSourceFeedback((prevState) => ({...prevState, showFeedback: !prevState.showFeedback}))}>
                        <div className={styles.feedbackDropdownContainer}>
                          {props.message.content !== "NA" && (
                              <div>
                                {selectedIcon === 1 && (
                                    <FaThumbsUp
                                        className={`${styles.thumbsIcon} ${styles.thumbsUp} ${
                                            selectedIcon === 1 ? styles.selected : ""
                                        }`}
                                    />
                                )}
                                {selectedIcon === 0 && (
                                    <FaThumbsDown
                                        className={`${styles.thumbsIcon} ${
                                            styles.thumbsDown
                                        } ${selectedIcon === 0 ? styles.selected : ""}`}
                                    />
                                )}
                              </div>
                          )}
                          <div>
                            {selectedIcon === 0 && (
                                <div>
                                  <p>
                                    <strong>
                                      *What about the response could have been improved?
                                    </strong>
                                  </p>

                                  {selectedFeedbackReasons.map((item: string) => (
                                      <div key={item}>{item}.</div>
                                  ))}
                                </div>
                            )}
                            {feedbackComment && (
                                <p>
                                  <strong>Additional Feedback</strong> <br />
                                  {feedbackComment}
                                </p>
                            )}
                          </div>
                        </div>
                      </ExpandableSection>
                      {isMetadataShown() && (
                          <ExpandableSection variant="footer" headerText="Show Metadata">
                            <JsonView
                                shouldInitiallyExpand={(level) => level < 2}
                                data={JSON.parse(
                                    JSON.stringify(props.message.metadata).replace(
                                        /\\n/g,
                                        "\\\\n"
                                    )
                                )}
                                style={{
                                  ...darkStyles,
                                  stringValue: "jsonStrings",
                                  numberValue: "jsonNumbers",
                                  booleanValue: "jsonBool",
                                  nullValue: "jsonNull",
                                  container: "jsonContainer",
                                }}
                            />
                            {props.message.metadata.prompts && (
                                <>
                                  <div className={styles.btn_chabot_metadata_copy}>
                                    <Popover
                                        size="medium"
                                        position="top"
                                        triggerType="custom"
                                        dismissButton={false}
                                        content={
                                          <StatusIndicator type="success">
                                            Downloaded as Word document
                                          </StatusIndicator>
                                        }
                                    >
                                      <div style={{display: 'flex', justifyContent:'space-between', alignItems:'center',paddingBottom:'20px'}}>
                                      <Button
                                          variant="icon"
                                          iconName="download"
                                          ariaLabel="Download as Word document"
                                          iconUrl={downloadIcon}
                                          onClick={() => {
                                            // Copy to clipboard first
                                            navigator.clipboard.writeText(
                                                (props.message.metadata.prompts as string[][])[
                                                    parseInt(promptIndex)
                                                    ][0]
                                            );
                                            // generateWordDocument((props.message.metadata.prompts as string[][])[
                                            //     parseInt(promptIndex)
                                            //     ][0]);
                                            markdownToDocxAndDownload(
                                                (
                                                    props.message.metadata
                                                        .prompts as string[][]
                                                )[parseInt(promptIndex)][0],
                                                `CDERGPT_Prompt_${new Date()
                                                    .toISOString()
                                                    .slice(0, 10)}.docx`,
                                            )
                                          }}
                                      />
                                      </div>
                                    </Popover>
                                  </div>
                                  <Tabs
                                      tabs={(
                                          props.message.metadata.prompts as string[][]
                                      ).map((p, i) => {
                                        return {
                                          id: `${i}`,
                                          label: `Prompt ${
                                              (props.message.metadata.prompts as string[][])
                                                  .length > 1
                                                  ? i + 1
                                                  : ""
                                          }`,
                                          content: (
                                              <>
                                                <Textarea
                                                    value={p[0]}
                                                    readOnly={true}
                                                    rows={8}
                                                />
                                              </>
                                          ),
                                        };
                                      })}
                                      activeTabId={promptIndex}
                                      onChange={({ detail }) =>
                                          setPromptIndex(detail.activeTabId)
                                      }
                                  />
                                </>
                            )}
                          </ExpandableSection>
                      )}
                    </div>
                  </div>
                </Container>
              </div>
            </div>
        )}
        {loading && (
            <Box float="left">
              <Spinner />
            </Box>
        )}
        {files && !loading && (
            <>
              {files.map((file, idx) => (
                  <a
                      key={idx}
                      href={file.url as string}
                      target="_blank"
                      rel="noreferrer"
                  >
                    <img
                        src={file.url as string}
                        className={styles.img_chabot_message}
                    />
                  </a>
              ))}
            </>
        )}
        {/*{props.message?.type === ChatBotMessageType.Human && (*/}
        {/*    <div style={{display: 'flex'}}>*/}
        {/*      /!*<img src={userInactive} alt="" height='45px' style={{marginRight: '8px'}}/>*!/*/}
        {/*      <TextContent>*/}
        {/*        <strong style={{  whiteSpace: "pre-wrap", wordWrap: "break-word"}}>*/}
        {/*          {props.message.content}*/}
        {/*        </strong>*/}
        {/*      </TextContent>*/}
        {/*    </div>*/}
        {/*)}*/}
      </div>
  );
}