import {useContext, useEffect, useState} from "react";
import {ChatBotConfiguration, ChatBotHistoryItem, ChatBotMessageType, FeedbackData,} from "./types";
import {<PERSON><PERSON>, SpaceBetween, StatusIndicator,} from "@cloudscape-design/components";
import {v4 as uuidv4} from "uuid";
import {AppContext} from "../../common/app-context";
import ChatMessage from "./chat-message";
import ChatInputPanel, {ChatScrollState} from "./chat-input-panel";
import styles from "../../styles/chat.module.scss";
import sessionService from "../../services/session.service";
import feedbackService from "../../services/feedback.service";
// import { useOktaAuth } from "@okta/okta-react";
import {UserInfo} from "../../common/types";
import {useDispatch, useSelector} from "react-redux";
import {chatActions} from "../../pages/chatbot/playground/chat.slice";
import {RootState} from "@/redux/store/configureStore";
import QuestionMark from "../../assets/images/question-mark.png";
import {useLocation} from 'react-router-dom';
import Preview from "./preview";

// 定义Help Modal的Props接口
interface HelpModalProps {
    visible: boolean;
    onDismiss: () => void;
}

// 将Help Modal组件提取到外部并使用memo优化
// const HelpModal = memo(({visible, onDismiss}: HelpModalProps) => {
//     return (
//         <Modal
//             visible={visible}
//             onDismiss={onDismiss}
//             header="How to use Data Governance Document Builder"
//             size="medium"
//         >
//             <SpaceBetween direction="vertical" size="m">
//                 <div>
//                     <h4>To generate a customized DUA based on your Center's needs:</h4>
//                     <ol>
//                         <li>Select <span style={{color: '#007BFF'}}><strong>DUA</strong></span> under <span
//                             style={{color: '#007BFF'}}><strong>Document Builder</strong></span></li>
//                         <li>Select the parties involved under <span style={{color: '#007BFF'}}><strong>Party 1</strong></span> and <span
//                             style={{color: '#007BFF'}}><strong>Party 2</strong></span></li>
//                         <li>Upload existing DUAs with domain-specific language (optional)</li>
//                         <li>Click on <span style={{color: '#007BFF'}}><strong>Send</strong></span></li>
//                         <li>Edit the DUA generated by combining, adding, or removing sections as needed</li>
//                     </ol>
//                     <p>
//                         <strong>For advanced users</strong>, select <span style={{color: '#007BFF'}}><strong>Generate a customized DUA</strong></span> under
//                         the chat box to generate a prompt that you can modify further through prompt engineering.
//                     </p>
//                 </div>
//
//                 <Box float="right">
//                     <Button variant="primary" onClick={onDismiss}>Got it</Button>
//                 </Box>
//             </SpaceBetween>
//         </Modal>
//     );
// });

export default function Chat(props: { sessionId?: string }) {
    const appContext = useContext(AppContext);
    const dispatch = useDispatch();

    const dataState = useSelector((state: RootState) => {
        return state.rootReducer;
    });

    const [running, setRunning] = useState<boolean>(false);
    const [session, setSession] = useState<{ id: string; loading: boolean }>({
        id: props.sessionId ?? uuidv4(),
        loading: typeof props.sessionId !== "undefined",
    });
    const [commentVisible, setCommentVisible] = useState(false);
    const [comment, setComment] = useState("");
    const [feedbackMessage, setFeedbackMessage] = useState<ChatBotHistoryItem>();
    const [feedbackType, setFeedbackType] = useState<number>(0);
    const [feedbackIdx, setFeedbackIdx] = useState<number>(0);
    const [error, setError] = useState(false);
    const [errorMessage, setErrorMessage] = useState("");
    const commentsFromRedux = dataState?.chatReducer?.comment;
    const chatIdFromRedux = dataState?.chatReducer?.chatId;
    const isFeedbackSubmitted = dataState?.chatReducer?.isFeedbackSubmitted;
    const messageContent = dataState?.chatReducer?.messageContent;
    const [feedbackComment, setFeedbackComment] = useState("");
    const [hasUploadedFiles, sethasUploadedFiles] = useState(false);
    const userEmail = dataState?.chatReducer?.userEmail;
    const userName = dataState?.chatReducer?.userName;
    const [isHelpModalVisible, setIsHelpModalVisible] = useState(false);
    // 添加状态跟踪Modal是否已初始化
    const [isModalInitialized, setIsModalInitialized] = useState(false);

    const [configuration, setConfiguration] = useState<ChatBotConfiguration>(
        () => ({
            streaming: true,
            useHistory: true,
            followPromptId: "",
            showMetadata: false,
            showSource: true,
            maxTokens: 2000,
            temperature: 0.1,
            topP: 0.1,
            numDocs: 10,
            files: null,
        })
    );

    // const [promptConfiguration, setPromptConfiguration] =
    //     useState<ChatBotPromptConfiguration>(() => ({
    //         name: '',
    //         description: '',
    //         prompt_type: '',
    //         tags: '',
    //     }));
    const location = useLocation();
    const [messages, setMessages] = useState([]);
    const [messageHistory, setMessageHistory] = useState<ChatBotHistoryItem[]>(
        []
    );
    // const { authState, oktaAuth } = useOktaAuth();
    const [userInfo, setUserInfo] = useState<UserInfo>();

    const chatDocuments = dataState?.chatReducer?.chatDocuments;

    // 确保Modal正确初始化
    useEffect(() => {
        // 确保组件完全挂载后再初始化Modal
        setIsModalInitialized(true);
    }, []);

    // useEffect(() => {
    //   (async () => {
    //     if (!authState?.isAuthenticated) {
    //       //Remove when auth/login is actually working
    //       // setUserInfo(null)
    //       const loggedUser = {
    //         email: "<EMAIL>",
    //         name: "test user",
    //         first_name: "test",
    //         last_name: "user",
    //       };
    //       setUserInfo(loggedUser);
    //     } else {
    //       await oktaAuth.getUser().then((user: any) => {
    //         const loggedUser = {
    //           email: user.email,
    //           name: user.name,
    //           first_name: user.given_name,
    //           last_name: user.family_name,
    //         };
    //         setUserInfo(loggedUser);
    //       });
    //     }
    //   })();
    // }, []);

    useEffect(() => {
        // if (!appContext) return;
        if (location.state?.resetChat) {
            setComment('')
        }

        setMessageHistory([]);
        // dummy responses
        // setMessageHistory([{
        //         type: ChatBotMessageType.AI,
        //         content: 'Generate a comprehensive template for data sharing between [PARTY A] and [PARTY B].' +
        //             '\nThis template must strictly follow the structure and contents outlined below.' +
        //             '\nDo not omit or summarize any section from 1.0 to 10.0.\nEnsure every subsection is generated with clear section numbering, formal legal tone, and all placeholders in [BRACKETS] for easy customization.' +
        //             '\nEnsure the final section includes a full table capturing dataset change tracking as specified.\nTemplate Structure\nTitle Page\nInclude a title that clearly states this is a ' +
        //             '\nIdentify the organizations entering into the agreement\nName the specific data or systems involved\nInclude version number and date\nAdd logo placeholder for the originating organization\nVersion History Table' +
        //             '\nInclude a version history table with the following columns:\nVersion Number\nWritten By\nRevision Date\nApproved By\nApproval Date\nDescription of Change\nTable of Contents\nGenerate a comprehensive Table of Contents covering all sections below from 1.0 to 10.0, including appendices and addendum.\n1.0 General Terms\n1.1 Parties to Agreement\nLanguage identifying the organizations and individuals entering the agreement, including full legal names and a table for contact information.\n1.2 Period of Agreement\nDefine effective dates, renewal terms, and notification procedures.\n1.3 Scope of Agreement\nDescribe the high-level purpose, involved systems, and types of data. Use [BRACKETS] for placeholders.\n1.4 Authority to Share Data\nCite relevant legal or regulatory basis, with placeholders for jurisdiction-specific laws.\n1.5 Modification\nDescribe how changes must be proposed, documented, and agreed upon.\n1.6 Termination\nOutline termination processes for both cause and no cause, with data return/destruction procedures.\n1.7 Violation of Terms\nSpecify reporting timelines, processes, and potential consequences.\n1.8 Indemnification\nAssign responsibility for claims, subject to federal/state law.\n1.9 Acknowledgements\nInclude affirmation of authority, understanding, and compliance.' +
        //             '\n1.10 Roles and Responsibilities\nDetail key roles and contacts (e.g., project officer, data custodian), with placeholders in a table format.\n1.11 Funding\nOutline any financial terms or payments, with [BRACKETS] for dollar amounts.\n1.12 Other Common Terms\nInclude clauses on: assignment,' +
        //             ' dispute resolution, captions, choice of law, costs and damages, counterparts, entire agreement, flow-down, order of precedence, independent entities, severability, survival, prohibition on third party beneficiaries, public availability.\n2.0 Purpose/Use Case\nClearly define the context, objectives, analytic methods, and expected outcomes.' +
        //             '\n3.0 Scope of Data\n3.1 Data Description\nHigh-level overview of data, with status of PII/PHI/BII, federal program source, and reference to detailed appendix of data specifications.\n3.2 Data Ownership\nState ownership rights and data use limitations.' +
        //             '\n3.3 Service Level\nInclude: frequency, transmission methods, platforms/tools/support, data standards.\n3.3.2 Data Quality' +
        //             '\nSpecify: quality assurances or limitations, bias mitigation, recommended analytic methods, handling of transmission errors.\n4.0 Data Controls\n4.1 Disclosure and Use\nCover: purpose limitations, consent, ' +
        //             'legal compliance, role-based access, user logs, notification for compelled disclosure, IRB requirements.\n4.2 Control of Identifiable Data\nProhibit identifiable data sharing except under well-defined exceptions.' +
        //             '\n4.3 Data Deidentification\n4.3.1 Person — standards and anti-reidentification practices.\n4.3.2 Organization — similar requirements for org-level data.\n4.4 Data Sharing\n4.4.1 Data Linkage — disallow unauthorized linkage.\n4.4.2 Data Reuse — prohibit unauthorized reuse.\n4.4.3 Data Redisclosure — prohibit redisclosure; outline exceptions (e.g., FOIA); define redisclosure authorization and violation handling.\n4.4.3.1 Identifiable Data as De-identified Data — prevent publication or sharing that allows re-identification.\n4.5 Data Disposition\nSpecify retention time, destruction method, and certification.\n4.6 Publication\nDefine data provider approval process and attribution requirements.\n5.0 Security\n5.1 General — Include broad safeguard requirements.\n5.2 Data Access Controls — Limit access to authorized users; include remote user policies.\n5.3 Network Access — Require secure networking practices.\n5.4 Physical Access — Specify facility and storage controls.\n5.5 Transmission and Storage — Require encryption and other security safeguards.\n5.6 Authority to Operate — Align with federal control standards.\n5.7 Cloud Computing — Require FedRAMP authorization and approval.\n5.8 Incidents Including Reporting — define breaches, set reporting procedures and timelines, require investigation records and remediation.\n5.9 Audit — Enable inspection rights to verify compliance.' +
        //             '\n6.0 Reporting Requirements\nDefine types, frequency, and content of required reports.\n7.0 Applicable Law and Regulations\nList relevant laws and describe their impact on data handling.\n8.0 Signatures\nInclude signature blocks with: Name, Title, Organization, Date.\n9.0 Appendices\nInclude placeholders for:\nDetailed data documentation\nTechnical specifications\nFedRAMP or other certifications\n10.0 Addendum Template\nCreate a structured table format to track dataset change requests.\nIMPORTANT: The following must be included at the end of the document as a table and should be clearly labeled and properly formatted:\nAppendix: Data Change Tracking Table\n| Date of Data Change Request (MM/DD/YYYY) | Target Date of Completion (MM/DD/YYYY) | Data Change Requestor(s) | Brief Description of Data Change Request | Reason for Data Change Request | Special Conditions/Stipulations, if any | [PARTY A] Signature | [PARTY B] Signature |\nFormatting Guidelines\nUse clear section numbering (e.g., 1.1, 1.2, etc.)\nUse [BRACKETS] for all placeholders\nUse table format for contact info and signature blocks\nMaintain professional formatting (spacing, headers/footers, page breaks)\nApply precise and legally appropriate language\nEnsure compliance with federal data security standards\nDocument should be suitable for intra-agency or inter-agency use\nDo not omit or summarize any subsection\nplease response with markdown format without starting with',
        //         metadata: 'test1',
        //         chatId: '123',
        // }]);

        (async () => {
            if (!props.sessionId) {
                setSession({id: uuidv4(), loading: false});
                return;
            }

            setSession({id: props.sessionId, loading: true});
            try {
                const result = await sessionService.getSession(
                    props.sessionId,
                    userEmail as string
                );

                if (result && result.history) {
                    ChatScrollState.skipNextHistoryUpdate = true;
                    ChatScrollState.skipNextScrollEvent = true;
                    setMessageHistory(
                        result.history
                            .filter((x: any) => x !== null)
                            .map((x: any) => ({
                                type: x?.type as ChatBotMessageType,
                                metadata: JSON.parse(x?.metadata),
                                content: x?.content,
                            }))
                    );

                    window.scrollTo({
                        top: 0,
                        behavior: "instant",
                    });
                }
            } catch (error) {
                console.log(error);
            }

            setSession({id: props.sessionId, loading: false});
            setRunning(false);
        })();
    }, [appContext, props.sessionId, location.state]);

    const saveFeedback = (
        feedbackType: number,
        idx: number,
        message: ChatBotHistoryItem,
        comment?: string,
        feedbackReasons?: string[],
    ) => {
        if (message.metadata.sessionId) {
            const prompt = messageHistory[idx - 1]?.content;
            const completion = message.content;
            const model = message.metadata.modelId;
            const feedbackData: FeedbackData = {
                sessionId: message.metadata.sessionId as string,
                feedback: feedbackType,
                prompt: prompt,
                comment: commentsFromRedux,
                userId: userEmail as string,
                chatId: message?.metadata?.chatId as string,
                completion: completion,
                model: model as string,
                improvementAreas: feedbackReasons,
                chatDocuments: messageHistory
                    .filter(
                        (historyItem) =>
                            historyItem.metadata.chatId === message.metadata.chatId
                    )
                    .map((historyItem) => historyItem.metadata.documents)
                    .flat()
                    .filter((doc) => typeof doc === "object") as Object[],
                promptId: messageHistory
                    .filter(promptIdHistoryItem => promptIdHistoryItem.metadata.chatId === message.metadata.chatId)
                    .map(promptIdHistoryItem => promptIdHistoryItem.metadata.promptId)
                    .toString(),
                useHistory: (messageHistory
                    .find(useHistoryItem => useHistoryItem.metadata.chatId === message.metadata.chatId)
                    ?.metadata.modelKwargs as { useHistory?: boolean })?.useHistory ?? false,
            };
            addUserFeedback(feedbackData);
        }
    };

    const handleThumbsClick = (
        feedbackType: number,
        idx: number,
        message: ChatBotHistoryItem,
        comment?: string,
        feedbackReasons?: string[]
    ) => {
        // setCommentVisible(true);
        // setComment('');
        // setFeedbackMessage(message);
        // setFeedbackType(feedbackType);
        // setFeedbackIdx(idx);
        saveFeedback(feedbackType, idx, message, comment, feedbackReasons);
        dispatch(chatActions?.setFeedbackReasons(feedbackReasons));
    };

    const addUserFeedback = async (feedbackData: FeedbackData) => {
        // if (!appContext) return;
        await feedbackService.saveFeedback(feedbackData);
    };

    const handleCloseNotification = () => {
        setError(false);
    };

    const handleClose = (
        event?: React.SyntheticEvent | Event,
        reason?: string
    ) => {
        if (reason === "clickaway") {
            return;
        }
        setError(false);
    };

    const handleFileUploadStatus = (isUploaded: boolean) => {
        sethasUploadedFiles(isUploaded)
    }

    // 使用记忆化函数来处理Modal打开，避免多次触发
    const handleOpenHelpModal = () => {
        if (isModalInitialized) {
            setIsHelpModalVisible(true);
        }
    };

    return (
        <div className={styles.chat_container}>
            <SpaceBetween direction="vertical" size="m">
                <div className={styles.chat_header_container}>
                    <div className={styles.chat_header}>
                        <h2 style={{
                            margin: 'inherit',
                            fontSize: '22px',
                            fontWeight: '700',
                            fontFamily: 'Roboto Condensed',
                        }}>Generate a Data Governance Document </h2>
                    </div>
                    <hr/>
                    {/* WELCOME MESSAGE CENTER Welcome to Data Governance Document Builder! */}
                    <div style={{display: 'flex', alignItems: 'center', justifyContent: 'center'}}>
                        {/*<img src={messageHistory.length > 0 ? cdergptInactive : cdergptActive} alt="" height='45px' style={{ marginRight: '8px' }} />*/}
                        <p className={styles.chat_welcome_message}>Welcome to Data Governance Document
                            Builder! {userName?.firstName}</p>
                        <div
                            style={{
                                marginLeft: '8px',
                                cursor: 'pointer',
                                display: 'flex',
                                alignItems: 'center'
                            }}
                            onClick={handleOpenHelpModal}
                            title="How to use Data Governance Document Builder"
                        >
                            {/*<Icon name="status-info" size="normal" variant="normal"/>*/}
                        </div>
                    </div>
                </div>
                
                {/* 嵌入Preview组件 */}
                <Preview />
                
                {/*for some reason css didn't work using this way, dam*/}
                <style>
                    {`
                        .blue-arrow-details summary::marker {
                          color: #007BFF;
                        }
                        
                        .blue-arrow-details summary::-webkit-details-marker {
                          color: #007BFF;
                        }
                    `}
                </style>
                {/*<details className="guideline-details blue-arrow-details" open>*/}
                {/*    <summary>*/}
                {/*        <img src={QuestionMark} alt="icon" height="13px" style={{marginRight: '10px'}}/>*/}
                {/*        How to use Data Governance Document Builder?*/}
                {/*    </summary>*/}
                {/*    <div className="guideline-content">*/}
                {/*        <h4>To generate a customized DUA based on your Center's needs:</h4>*/}
                {/*        <ol>*/}
                {/*            <li>Select <span style={{color: '#007BFF'}}><strong>DUA</strong></span> under <span*/}
                {/*                style={{color: '#007BFF'}}><strong>Document Builder</strong></span></li>*/}
                {/*            <li>Select the parties involved under <span*/}
                {/*                style={{color: '#007BFF'}}><strong>Party 1</strong></span> and <span*/}
                {/*                style={{color: '#007BFF'}}><strong>Party 2</strong></span></li>*/}
                {/*            <li>Upload existing DUAs with domain-specific language (optional)</li>*/}
                {/*            <li>Click on <span style={{color: '#007BFF'}}><strong>Send</strong></span></li>*/}
                {/*            <li>Edit the DUA generated by combining, adding, or removing sections as needed</li>*/}
                {/*        </ol>*/}
                {/*        <strong>For advanced users</strong>, select <span style={{color: '#007BFF'}}><strong>Generate a customized DUA</strong></span> under*/}
                {/*        the chat box to generate a prompt that you can modify further through prompt engineering.*/}
                {/*        <p>*/}
                {/*            To learn more about FDA internal data sharing procedures, access the{' '}*/}
                {/*            <a href="https://fda.sharepoint.com/:w:/r/sites/ODT-ODAR-DAG-MDM-Data_Curation/Shared Documents/6-Chatbot/1_Research %26 Business Knowledge/2. MOUs/ODT/FDA_wide Internal Data Sharing_Final_SMG.docx?d=waf45b462d41540259898c4c734b4448f&csf=1&web=1&e=Wtl1Bf"*/}
                {/*               style={{color: '#007BFF', textDecoration: 'underline'}}*/}
                {/*               target="_blank"*/}
                {/*               rel="noopener noreferrer"*/}
                {/*            >*/}
                {/*                FDA-wide Internal Data Sharing Staff Manual Guide*/}
                {/*            </a>*/}
                {/*        </p>*/}
                {/*    </div>*/}
                {/*</details>*/}
                {messageHistory.map((message: ChatBotHistoryItem, idx) => (
                    <ChatMessage
                        key={idx}
                        chatId={message.chatId}
                        message={message}
                        lastAiResponse={idx === messageHistory.length - 1}
                        showMetadata={configuration.showMetadata}
                        showSource={configuration.showSource}
                        onThumbsUp={() => handleThumbsClick(1, idx, message)}
                        onThumbsDown={(comment, selectedCheckboxes) =>
                            handleThumbsClick(0, idx, message, comment, selectedCheckboxes)
                        }
                        hasUploadedFiles={hasUploadedFiles}
                    />
                ))}
            </SpaceBetween>
            {error && (
                <Alert
                    dismissible
                    statusIconAriaLabel="Error"
                    type="error"
                    header="Error"
                    onDismiss={handleCloseNotification}
                >
                    {errorMessage}
                </Alert>
            )}

            <div className={styles.welcome_text}>
                {/* {messageHistory.length == 0 && !session?.loading && (
          <center>
            {`Welcome ${userName?.firstName || ''}`} <br />
            {"FDA Document"} <br /> {"Question and Answer"}
          </center>
        )} */}
                {session?.loading && (
                    <center>
                        <StatusIndicator type="loading">Loading session</StatusIndicator>
                    </center>
                )}
            </div>
            <div className={styles.input_container}>
                {/*<img src={userActive} alt="" height='45px' style={{ margin: '8px' }} />*/}
                <div style={{width: '100%'}}>
                    {/*<ChatInputPanel*/}
                    {/*    chatId={uuidv4()} // Pass a new UUID v4 as chatId*/}
                    {/*    session={session}*/}
                    {/*    running={running}*/}
                    {/*    setRunning={setRunning}*/}
                    {/*    messageHistory={messageHistory}*/}
                    {/*    onFileUploadStatusChange={handleFileUploadStatus}*/}
                    {/*    setMessageHistory={(history: ChatBotHistoryItem[]) => {*/}
                    {/*        setMessageHistory(history);*/}
                    {/*    }}*/}
                    {/*    configuration={configuration}*/}
                    {/*    setConfiguration={setConfiguration}*/}
                    {/*    closeNotification={handleCloseNotification}*/}
                    {/*    setError={setError}*/}
                    {/*    setErrorMessage={setErrorMessage}*/}
                    {/*/>*/}
                </div>
            </div>

            {/* 确保Modal仅在组件初始化后渲染 */}
            {/*{isModalInitialized && (*/}
            {/*    <HelpModal*/}
            {/*        visible={isHelpModalVisible}*/}
            {/*        onDismiss={() => setIsHelpModalVisible(false)}*/}
            {/*    />*/}
            {/*)}*/}
        </div>
    );
}
