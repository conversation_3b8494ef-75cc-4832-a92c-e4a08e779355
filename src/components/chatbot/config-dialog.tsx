import {
    Box,
    Button,
    ColumnLayout,
    Form,
    FormField,
    Input,
    Modal,
    Select,
    SelectProps,
    SpaceBetween,
    Toggle,
} from '@cloudscape-design/components';
import { useForm } from '../../common/hooks/use-form';
import { ChatBotConfiguration, ConfigDialogState } from './types';
import { Dispatch, useContext, useEffect, useState } from 'react';
import { OptionsHelper } from '../../common/helpers/options-helper';
import { StorageHelper } from '../../common/helpers/storage-helper';
import promptService from '../../services/prompt.service';
import { AppContext } from '../../common/app-context';
import { PromptsHelper } from '../../common/helpers/prompts-helper';
import { ModelPromptFilter } from './model-prompt-filter';

export interface ConfigDialogProps {
    sessionId: string;
    visible: boolean;
    setVisible: (visible: boolean) => void;
    configuration: ChatBotConfiguration;
    setConfiguration: Dispatch<React.SetStateAction<ChatBotConfiguration>>;
    selectedModel?: SelectProps.Option | null;
}

interface ChatConfigDialogData {
    streaming: boolean;
    useHistory: boolean;
    followPromptId: string;
    showSource: boolean;
    showMetadata: boolean;
    maxTokens: number;
    temperature: number;
    topP: number;
    numDocs: number;
}

const promptDefaultOptions: SelectProps.Option[] = [
    {
        label: 'No Prompt',
        value: '',
        iconName: 'close',
    },
];

export default function ConfigDialog(props: ConfigDialogProps) {
    const appContext = useContext(AppContext);

    const [state, setState] = useState<ConfigDialogState>({
        value: '',
        selectedPrompt: promptDefaultOptions[0],
        promptsStatus: 'loading',
    });

    const { data, onChange, errors, validate } = useForm<ChatConfigDialogData>({
        initialValue: () => {
            const retValue = {
                streaming: props.configuration.streaming,
                useHistory: props.configuration.useHistory,
                followPromptId: props.configuration.followPromptId,
                showSource: props.configuration.showSource,
                showMetadata: props.configuration.showMetadata,
                maxTokens: props.configuration.maxTokens,
                temperature: props.configuration.temperature,
                numDocs: props.configuration.numDocs,
                topP: props.configuration.topP,
            };

            return retValue;
        },
        validate: (form) => {
            const errors: Record<string, string | string[]> = {};

            if (form.temperature < 0 || form.temperature > 1.0) {
                errors.temperature = 'Temperature must be between 0 and 1.0';
            }

            return errors;
        },
    });

    const promptsOptions = [
        ...promptDefaultOptions,
        ...OptionsHelper.getSelectPromptOptions(state.prompts || []),
    ];

    const saveConfig = () => {
        if (!validate()) return;

        props.setConfiguration({
            ...props.configuration,
            ...data,
        });

        props.setVisible(false);
    };

    const cancelChanges = () => {
        onChange({
            ...props.configuration,
            streaming: props.configuration.streaming,
            useHistory: props.configuration.useHistory,
            followPromptId: props.configuration.followPromptId,
            showSource: props.configuration.showSource,
            showMetadata: props.configuration.showMetadata,
            temperature: props.configuration.temperature,
            maxTokens: props.configuration.maxTokens,
            topP: props.configuration.topP,
        });

        props.setVisible(false);
    };

    useEffect(() => {
        if (props.selectedModel) {
            (async () => {
                try {
                    //get all prompts
                    const allPrompts = await promptService.getPrompts();

                    //get all prompts by model
                    let filteredPrompts = ModelPromptFilter.filterPrompts(
                        allPrompts,
                        props.selectedModel
                    );

                    //set default prompt to "followup" prompt
                    let defaultPrompt = filteredPrompts.filter(p => p.tags.includes("followupdua1"));
                    const selectedPromptOption = defaultPrompt.length > 0
                        ? OptionsHelper.getSelectPromptOptions(defaultPrompt)[0]
                        : promptDefaultOptions[0];

                    setState(state => ({
                        ...state,
                        prompts: filteredPrompts,
                        selectedPrompt: selectedPromptOption,
                        promptsStatus: 'finished'
                    }));

                    // Update the configuration with the new prompt
                    if (selectedPromptOption?.value) {
                        StorageHelper.setSelectedFollowupPromptId(selectedPromptOption.value);
                        props.setConfiguration(prev => ({
                            ...prev,
                            followPromptId: selectedPromptOption.value
                        }));
                    }
                } catch (error) {
                    console.error('Error updating prompts:', error);
                    setState(state => ({
                        ...state,
                        promptsStatus: 'error'
                    }));
                }
            })();
        }
    }, [props.selectedModel]);

    useEffect(() => {
        // if (!appContext) return;
        let prompts: any[] = [];
        (async () => {
            try {
                prompts = await promptService.getPrompts();
                const selectedPromptOption =
                    PromptsHelper.getSelectedPromptOption(prompts);
                setState((state) => ({
                    ...state,
                    prompts,
                    selectedPrompt: selectedPromptOption,
                    promptsStatus: 'finished',
                }));
                setState((state) => ({
                    ...state,
                    prompts: prompts,
                }));
            } catch (error) {}
        })();
    }, [appContext]);

    return (
        <Modal
            onDismiss={() => props.setVisible(false)}
            visible={props.visible}
            footer={
                <Box float="right">
                    <SpaceBetween
                        direction="horizontal"
                        size="xs"
                        alignItems="center"
                    >
                        <Button variant="link" onClick={cancelChanges}>
                            Cancel
                        </Button>
                        <Button variant="primary" onClick={saveConfig}>
                            Save changes
                        </Button>
                    </SpaceBetween>
                </Box>
            }
            header="Configuration"
        >
            <Form>
                <SpaceBetween size="m">
                    {/* <FormField label="Session Id">{props.sessionId}</FormField> */}
                    <ColumnLayout columns={2}>
                        <FormField
                            label="Prompts"
                            errorText={errors.useHistory}
                        >
                            <Toggle
                                checked={data.useHistory}
                                onChange={({ detail: { checked } }) =>
                                    onChange({ useHistory: checked })
                                }
                            >
                                Use Conversation History
                            </Toggle>
                        </FormField>
                        <FormField
                            label="Select Follow-up Prompt"
                            errorText={errors.streaming}
                        >
                            <Select
                                statusType={state.promptsStatus}
                                loadingText="Loading prompts (might take few seconds)..."
                                placeholder="Select a prompt"
                                empty={<div>No prompts available.</div>}
                                filteringType="auto"
                                selectedOption={state.selectedPrompt}
                                onChange={({ detail }) => {
                                    setState((state) => ({
                                        ...state,
                                        selectedPrompt: detail.selectedOption,
                                    }));
                                    if (detail.selectedOption?.value) {
                                        StorageHelper.setSelectedFollowupPromptId(
                                            detail.selectedOption.value,
                                        );
                                    }
                                }}
                                options={promptsOptions}
                            />
                            {/* <Toggle
                checked={data.streaming}
                onChange={({ detail: { checked } }) =>
                  onChange({ streaming: checked })
                }
              >
                Enabled
              </Toggle> */}
                        </FormField>
                    </ColumnLayout>
                    <ColumnLayout columns={2}>
                        <FormField label="Source" errorText={errors.showSource}>
                            <Toggle
                                checked={data.showSource}
                                onChange={({ detail: { checked } }) =>
                                    onChange({ showSource: checked })
                                }
                            >
                                Show Source
                            </Toggle>
                        </FormField>
                        <FormField
                            label="Metadata"
                            errorText={errors.showMetadata}
                        >
                            <Toggle
                                checked={data.showMetadata}
                                onChange={({ detail: { checked } }) =>
                                    onChange({ showMetadata: checked })
                                }
                            >
                                Show Metadata
                            </Toggle>
                        </FormField>
                    </ColumnLayout>
                    <FormField
                        label="Number of Documents"
                        errorText={errors.numDocs}
                        description="This is the maximum number of documents for similarity search."
                    >
                        <Input
                            type="number"
                            step={1}
                            value={data.numDocs.toString()}
                            onChange={({ detail: { value } }) => {
                                onChange({ numDocs: parseInt(value) });
                            }}
                        />
                    </FormField>
                    <FormField
                        label="Max Tokens"
                        errorText={errors.maxTokens}
                        description="This is the maximum number of tokens that the LLM generates. The higher the number, the longer the response. This is strictly related to the target model."
                    >
                        <Input
                            type="number"
                            step={1}
                            value={data.maxTokens.toString()}
                            onChange={({ detail: { value } }) => {
                                onChange({ maxTokens: parseInt(value) });
                            }}
                        />
                    </FormField>
                    <FormField
                        label="Temperature"
                        errorText={errors.temperature}
                        description="A higher temperature setting usually results in a more varied and inventive output, but it may also raise the chances of deviating from the topic."
                    >
                        <Input
                            type="number"
                            step={0.05}
                            value={data.temperature.toFixed(2)}
                            onChange={({ detail: { value } }) => {
                                let floatVal = parseFloat(value);
                                floatVal = Math.min(
                                    1.0,
                                    Math.max(0.0, floatVal),
                                );

                                onChange({ temperature: floatVal });
                            }}
                        />
                    </FormField>
                    <FormField
                        label="Top-P"
                        errorText={errors.topP}
                        description="Top-P picks from the top tokens based on the sum of their probabilities. Also known as nucleus sampling, is another hyperparameter that controls the randomness of language model output. This method can produce more diverse and interesting output than traditional methods that randomly sample the entire vocabulary."
                    >
                        <Input
                            type="number"
                            step={0.1}
                            value={data.topP.toFixed(2)}
                            onChange={({ detail: { value } }) => {
                                let floatVal = parseFloat(value);
                                floatVal = Math.min(
                                    1.0,
                                    Math.max(0.0, floatVal),
                                );

                                onChange({ topP: floatVal });
                            }}
                        />
                    </FormField>
                </SpaceBetween>
            </Form>
        </Modal>
    );
}

// import {
//     Box,
//     Button,
//     ColumnLayout,
//     Form,
//     FormField,
//     Input,
//     Modal,
//     Select,
//     SelectProps,
//     SpaceBetween,
//     Toggle,
// } from '@cloudscape-design/components';
// import { useForm } from '../../common/hooks/use-form';
// import { ChatBotConfiguration, ConfigDialogState } from './types';
// import { Dispatch, useContext, useEffect, useState } from 'react';
// import { OptionsHelper } from '../../common/helpers/options-helper';
// import { StorageHelper } from '../../common/helpers/storage-helper';
// import promptService from '../../services/prompt.service';
// import { AppContext } from '../../common/app-context';
// import { PromptsHelper } from '../../common/helpers/prompts-helper';
// import { ModelPromptFilter } from './model-prompt-filter';
//
// export interface ConfigDialogProps {
//     sessionId: string;
//     visible: boolean;
//     setVisible: (visible: boolean) => void;
//     configuration: ChatBotConfiguration;
//     setConfiguration: Dispatch<React.SetStateAction<ChatBotConfiguration>>;
//     selectedModel?: SelectProps.Option | null;
// }
//
// interface ChatConfigDialogData {
//     streaming: boolean;
//     useHistory: boolean;
//     followPromptId: string;
//     showSource: boolean;
//     showMetadata: boolean;
//     maxTokens: number;
//     temperature: number;
//     topP: number;
//     numDocs: number;
// }
//
// const promptDefaultOptions: SelectProps.Option[] = [
//     {
//         label: 'No Prompt',
//         value: '',
//         iconName: 'close',
//     },
// ];
//
// export default function ConfigDialog(props: ConfigDialogProps) {
//     const appContext = useContext(AppContext);
//
//     const [state, setState] = useState<ConfigDialogState>({
//         value: '',
//         selectedPrompt: promptDefaultOptions[0],
//         promptsStatus: 'loading',
//     });
//
//     const { data, onChange, errors, validate } = useForm<ChatConfigDialogData>({
//         initialValue: () => {
//             const retValue = {
//                 streaming: props.configuration.streaming,
//                 useHistory: props.configuration.useHistory,
//                 followPromptId: props.configuration.followPromptId,
//                 showSource: props.configuration.showSource,
//                 showMetadata: props.configuration.showMetadata,
//                 maxTokens: props.configuration.maxTokens,
//                 temperature: props.configuration.temperature,
//                 numDocs: props.configuration.numDocs,
//                 topP: props.configuration.topP,
//             };
//
//             return retValue;
//         },
//         validate: (form) => {
//             const errors: Record<string, string | string[]> = {};
//
//             if (form.temperature < 0 || form.temperature > 1.0) {
//                 errors.temperature = 'Temperature must be between 0 and 1.0';
//             }
//
//             return errors;
//         },
//     });
//
//     const promptsOptions = [
//         ...promptDefaultOptions,
//         ...OptionsHelper.getSelectPromptOptions(state.prompts || []),
//     ];
//
//     const saveConfig = () => {
//         if (!validate()) return;
//
//         props.setConfiguration({
//             ...props.configuration,
//             ...data,
//         });
//
//         props.setVisible(false);
//     };
//
//     const cancelChanges = () => {
//         onChange({
//             ...props.configuration,
//             streaming: props.configuration.streaming,
//             useHistory: props.configuration.useHistory,
//             followPromptId: props.configuration.followPromptId,
//             showSource: props.configuration.showSource,
//             showMetadata: props.configuration.showMetadata,
//             temperature: props.configuration.temperature,
//             maxTokens: props.configuration.maxTokens,
//             topP: props.configuration.topP,
//         });
//
//         props.setVisible(false);
//     };
//
//     useEffect(() => {
//         if (props.selectedModel) {
//             (async () => {
//                 try {
//                     //get all prompts
//                     const allPrompts = await promptService.getPrompts();
//
//                     //get all prompts by model
//                     let filteredPrompts = ModelPromptFilter.filterPrompts(
//                         allPrompts,
//                         props.selectedModel
//                     );
//
//                     //set default prompt to "followup" prompt
//                     let defaultPrompt = filteredPrompts.filter(p => p.tags.includes("followupdua1"));
//                     const selectedPromptOption = defaultPrompt.length > 0
//                         ? OptionsHelper.getSelectPromptOptions(defaultPrompt)[0]
//                         : promptDefaultOptions[0];
//
//                     setState(state => ({
//                         ...state,
//                         prompts: filteredPrompts,
//                         selectedPrompt: selectedPromptOption,
//                         promptsStatus: 'finished'
//                     }));
//
//                     // Update the configuration with the new prompt
//                     if (selectedPromptOption?.value) {
//                         StorageHelper.setSelectedFollowupPromptId(selectedPromptOption.value);
//                         props.setConfiguration(prev => ({
//                             ...prev,
//                             followPromptId: selectedPromptOption.value
//                         }));
//                     }
//                 } catch (error) {
//                     console.error('Error updating prompts:', error);
//                     setState(state => ({
//                         ...state,
//                         promptsStatus: 'error'
//                     }));
//                 }
//             })();
//         }
//     }, [props.selectedModel]);
//
//     useEffect(() => {
//         // if (!appContext) return;
//         let prompts: any[] = [];
//         (async () => {
//             try {
//                 prompts = await promptService.getPrompts();
//                 const selectedPromptOption =
//                     PromptsHelper.getSelectedPromptOption(prompts);
//                 setState((state) => ({
//                     ...state,
//                     prompts,
//                     selectedPrompt: selectedPromptOption,
//                     promptsStatus: 'finished',
//                 }));
//                 setState((state) => ({
//                     ...state,
//                     prompts: prompts,
//                 }));
//             } catch (error) {}
//         })();
//     }, [appContext]);
//
//     return (
//         <Modal
//             onDismiss={() => props.setVisible(false)}
//             visible={props.visible}
//             footer={
//                 <Box float="right">
//                     <SpaceBetween
//                         direction="horizontal"
//                         size="xs"
//                         alignItems="center"
//                     >
//                         <Button variant="link" onClick={cancelChanges}>
//                             Cancel
//                         </Button>
//                         <Button variant="primary" onClick={saveConfig}>
//                             Save changes
//                         </Button>
//                     </SpaceBetween>
//                 </Box>
//             }
//             header="Configuration"
//         >
//             <Form>
//                 <SpaceBetween size="m">
//                     {/* <FormField label="Session Id">{props.sessionId}</FormField> */}
//                     <ColumnLayout columns={2}>
//                         <FormField
//                             label="Prompts"
//                             errorText={errors.useHistory}
//                         >
//                             <Toggle
//                                 checked={data.useHistory}
//                                 onChange={({ detail: { checked } }) =>
//                                     onChange({ useHistory: checked })
//                                 }
//                             >
//                                 Use Conversation History
//                             </Toggle>
//                         </FormField>
//                         <FormField
//                             label="Select Follow-up Prompt"
//                             errorText={errors.streaming}
//                         >
//                             <Select
//                                 statusType={state.promptsStatus}
//                                 loadingText="Loading prompts (might take few seconds)..."
//                                 placeholder="Select a prompt"
//                                 empty={<div>No prompts available.</div>}
//                                 filteringType="auto"
//                                 selectedOption={state.selectedPrompt}
//                                 onChange={({ detail }) => {
//                                     setState((state) => ({
//                                         ...state,
//                                         selectedPrompt: detail.selectedOption,
//                                     }));
//                                     if (detail.selectedOption?.value) {
//                                         StorageHelper.setSelectedFollowupPromptId(
//                                             detail.selectedOption.value,
//                                         );
//                                     }
//                                 }}
//                                 options={promptsOptions}
//                             />
//                             {/* <Toggle
//                 checked={data.streaming}
//                 onChange={({ detail: { checked } }) =>
//                   onChange({ streaming: checked })
//                 }
//               >
//                 Enabled
//               </Toggle> */}
//                         </FormField>
//                     </ColumnLayout>
//                     <ColumnLayout columns={2}>
//                         <FormField label="Source" errorText={errors.showSource}>
//                             <Toggle
//                                 checked={data.showSource}
//                                 onChange={({ detail: { checked } }) =>
//                                     onChange({ showSource: checked })
//                                 }
//                             >
//                                 Show Source
//                             </Toggle>
//                         </FormField>
//                         <FormField
//                             label="Metadata"
//                             errorText={errors.showMetadata}
//                         >
//                             <Toggle
//                                 checked={data.showMetadata}
//                                 onChange={({ detail: { checked } }) =>
//                                     onChange({ showMetadata: checked })
//                                 }
//                             >
//                                 Show Metadata
//                             </Toggle>
//                         </FormField>
//                     </ColumnLayout>
//                     <FormField
//                         label="Number of Documents"
//                         errorText={errors.numDocs}
//                         description="This is the maximum number of documents for similarity search."
//                     >
//                         <Input
//                             type="number"
//                             step={1}
//                             value={data.numDocs.toString()}
//                             onChange={({ detail: { value } }) => {
//                                 onChange({ numDocs: parseInt(value) });
//                             }}
//                         />
//                     </FormField>
//                     <FormField
//                         label="Max Tokens"
//                         errorText={errors.maxTokens}
//                         description="This is the maximum number of tokens that the LLM generates. The higher the number, the longer the response. This is strictly related to the target model."
//                     >
//                         <Input
//                             type="number"
//                             step={1}
//                             value={data.maxTokens.toString()}
//                             onChange={({ detail: { value } }) => {
//                                 onChange({ maxTokens: parseInt(value) });
//                             }}
//                         />
//                     </FormField>
//                     <FormField
//                         label="Temperature"
//                         errorText={errors.temperature}
//                         description="A higher temperature setting usually results in a more varied and inventive output, but it may also raise the chances of deviating from the topic."
//                     >
//                         <Input
//                             type="number"
//                             step={0.05}
//                             value={data.temperature.toFixed(2)}
//                             onChange={({ detail: { value } }) => {
//                                 let floatVal = parseFloat(value);
//                                 floatVal = Math.min(
//                                     1.0,
//                                     Math.max(0.0, floatVal),
//                                 );
//
//                                 onChange({ temperature: floatVal });
//                             }}
//                         />
//                     </FormField>
//                     <FormField
//                         label="Top-P"
//                         errorText={errors.topP}
//                         description="Top-P picks from the top tokens based on the sum of their probabilities. Also known as nucleus sampling, is another hyperparameter that controls the randomness of language model output. This method can produce more diverse and interesting output than traditional methods that randomly sample the entire vocabulary."
//                     >
//                         <Input
//                             type="number"
//                             step={0.1}
//                             value={data.topP.toFixed(2)}
//                             onChange={({ detail: { value } }) => {
//                                 let floatVal = parseFloat(value);
//                                 floatVal = Math.min(
//                                     1.0,
//                                     Math.max(0.0, floatVal),
//                                 );
//
//                                 onChange({ topP: floatVal });
//                             }}
//                         />
//                     </FormField>
//                 </SpaceBetween>
//             </Form>
//         </Modal>
//     );
// }
