import {
    Box,
    SpaceBetween,
    Table,
    <PERSON><PERSON>ation,
    Button,
    TableProps,
  } from "@cloudscape-design/components";

export default function ElegantPagination({ currentPageIndex, pagesCount, onChange }) {
    return (
        <div style={{marginLeft:'50px'}}>
        <SpaceBetween size="xxs" direction="horizontal">
            <Button
                iconName="angle-left"
                variant="icon"
                disabled={currentPageIndex === 1}
                onClick={() => onChange({ detail: { currentPageIndex: currentPageIndex - 1 } })}
            >
                Previous
            </Button>
            <div style={{ marginTop: "3px" }}>
                <Box color="text-body-secondary">
                    {pagesCount === 0 ? "" : `${currentPageIndex} of ${pagesCount}`}
                </Box>
            </div>
            <Button
                iconName="angle-right"
                variant="icon"
                disabled={currentPageIndex === pagesCount || pagesCount === 0}
                onClick={() => onChange({ detail: { currentPageIndex: currentPageIndex + 1 } })}
            >
                Next
            </Button>
        </SpaceBetween>
        </div>
    );
};