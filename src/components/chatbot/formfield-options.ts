import {
 SelectProps,
} from "@cloudscape-design/components";


export const duaPromptTemplate = `Generate a comprehensive Data Use Agreement (DUA) template for data sharing between [party1] and [party2]. This DUA should be written in formal business/legal language and comply with federal data sharing standards (e.g., FISMA, Privacy Act, applicable NIST guidelines).
The DUA should facilitate the sharing of data and must include all standard sections necessary to formalize the understanding between the two parties. The final document should be detailed and ready for legal or compliance review.
Template Structure Requirements:
1. Title Page
  Clearly label the document as a Data Use Agreement
  Identify [party1] and [party2] as the participating organizations
  Specify the type of data or systems covered by the DUA
  Include version number and effective date of [effectiveDate]
  Add a logo placeholder for [party1] at the top
2. Version History Table
Provide a table with the following columns:
  Version Number
  Written By: [authorName]
  Revision Date: [currentDate]
  Approved By: [approverName]
  Approval Date: [currentDate]
  Description of Change
3. Table of Contents
Generate a detailed table of contents referencing all major sections below, including subsections where applicable.
4. General Terms
Include the following:
  Parties to Agreement: Clearly define [party1] and [party2], including full legal names and descriptions of each organization's role
  Purpose and Scope: Describe the objectives of the data sharing and outline its scope
  Definitions: Provide key definitions related to data sharing, security, privacy, etc.
  Authority: Cite the legal or policy authority under which the DUA is executed
  Duration: State the effective date, duration of the DUA, renewal terms, and termination conditions
  Points of Contact: Include sections for designating technical and administrative contacts for both parties
5. Data Sharing Terms
  Description of Data: Outline what data will be shared, including any classifications or sensitivity
  Data Ownership: Clarify ownership and custodianship of the data
  Security and Privacy Requirements: Include standards such as encryption, access control, and compliance with federal privacy rules
  Data Transfer Methods: Describe how the data will be shared (e.g., secure FTP, API)
  Data Use Limitations: State any restrictions on how the shared data can be used
6. Roles and Responsibilities
Clearly delineate responsibilities of [party1] and [party2] in implementing and maintaining the data sharing arrangement.
7. Auditing and Monitoring
Include terms for periodic audits, usage tracking, and compliance verification.
8. Breach Notification
Define procedures for notifying the other party in case of a data breach.
9. Dispute Resolution
Provide a mechanism for resolving disagreements or issues under the DUA.
10. Signatures
Create placeholders for:
Authorized signatories from [party1] and [party2]
Printed name, title, signature, and date for each signatory
Create a structured table format to track dataset change requests.
IMPORTANT: The following must be included at the end of the document as a table and should be clearly labeled and properly formatted:
Appendix: Data Change Tracking Table
| Date of Data Change Request (MM/DD/YYYY) | Target Date of Completion (MM/DD/YYYY) | Data Change Requestor(s) | Brief Description of Data Change Request | Reason for Data Change Request | Special Conditions/Stipulations, if any | [PARTY A] Signature | [PARTY B] Signature | 
please response with markdown format without starting with \`\`\`markdown
 `;

export const partyOptions: SelectProps.Option[] = [
 {
  label: "Select Party",
  value: "",
  iconName: "close",
 },
 { label: "CDER", value: "cder" },
 { label: "CBER", value: "cber" },
 { label: "ODT", value: "odt" },
 { label: "CDRH", value: "cdrh" },
 { label: "CTP", value: "ctp" },
 { label: "HFP", value: "hfp" },
 { label: "OC", value: "oc" },
 { label: "OII", value: "oii" },
 { label: "CVM", value: "cvm" }
];