import { $getRoot, $getSelection, $isRangeSelection, FORMAT_TEXT_COMMAND, FORMAT_ELEMENT_COMMAND, TextFormatType, LexicalEditor, COMMAND_PRIORITY_NORMAL, createCommand, UNDO_COMMAND, REDO_COMMAND, $createParagraphNode, $createTextNode, TextNode, $isTextNode } from 'lexical';
import { useEffect, useRef, useState, useCallback } from 'react';

import { AutoFocusPlugin } from '@lexical/react/LexicalAutoFocusPlugin';
import { LexicalComposer } from '@lexical/react/LexicalComposer';
import { ContentEditable } from '@lexical/react/LexicalContentEditable';
import { HistoryPlugin } from '@lexical/react/LexicalHistoryPlugin';
import { OnChangePlugin } from '@lexical/react/LexicalOnChangePlugin';
import { RichTextPlugin } from '@lexical/react/LexicalRichTextPlugin';
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { LexicalErrorBoundary } from '@lexical/react/LexicalErrorBoundary';
import { ListPlugin } from '@lexical/react/LexicalListPlugin';
import { LinkPlugin } from '@lexical/react/LexicalLinkPlugin';
import { TablePlugin } from '@lexical/react/LexicalTablePlugin';
import { mergeRegister } from '@lexical/utils';
import { $patchStyleText } from '@lexical/selection';

import {
    TableNode,
    TableCellNode,
    TableRowNode
} from '@lexical/table';

import {
    ListNode,
    ListItemNode,
    INSERT_ORDERED_LIST_COMMAND,
    INSERT_UNORDERED_LIST_COMMAND,
    REMOVE_LIST_COMMAND,
    $isListNode,
    $isListItemNode
} from '@lexical/list';

import { LinkNode, TOGGLE_LINK_COMMAND } from '@lexical/link';


export const FORMAT_FONT_COMMAND = createCommand('FORMAT_FONT_COMMAND');
export const FORMAT_FONT_SIZE_COMMAND = createCommand('FORMAT_FONT_SIZE_COMMAND');
const INDENT_CONTENT_COMMAND = createCommand('INDENT_CONTENT_COMMAND');
const OUTDENT_CONTENT_COMMAND = createCommand('OUTDENT_CONTENT_COMMAND');

export const SET_EDITOR_CONTENT_COMMAND = createCommand('SET_EDITOR_CONTENT_COMMAND');


function ToolbarPlugin() {
    const [editor] = useLexicalComposerContext();
    const [isBold, setIsBold] = useState(false);
    const [isItalic, setIsItalic] = useState(false);
    const [isUnderline, setIsUnderline] = useState(false);
    const [isOrderedList, setIsOrderedList] = useState(false);
    const [isUnorderedList, setIsUnorderedList] = useState(false);
    const [alignmentType, setAlignmentType] = useState('left');
    const [fontFamily, setFontFamily] = useState('Arial');
    const [fontSize, setFontSize] = useState('14px');

    // Define font options
    const FONT_FAMILY_OPTIONS: [string, string][] = [
        ['Arial', 'Arial'],
        ['Courier New', 'Courier New'],
        ['Georgia', 'Georgia'],
        ['Times New Roman', 'Times New Roman'],
        ['Trebuchet MS', 'Trebuchet MS'],
        ['Verdana', 'Verdana'],
        ['Calibri', 'Calibri'],
    ];

    const FONT_SIZE_OPTIONS: [string, string][] = [
        ['10px', '10'],
        ['11px', '11'],
        ['12px', '12'],
        ['14px', '14'],
        ['16px', '16'],
        ['18px', '18'],
        ['20px', '20'],
        ['22px', '22'],
        ['24px', '24'],
        ['28px', '28'],
        ['32px', '32'],
        ['36px', '36'],
        ['42px', '42'],
    ];

    const updateToolbar = useCallback(() => {
        const selection = $getSelection();
        if ($isRangeSelection(selection)) {
            setIsBold(selection.hasFormat('bold'));
            setIsItalic(selection.hasFormat('italic'));
            setIsUnderline(selection.hasFormat('underline'));

            // Check font family of the current selection
            const node = selection.anchor.getNode();
            if ($isTextNode(node)) {
                const fontFamilyStyle = node.getStyle()?.['font-family'];
                if (fontFamilyStyle) {
                    // Remove quotes from font name if any
                    const cleanFont = fontFamilyStyle.replace(/["']/g, '');
                    // Find matching font in options
                    const fontOption = FONT_FAMILY_OPTIONS.find(
                        ([value]) => value.toLowerCase() === cleanFont.toLowerCase()
                    );
                    if (fontOption) {
                        setFontFamily(fontOption[0]);
                    }
                }

                // Check font size of the current selection
                const fontSizeStyle = node.getStyle()?.['font-size'];
                if (fontSizeStyle) {
                    const fontSizeOption = FONT_SIZE_OPTIONS.find(
                        ([value]) => value === fontSizeStyle
                    );
                    if (fontSizeOption) {
                        setFontSize(fontSizeOption[0]);
                    }
                }
            }

            // Check if selection is in a list
            const anchorNode = selection.anchor.getNode();
            let element = anchorNode;
            let isInList = false;
            let listType = '';

            while (element) {
                if ($isListItemNode(element)) {
                    isInList = true;
                    const parent = element.getParent();
                    if (parent && $isListNode(parent)) {
                        listType = parent.getTag();
                    }
                    break;
                }
                element = element.getParent();
            }

            setIsOrderedList(isInList && listType === 'ol');
            setIsUnorderedList(isInList && listType === 'ul');
        }
    }, [FONT_FAMILY_OPTIONS, FONT_SIZE_OPTIONS]);

    useEffect(() => {
        return mergeRegister(
            editor.registerUpdateListener(({ editorState }) => {
                editorState.read(() => {
                    updateToolbar();
                });
            })
        );
    }, [editor, updateToolbar]);

    useEffect(() => {
        // Register font command
        editor.registerCommand(
            FORMAT_FONT_COMMAND,
            (fontFamily: string) => {
                editor.update(() => {
                    const selection = $getSelection();
                    if ($isRangeSelection(selection)) {
                        $patchStyleText(selection, {
                            'font-family': fontFamily,
                        });
                    }
                });
                setFontFamily(fontFamily);
                return true;
            },
            COMMAND_PRIORITY_NORMAL
        );

        // Register font size command
        editor.registerCommand(
            FORMAT_FONT_SIZE_COMMAND,
            (fontSize: string) => {
                editor.update(() => {
                    const selection = $getSelection();
                    if ($isRangeSelection(selection)) {
                        $patchStyleText(selection, {
                            'font-size': fontSize,
                        });
                    }
                });
                setFontSize(fontSize);
                return true;
            },
            COMMAND_PRIORITY_NORMAL
        );

        // Register indent command
        editor.registerCommand(
            INDENT_CONTENT_COMMAND,
            () => {
                console.log('Indenting content');
                return true;
            },
            COMMAND_PRIORITY_NORMAL
        );

        // Register outdent command
        editor.registerCommand(
            OUTDENT_CONTENT_COMMAND,
            () => {
                console.log('Outdenting content');
                return true;
            },
            COMMAND_PRIORITY_NORMAL
        );
    }, [editor]);

    const toggleOrderedList = () => {
        if (isOrderedList) {
            editor.dispatchCommand(REMOVE_LIST_COMMAND, undefined);
        } else {
            editor.dispatchCommand(INSERT_ORDERED_LIST_COMMAND, undefined);
        }
    };

    const toggleUnorderedList = () => {
        if (isUnorderedList) {
            editor.dispatchCommand(REMOVE_LIST_COMMAND, undefined);
        } else {
            editor.dispatchCommand(INSERT_UNORDERED_LIST_COMMAND, undefined);
        }
    };

    const toolbarButtonStyle = {
        marginRight: '4px',
        padding: '4px 8px',
        backgroundColor: 'transparent',
        border: 'none',
        cursor: 'pointer',
        borderRadius: '4px',
        color: '#333',
        fontSize: '12px',
        minWidth: '24px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
    };

    const activeButtonStyle = {
        ...toolbarButtonStyle,
        backgroundColor: '#e0e0e0',
    };

    const dividerStyle = {
        height: '20px',
        width: '0px',
        backgroundColor: 'transparent',
        margin: '0 4px',
    };

    const selectStyle = {
        marginRight: '8px',
        padding: '4px 8px',
        backgroundColor: 'white',
        border: '1px solid #ddd',
        borderRadius: '4px',
        fontSize: '12px',
        cursor: 'pointer',
        color: '#333',
    };

    return (
        <div style={{
            borderBottom: 'none',
            padding: '8px 6px',
            paddingBottom: '10px',
            display: 'flex',
            backgroundColor: 'transparent',
            flexWrap: 'wrap',
            alignItems: 'center',
            width: '100%',
            maxWidth: '100%',
            overflowX: 'auto'
        }}>
            <button
                style={toolbarButtonStyle}
                onClick={() => {
                    editor.dispatchCommand(UNDO_COMMAND, undefined);
                }}
                title="Undo"
            >
                ↺
            </button>
            <button
                style={toolbarButtonStyle}
                onClick={() => {
                    editor.dispatchCommand(REDO_COMMAND, undefined);
                }}
                title="Redo"
            >
                ↻
            </button>

            <div style={dividerStyle}></div>

            {/* Font Family Select */}
            <select
                style={selectStyle}
                value={fontFamily}
                onChange={(e) => {
                    editor.dispatchCommand(FORMAT_FONT_COMMAND, e.target.value);
                }}
                title="Font family"
            >
                {FONT_FAMILY_OPTIONS.map(([value, label]) => (
                    <option key={value} value={value}>
                        {label}
                    </option>
                ))}
            </select>

            {/* Font Size Select */}
            <select
                style={selectStyle}
                value={fontSize}
                onChange={(e) => {
                    editor.dispatchCommand(FORMAT_FONT_SIZE_COMMAND, e.target.value);
                }}
                title="Font size"
            >
                {FONT_SIZE_OPTIONS.map(([value, label]) => (
                    <option key={value} value={value}>
                        {label}
                    </option>
                ))}
            </select>

            <div style={dividerStyle}></div>

            <button
                style={isOrderedList ? activeButtonStyle : toolbarButtonStyle}
                onClick={toggleOrderedList}
                title={isOrderedList ? "Remove ordered list" : "Ordered list"}
            >
                1.
            </button>
            <div style={dividerStyle}></div>
            <button
                style={isUnorderedList ? activeButtonStyle : toolbarButtonStyle}
                onClick={toggleUnorderedList}
                title={isUnorderedList ? "Remove unordered list" : "Unordered list"}
            >
                •
            </button>
            <div style={dividerStyle}></div>

            <button
                style={isBold ? activeButtonStyle : toolbarButtonStyle}
                onClick={() => {
                    editor.dispatchCommand(FORMAT_TEXT_COMMAND, 'bold');
                }}
                title="Bold"
            >
                B
            </button>
            <button
                style={isItalic ? activeButtonStyle : toolbarButtonStyle}
                onClick={() => {
                    editor.dispatchCommand(FORMAT_TEXT_COMMAND, 'italic');
                }}
                title="Italic"
            >
                I
            </button>
            <button
                style={isUnderline ? activeButtonStyle : toolbarButtonStyle}
                onClick={() => {
                    editor.dispatchCommand(FORMAT_TEXT_COMMAND, 'underline');
                }}
                title="Underline"
            >
                U
            </button>

            <div style={dividerStyle}></div>

            <button
                style={alignmentType === 'left' ? activeButtonStyle : toolbarButtonStyle}
                onClick={() => {
                    editor.dispatchCommand(FORMAT_ELEMENT_COMMAND, 'left');
                    setAlignmentType('left');
                }}
                title="Left align"
            >
                ⫷
            </button>
            <button
                style={alignmentType === 'center' ? activeButtonStyle : toolbarButtonStyle}
                onClick={() => {
                    editor.dispatchCommand(FORMAT_ELEMENT_COMMAND, 'center');
                    setAlignmentType('center');
                }}
                title="Center"
            >
                ⫸⫷
            </button>
            <button
                style={alignmentType === 'right' ? activeButtonStyle : toolbarButtonStyle}
                onClick={() => {
                    editor.dispatchCommand(FORMAT_ELEMENT_COMMAND, 'right');
                    setAlignmentType('right');
                }}
                title="Right align"
            >
                ⫸
            </button>
            <button
                style={alignmentType === 'justify' ? activeButtonStyle : toolbarButtonStyle}
                onClick={() => {
                    editor.dispatchCommand(FORMAT_ELEMENT_COMMAND, 'justify');
                    setAlignmentType('justify');
                }}
                title="Justified alignment"
            >
                ⫸|⫷
            </button>

            <div style={dividerStyle}></div>
        </div>
    );
}


function CustomAutoFocusPlugin() {
    const [editor] = useLexicalComposerContext();

    useEffect(() => {
        editor.focus();
    }, [editor]);

    return null;
}


function PlaceholderPositionPlugin() {
    const [editor] = useLexicalComposerContext();

    const adjustPlaceholderPosition = useCallback(() => {
        const editorDiv = document.querySelector('.lexical-content-editable') as HTMLElement;
        const placeholder = document.querySelector('.lexical-placeholder') as HTMLElement;

        if (editorDiv && placeholder) {
            const editorStyles = window.getComputedStyle(editorDiv);

            const paddingTop = parseFloat(editorStyles.paddingTop);
            const paddingLeft = parseFloat(editorStyles.paddingLeft);

            const fontSize = parseFloat(editorStyles.fontSize);
            const lineHeight = parseFloat(editorStyles.lineHeight) || fontSize * 1.2;

            const topOffset = paddingTop;
            const leftOffset = paddingLeft + 5;

            placeholder.style.top = `${topOffset}px`;
            placeholder.style.left = `${leftOffset}px`;
            placeholder.style.fontSize = editorStyles.fontSize;
            placeholder.style.lineHeight = editorStyles.lineHeight;
        }
    }, []);

    useEffect(() => {
        setTimeout(adjustPlaceholderPosition, 0);

        return editor.registerUpdateListener(() => {
            setTimeout(adjustPlaceholderPosition, 0);
        });
    }, [editor, adjustPlaceholderPosition]);

    useEffect(() => {
        window.addEventListener('resize', adjustPlaceholderPosition);
        return () => {
            window.removeEventListener('resize', adjustPlaceholderPosition);
        };
    }, [adjustPlaceholderPosition]);

    return null;
}


const theme = {
    paragraph: 'lexical-paragraph',
    text: {
        bold: 'lexical-text-bold',
        italic: 'lexical-text-italic',
        underline: 'lexical-text-underline',
    },
};


const editorStyles = `
  .lexical-editor-container {
    position: relative;
    border: none;
    border-radius: 4px;
    overflow: visible;
    display: flex;
    flex-direction: column;
    margin-bottom: 15px;
    transition: min-height 0.5s ease;
    max-height: none;
    width: 100%;
  }
  
  .lexical-content-editable {
    min-height: 50px;
    padding: 12px 10px;
    outline: none;
    resize: none;
    font-size: 14px;
    line-height: 1.5;
    overflow-y: visible;
    position: relative;
    transition: min-height 0.5s ease;
    max-height: none;
    width: 100%;
    word-wrap: break-word;
    white-space: pre-wrap;
    overflow-wrap: break-word;
  }
  
  
  .lexical-content-editable.long-content-mode {
    min-height: min(200px, 30vh);
    padding: 15px 12px;
    overflow-y: visible;
    font-size: 14px;
    line-height: 1.6;
    max-height: none;
    word-wrap: break-word;
    white-space: pre-wrap;
    overflow-wrap: break-word;
  }
  
  
  .lexical-content-editable.long-content-mode::-webkit-scrollbar {
    width: 8px;
  }
  
  .lexical-content-editable.long-content-mode::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }
  
  .lexical-content-editable.long-content-mode::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
  }
  
  .lexical-content-editable.long-content-mode::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }
  
  .lexical-paragraph {
    margin: 0;
    width: 100%;
    word-wrap: break-word;
    white-space: pre-wrap;
    overflow-wrap: break-word;
  }
  
  .lexical-text-bold {
    font-weight: bold;
  }
  
  .lexical-text-italic {
    font-style: italic;
  }
  
  .lexical-text-underline {
    text-decoration: underline;
  }
  
  
  .lexical-placeholder {
    color: #888;
    font-weight: 600;
    overflow: hidden;
    position: absolute;
    text-overflow: ellipsis;
    pointer-events: none;
    user-select: none;
    z-index: 0;
  }
  
  
  .toolbar-container {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
    flex-shrink: 0;
    background-color: transparent;
    width: 100%;
    overflow-x: auto;
  }
  
  .editor-content-container {
    position: relative;
    flex-grow: 1;
    overflow: visible;
    border-top: none;
    margin-top: 0;
    background-color: transparent;
    transition: min-height 0.5s ease;
    max-height: none;
    width: 100%;
  }
  
  
  .horizontal-line {
    height: 0;
    background-color: transparent;
    margin: 0;
    padding: 0;
    width: 100%;
    border: none;
  }
  
  
  .editor-border {
    border: none;
    border-radius: 4px;
    overflow: visible;
    transition: min-height 0.5s ease;
    max-height: none;
    width: 100%;
  }
  
  
  .dua-content .lexical-content-editable {
    min-height: min(400px, 40vh);
    max-height: none;
    line-height: 1.8;
    font-size: 14px;
    padding: 20px 15px;
    word-wrap: break-word;
    white-space: pre-wrap;
    overflow-wrap: break-word;
  }
  
  
  .dua-content .table, 
  .dua-content table {
    border-collapse: collapse;
    margin: 15px 0;
    width: 100%;
    max-width: 100%;
    table-layout: fixed;
    overflow-wrap: break-word;
  }
  
  .dua-content .table td, 
  .dua-content .table th,
  .dua-content table td,
  .dua-content table th {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
    word-wrap: break-word;
    overflow-wrap: break-word;
  }
  
  .dua-content .table th,
  .dua-content table th {
    background-color: #f2f2f2;
  }
  
  
  @media screen and (max-height: 600px) {
    .lexical-content-editable.long-content-mode {
      min-height: 150px;
    }
    
    .dua-content .lexical-content-editable {
      min-height: 200px;
    }
  }
  
  
  @media screen and (min-height: 900px) {
    .lexical-content-editable.long-content-mode {
      min-height: 250px;
    }
    
    .dua-content .lexical-content-editable {
      min-height: 400px;
    }
  }
`;


function injectStyles() {
    const styleElement = document.createElement('style');
    styleElement.textContent = editorStyles;
    document.head.appendChild(styleElement);
    return () => {
        document.head.removeChild(styleElement);
    };
}

interface LexicalRichTextEditorProps {
    placeholder?: string;
    onChange?: (text: string, html: string) => void;
    onSubmit?: (text: string, html: string) => void;
    initialValue?: string;
    maxHeight?: number;
}


function EditorInstancePlugin() {
    const [editor] = useLexicalComposerContext();

    useEffect(() => {
        (window as any).lexicalEditor = editor;

        return () => {
            delete (window as any).lexicalEditor;
        };
    }, [editor]);

    return null;
}

export default function LexicalRichTextEditor({
                                                  placeholder = 'Send a message...',
                                                  onChange,
                                                  onSubmit,
                                                  initialValue = '',
                                                  maxHeight = 200,
                                              }: LexicalRichTextEditorProps) {
    const editorRef = useRef<HTMLDivElement>(null);
    const [isEmpty, setIsEmpty] = useState(!initialValue);

    const [contentLength, setContentLength] = useState(initialValue.length);

    const [screenHeight, setScreenHeight] = useState(window.innerHeight);


    const calculateMinEditorHeight = () => {
        const screenRatio = contentLength > 3000 ? 0.3 : (contentLength > 1000 ? 0.2 : 0.1);
        return Math.max(50, Math.floor(screenHeight * screenRatio));
    };


    const dynamicMinHeight = calculateMinEditorHeight();


    useEffect(() => {
        const handleResize = () => {
            setScreenHeight(window.innerHeight);
        };

        window.addEventListener('resize', handleResize);
        return () => {
            window.removeEventListener('resize', handleResize);
        };
    }, []);


    const adjustEditorHeight = (content: string) => {
        const contentLength = content.length;

        const editorDiv = document.querySelector('.lexical-content-editable') as HTMLElement;
        const container = document.querySelector('.editor-content-container') as HTMLElement;

        if (editorDiv && container) {
            if (contentLength > 3000) {
                editorDiv.classList.add('long-content-mode');
                editorDiv.style.minHeight = `${Math.min(400, screenHeight * 0.3)}px`;

                editorDiv.style.maxHeight = 'none';
                container.style.minHeight = `${Math.min(410, screenHeight * 0.31)}px`;
                container.style.maxHeight = 'none';
            } else if (contentLength > 1000) {
                editorDiv.classList.add('long-content-mode');
                editorDiv.style.minHeight = `${Math.min(200, screenHeight * 0.15)}px`;
                editorDiv.style.maxHeight = 'none';
                container.style.minHeight = `${Math.min(210, screenHeight * 0.16)}px`;
                container.style.maxHeight = 'none';
            } else if (contentLength > 0) {
                editorDiv.classList.remove('long-content-mode');
                editorDiv.style.minHeight = '50px';
                editorDiv.style.maxHeight = 'none';
                container.style.minHeight = '';
                container.style.maxHeight = 'none';
            } else {
                editorDiv.classList.remove('long-content-mode');
                editorDiv.style.minHeight = '50px';
                editorDiv.style.maxHeight = 'none';
                container.style.minHeight = '';
                container.style.maxHeight = 'none';
            }
        }
    };


    useEffect(() => {
        const cleanup = injectStyles();
        return cleanup;
    }, []);


    const onError = (error: Error) => {
        console.error(error);
    };


    const initialConfig = {
        namespace: 'ChatInputEditor',
        theme,
        onError,
        nodes: [
            TableNode,
            TableCellNode,
            TableRowNode,
            ListNode,
            ListItemNode,
            LinkNode
        ]
    };


    const handleEditorChange = (editorState: any) => {
        editorState.read(() => {
            const root = $getRoot();
            const text = root.getTextContent();

            setIsEmpty(!text.trim());
            setContentLength(text.length);

            adjustEditorHeight(text);

            const htmlContent = text;

            if (onChange) {
                onChange(text, htmlContent);
            }
        });
    };

    const handleKeyDown = useCallback(
        (event: React.KeyboardEvent<HTMLDivElement>) => {
            if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
                event.preventDefault();

                const editor = (window as any).lexicalEditor;
                if (editor) {
                    editor.getEditorState().read(() => {
                        const root = $getRoot();
                        const text = root.getTextContent();
                        const htmlContent = text;

                        if (onSubmit) {
                            onSubmit(text, htmlContent);
                        }
                    });
                }
                return true;
            }

            if (event.key === 'Enter' && !event.shiftKey && !event.ctrlKey && !event.metaKey && !event.altKey) {
                const selection = window.getSelection();
                const node = selection?.anchorNode;
                if (node) {
                    let parent = node.parentElement;
                    while (parent) {
                        if (parent.tagName === 'TABLE') {
                            event.preventDefault();
                            return false;
                        }
                        parent = parent.parentElement;
                    }
                }
            }
            return false;
        },
        [onSubmit]
    );


    useEffect(() => {
        const handleSetContent = (event: CustomEvent) => {
            const { content, isLongContent, screenHeight, removeMaxHeight } = event.detail;

            const editor = (window as any).lexicalEditor;
            if (editor) {
                const payload = {
                    content,
                    isLongContent: isLongContent || content.includes('Data Use Agreement') || content.includes('DUA'),
                    adjustHeightFn: adjustEditorHeight,
                    screenHeight: screenHeight || window.innerHeight,
                    removeMaxHeight: removeMaxHeight || content.includes('Data Use Agreement') || content.includes('DUA')
                };

                editor.dispatchCommand(SET_EDITOR_CONTENT_COMMAND, payload);

                setContentLength(content.length);

                setIsEmpty(!content.trim());
            }
        };

        const container = editorRef.current;
        if (container) {
            container.removeEventListener('set-editor-content', handleSetContent as EventListener);

            container.addEventListener('set-editor-content', handleSetContent as EventListener);

            console.log('Event listener for set-editor-content registered on', container);
        }

        return () => {
            if (container) {
                container.removeEventListener('set-editor-content', handleSetContent as EventListener);
            }
        };
    }, []);

    return (
        <div className="editor-border">
            <div
                className="lexical-editor-container"
                ref={editorRef}
                onKeyDown={handleKeyDown}
                style={{
                    minHeight: dynamicMinHeight,
                    maxHeight: 'none',
                    transition: 'min-height 0.5s ease'
                }}
            >
                <LexicalComposer initialConfig={initialConfig}>
                    <div className="toolbar-container">
                        <ToolbarPlugin />
                    </div>
                    <div
                        className="editor-content-container"
                        style={{
                            minHeight: dynamicMinHeight > 100 ? dynamicMinHeight - 60 : 'auto',
                            maxHeight: 'none',
                            transition: 'min-height 0.5s ease'
                        }}
                    >
                        <RichTextPlugin
                            contentEditable={
                                <ContentEditable
                                    className="lexical-content-editable"
                                    style={{
                                        minHeight: '50px',
                                        maxHeight: 'none'
                                    }}
                                />
                            }
                            placeholder={
                                isEmpty ? <div className="lexical-placeholder">{placeholder}</div> : null
                            }
                            ErrorBoundary={LexicalErrorBoundary}
                        />
                    </div>
                    <div className="horizontal-line"></div>
                    <OnChangePlugin onChange={handleEditorChange} />
                    <HistoryPlugin />
                    <ListPlugin />
                    <LinkPlugin />
                    <TablePlugin />
                    <CustomAutoFocusPlugin />
                    <SetContentPlugin />
                    <EditorInstancePlugin />
                    <PlaceholderPositionPlugin />
                </LexicalComposer>
            </div>
        </div>
    );
}


function SetContentPlugin() {
    const [editor] = useLexicalComposerContext();


    const applyOverflowFixes = (editorDiv: HTMLElement, content: string) => {

        editorDiv.style.maxHeight = 'none';
        const container = document.querySelector('.editor-content-container') as HTMLElement;
        const mainContainer = document.querySelector('.editor-border') as HTMLElement;
        const editorContainer = document.querySelector('.lexical-editor-container') as HTMLElement;

        if (container) {
            container.style.maxHeight = 'none';
            container.style.width = '100%';
        }
        if (mainContainer) {
            mainContainer.style.maxHeight = 'none';
            mainContainer.style.width = '100%';
        }
        if (editorContainer) {
            editorContainer.style.maxHeight = 'none';
            editorContainer.style.width = '100%';
        }


        editorDiv.style.overflowY = 'visible';
        editorDiv.style.width = '100%';
        editorDiv.style.wordWrap = 'break-word';
        editorDiv.style.whiteSpace = 'pre-wrap';
        editorDiv.style.overflowWrap = 'break-word';

        if (container) container.style.overflow = 'visible';
        if (mainContainer) mainContainer.style.overflow = 'visible';
        if (editorContainer) editorContainer.style.overflow = 'visible';


        const paragraphs = editorDiv.querySelectorAll('p');
        paragraphs.forEach(p => {
            (p as HTMLElement).style.maxWidth = '100%';
            (p as HTMLElement).style.width = '100%';
            (p as HTMLElement).style.wordWrap = 'break-word';
            (p as HTMLElement).style.whiteSpace = 'pre-wrap';
            (p as HTMLElement).style.overflowWrap = 'break-word';
        });


        setTimeout(() => {
            const tables = editorDiv.querySelectorAll('table');
            tables.forEach(table => {
                table.style.width = '100%';
                table.style.maxWidth = '100%';
                table.style.tableLayout = 'fixed';
                table.style.wordWrap = 'break-word';
                table.style.borderCollapse = 'collapse';
                table.style.margin = '15px 0';


                const tableRows = table.querySelectorAll('tr');
                if (tableRows.length > 0) {

                    const firstRow = tableRows[0];
                    const cellCount = firstRow.querySelectorAll('th, td').length;
                    const cellWidth = 100 / Math.max(1, cellCount);


                    tableRows.forEach((row, rowIndex) => {
                        row.style.minHeight = '30px';

                        const cells = row.querySelectorAll('th, td');
                        cells.forEach((cell, cellIndex) => {
                            (cell as HTMLElement).style.width = `${cellWidth}%`;
                            (cell as HTMLElement).style.minWidth = '60px';
                            (cell as HTMLElement).style.wordWrap = 'break-word';
                            (cell as HTMLElement).style.overflowWrap = 'break-word';
                            (cell as HTMLElement).style.padding = '8px';
                            (cell as HTMLElement).style.border = '1px solid #ddd';
                            (cell as HTMLElement).style.textAlign = 'left';
                            (cell as HTMLElement).style.verticalAlign = 'top';
                            (cell as HTMLElement).style.lineHeight = '1.4';
                        });
                    });
                }


                const headers = table.querySelectorAll('th');
                headers.forEach(header => {
                    (header as HTMLElement).style.backgroundColor = '#f2f2f2';
                    (header as HTMLElement).style.fontWeight = 'bold';
                    (header as HTMLElement).style.borderBottom = '2px solid #ddd';
                });
            });


            if (content.includes('Data Use Agreement') || content.includes('DUA')) {
                editorDiv.classList.add('long-content-mode');
                const editorContainer = document.querySelector('.editor-border') as HTMLElement;
                if (editorContainer) {
                    editorContainer.classList.add('dua-content');
                }
            }
        }, 100);
    };

    useEffect(() => {
        return editor.registerCommand(
            SET_EDITOR_CONTENT_COMMAND,
            (payload: {
                content: string,
                isLongContent?: boolean,
                adjustHeightFn?: (content: string) => void,
                screenHeight?: number,
                removeMaxHeight?: boolean
            }) => {
                const content = typeof payload === 'string' ? payload : payload.content;
                const isLongContent = typeof payload === 'object' ? payload.isLongContent : false;
                const adjustHeightFn = typeof payload === 'object' ? payload.adjustHeightFn : null;
                const customScreenHeight = typeof payload === 'object' ? payload.screenHeight : window.innerHeight;
                const removeMaxHeight = typeof payload === 'object' ? payload.removeMaxHeight : false;


                const isDUAContent = content.includes('Data Use Agreement') || content.includes('DUA');

                editor.update(() => {
                    const root = $getRoot();

                    root.clear();


                    if (content.includes('|') && (isDUAContent)) {

                        const lines = content.split('\n');
                        lines.forEach((line) => {

                            const paragraph = $createParagraphNode();

                            const text = $createTextNode(line);

                            paragraph.append(text);

                            root.append(paragraph);
                        });
                    } else {

                        if (isDUAContent) {
                            const lines = content.split('\n');
                            for (const line of lines) {
                                const paragraph = $createParagraphNode();
                                const text = $createTextNode(line);
                                paragraph.append(text);
                                root.append(paragraph);
                            }
                        } else {

                            const paragraph = $createParagraphNode();
                            const text = $createTextNode(content);
                            paragraph.append(text);
                            root.append(paragraph);
                        }
                    }


                    setTimeout(() => {

                        if (adjustHeightFn) {
                            adjustHeightFn(content);
                        } else {

                            const editorDiv = document.querySelector('.lexical-content-editable') as HTMLElement;
                            if (editorDiv) {
                                const contentLength = content.length;


                                if (removeMaxHeight || isDUAContent) {

                                    applyOverflowFixes(editorDiv, content);
                                }


                                const screenBasedMinHeight = Math.floor(customScreenHeight * 0.1);

                                let newHeight = Math.max(
                                    screenBasedMinHeight,
                                    Math.min(400, 50 + Math.floor(contentLength / 1000) * 50)
                                );


                                if (isDUAContent) {
                                    newHeight = Math.max(
                                        newHeight,
                                        Math.min(
                                            Math.floor(customScreenHeight * 0.3),
                                            400 + Math.floor(contentLength / 2000) * 100
                                        )
                                    );
                                    editorDiv.style.minHeight = `${Math.min(400, customScreenHeight * 0.3)}px`;

                                    const editorContainer = document.querySelector('.editor-border') as HTMLElement;
                                    if (editorContainer) {
                                        editorContainer.classList.add('dua-content');
                                    }

                                    const container = document.querySelector('.editor-content-container') as HTMLElement;
                                    if (container) {
                                        container.style.minHeight = `${Math.min(newHeight + 10, customScreenHeight * 0.31)}px`;
                                    }

                                    editorDiv.classList.add('long-content-mode');
                                }

                                else if (isLongContent || contentLength > 2000) {
                                    newHeight = Math.max(
                                        newHeight,
                                        Math.min(
                                            customScreenHeight * 0.25,
                                            100 + Math.floor(contentLength / 1000) * 50
                                        )
                                    );
                                    editorDiv.style.minHeight = `${Math.min(newHeight, customScreenHeight * 0.3)}px`;

                                    const container = document.querySelector('.editor-content-container') as HTMLElement;
                                    if (container) {
                                        container.style.minHeight = `${Math.min(newHeight + 10, customScreenHeight * 0.32)}px`;
                                    }

                                    editorDiv.classList.add('long-content-mode');
                                }
                            }
                        }
                    }, 0);
                });
                return true;
            },
            COMMAND_PRIORITY_NORMAL
        );
    }, [editor]);

    return null;
}

// Bold and Highlight prompt [partA/B]
export function createFormattedLexicalState(text: string): string {
    // Split the text by the placeholder patterns
    const parts = text.split(/(\[PARTY A\]|\[PARTY B\]|\[partyA\]|\[partyB\])/g);

    // Create the Lexical nodes structure
    const nodes: any[] = [];
    let currentParagraph: any = {
        type: "paragraph",
        children: []
    };

    parts.forEach(part => {
        if (part === '[PARTY A]' || part === '[PARTY B]' || part === '[partyA]' || part === '[partyB]') {
            // Create a text node with bold format and background color for placeholders
            currentParagraph.children.push({
                type: "text",
                text: part,
                format: 1, // 1 is the value for BOLD in Lexical
                style: "background-color: #FFFF00;", // Yellow highlight
                mode: 0,
                detail: 0
            });
        } else if (part) {
            // For regular text, just create a normal text node
            currentParagraph.children.push({
                type: "text",
                text: part,
                format: 0, // No formatting
                style: "",
                mode: 0,
                detail: 0
            });
        }

        // Check if the part ends with a newline
        if (part.endsWith('\n')) {
            // Add the current paragraph to nodes
            nodes.push(currentParagraph);
            // Create a new paragraph
            currentParagraph = {
                type: "paragraph",
                children: []
            };
        }
    });

    // Add the last paragraph if it has children
    if (currentParagraph.children.length > 0) {
        nodes.push(currentParagraph);
    }

    // Create the full Lexical state
    const lexicalState = {
        root: {
            type: "root",
            children: nodes,
            direction: null,
            format: "",
            indent: 0,
            version: 1
        }
    };

    return JSON.stringify(lexicalState);
}


export function setEditorContent(editor: LexicalEditor, content: string) {
    editor.dispatchCommand(SET_EDITOR_CONTENT_COMMAND, content);
}