import { useMCP } from "../../contexts/mcp-context";
import { ChatBotMessageType, ChatInputPanelProps, ChatBotHistoryItem } from "./types";
import { SET_EDITOR_CONTENT_COMMAND } from './lexical-editor';
import * as React from "react";

interface MCPHandlerOptions {
  servers: any[];
  getClient: (serverId: string) => any;
  messageHistoryRef: React.MutableRefObject<ChatBotHistoryItem[]>;
  setState: (state: any) => void;
  props: ChatInputPanelProps;
}

export class MCPHandler {
  private servers: any[];
  private getClient: (serverId: string) => any;
  private messageHistoryRef: React.MutableRefObject<ChatBotHistoryItem[]>;
  private setState: (state: any) => void;
  private props: ChatInputPanelProps;

  constructor(options: MCPHandlerOptions) {
    this.servers = options.servers;
    this.getClient = options.getClient;
    this.messageHistoryRef = options.messageHistoryRef;
    this.setState = options.setState;
    this.props = options.props;
  }

  async handleCommand(command: string): Promise<void> {
    // console.log("handleMCPCommand被调用，命令:", command);
    // console.log("MCP服务器数量:", this.servers.length);

    if (this.servers.length === 0) {
      this.showError("No MCP server configured. Please go to the MCP server page to add and connect to a server.");
      return;
    }

    this.clearInput();
    this.addUserMessage(command);
    this.addPendingAIMessage();

    try {
      const connectedServer = this.getConnectedServer();
      if (!connectedServer) {
        throw new Error("No connected MCP server found");
      }

      const mcpClient = this.getClient(connectedServer.id);
      if (!mcpClient) {
        throw new Error(`Unable to get MCP client for server ${connectedServer.name}`);
      }

      const response = await this.executeCommand(connectedServer, command);
      this.updateAIMessage(response);
    } catch (error) {
      this.handleError(error);
    } finally {
      this.props.setMessageHistory(this.messageHistoryRef.current);
      this.props.setRunning(false);
    }
  }

  private showError(message: string): void {
    this.messageHistoryRef.current = [
      ...this.messageHistoryRef.current,
      {
        type: ChatBotMessageType.Human,
        content: `/mcp ${message}`,
        metadata: { ...this.props.configuration },
        tokens: [],
        chatId: this.props.chatId,
      },
      {
        type: ChatBotMessageType.AI,
        tokens: [],
        content: message,
        metadata: {},
        chatId: this.props.chatId,
      },
    ];
    this.props.setMessageHistory(this.messageHistoryRef.current);
  }

  private clearInput(): void {
    this.setState((state: any) => ({ ...state, value: "" }));

    const editor = (window as any).lexicalEditor;
    if (editor) {
      editor.dispatchCommand(SET_EDITOR_CONTENT_COMMAND, {
        content: "",
        isLongContent: false,
        removeMaxHeight: false
      });
    }
  }

  private addUserMessage(command: string): void {
    this.messageHistoryRef.current = [
      ...this.messageHistoryRef.current,
      {
        type: ChatBotMessageType.Human,
        content: `/mcp ${command}`,
        metadata: { ...this.props.configuration },
        tokens: [],
        chatId: this.props.chatId,
      },
    ];
  }

  private addPendingAIMessage(): void {
    this.messageHistoryRef.current = [
      ...this.messageHistoryRef.current,
      {
        type: ChatBotMessageType.AI,
        tokens: [],
        content: "Processing MCP request...",
        metadata: {},
        chatId: this.props.chatId,
      },
    ];
    this.props.setMessageHistory(this.messageHistoryRef.current);
    this.props.setRunning(true);
  }

  private getConnectedServer(): any {
    const connectedServer = this.servers.find(server => server.isConnected);
    // console.log("查找已连接的MCP服务器:", connectedServer ? connectedServer.name : "未找到");
    return connectedServer;
  }

  private async executeCommand(connectedServer: any, command: string): Promise<any> {
    const [toolName, ...args] = command.trim().split(/\s+/);
    const toolArgs = this.parseArguments(args);

    if (!connectedServer.url) {
      throw new Error("Server URL undefined, unable to send request");
    }

    return await this.sendRequest(connectedServer.url, toolName, toolArgs, command);
  }

  private parseArguments(args: string[]): any {
    let toolArgs = {};

    if (args.length === 0) return toolArgs;

    const argsText = args.join(' ');

    try {
      // 首先尝试作为完整的JSON对象解析
      if (argsText.trim().startsWith('{') && argsText.trim().endsWith('}')) {
        try {
          toolArgs = JSON.parse(argsText.trim());
          return toolArgs;
        } catch (jsonError) {
          console.warn("JSON parsing failed, falling back to parameter parsing:", jsonError);
        }
      }

      // 如果不是有效的JSON对象，解析参数
      toolArgs = this.parseKeyValueArguments(argsText);

      // 如果没有参数匹配，将剩余文本作为query参数
      if (Object.keys(toolArgs).length === 0) {
        toolArgs = { query: argsText };
      }
    } catch (e) {
      console.error("Parameter parsing error:", e);
      toolArgs = { query: argsText };
    }

    return toolArgs;
  }

  private parseKeyValueArguments(argsText: string): any {
    const toolArgs: any = {};

    // 尝试检测JSON格式参数，如数组或复杂对象
    const jsonRegex = /(\w+)=([\[{].*?[\]}])/g;
    let match;
    while ((match = jsonRegex.exec(argsText)) !== null) {
      const [_, key, jsonValue] = match;
      try {
        toolArgs[key] = JSON.parse(jsonValue);
      } catch (e) {
        console.warn(`Cannot parse JSON parameters ${key}=${jsonValue}:`, e);
        toolArgs[key] = jsonValue;
      }
    }

    // 尝试解析key=value形式的参数
    const keyValueRegex = /(\w+)=("[^"]+"|'[^']+'|[^"\s\[\{][^\s,]*(?:,[^\s,]*)*)/g;
    while ((match = keyValueRegex.exec(argsText)) !== null) {
      if (toolArgs.hasOwnProperty(match[1])) continue;

      const [_, key, rawValue] = match;
      let value = rawValue.replace(/^["']|["']$/g, '');
      value = this.processSpecialParameters(key, value);
      toolArgs[key] = this.convertValueType(value);
    }

    return toolArgs;
  }

  private processSpecialParameters(key: string, value: string): any {
    if (key === 'file_paths' || key.includes('path') || key.includes('file')) {
      if (value.includes(',')) {
        const paths = value.split(',')
          .map(path => path.trim())
          .filter(path => path.length > 0)
          .map(path => path.replace(/\\/g, '\\\\'));
        return paths;
      } else if (value.includes('\\')) {
        return value.replace(/\\/g, '\\\\');
      }
    }
    return value;
  }

  private convertValueType(value: any): any {
    if (value.toLowerCase === 'true') return true;
    if (value.toLowerCase === 'false') return false;
    if (!isNaN(Number(value)) && value.trim() !== '') return Number(value);
    return value;
  }

  private async sendRequest(url: string, toolName: string, toolArgs: any, command: string): Promise<any> {
    try {
      const response = await this.tryExecuteMethod(url, toolName, toolArgs);
      return response;
    } catch (error) {
      return await this.tryFallbackMethods(url, command, error);
    }
  }

  private async tryExecuteMethod(url: string, toolName: string, toolArgs: any): Promise<any> {
    const fetchResponse = await fetch(url, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        jsonrpc: '2.0',
        method: 'execute',
        params: { tool: toolName, params: toolArgs },
        id: Date.now()
      })
    });

    if (!fetchResponse.ok) {
      throw new Error(`HTTP error! Status: ${fetchResponse.status}`);
    }

    return await fetchResponse.json();
  }

  private async tryFallbackMethods(url: string, command: string, error: Error): Promise<any> {
    console.error("execute method failed:", error);

    try {
      const initResponse = await this.tryInitializeMethod(url);
      return await this.tryProcessMethod(url, command, initResponse);
    } catch (initError) {
      console.error("Initialization query also failed:", initError);
      throw error;
    }
  }

  private async tryInitializeMethod(url: string): Promise<any> {
    const initResponse = await fetch(url, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        jsonrpc: '2.0',
        method: 'initialize',
        params: {},
        id: Date.now()
      })
    });

    if (initResponse.ok) {
      return await initResponse.json();
    }
    throw new Error("Initialize method failed");
  }

  private async tryProcessMethod(url: string, command: string, initData: any): Promise<any> {
    const processResponse = await fetch(url, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        jsonrpc: '2.0',
        method: 'mcp.process',
        params: { query: command },
        id: Date.now()
      })
    });

    if (processResponse.ok) {
      return await processResponse.json();
    } else {
      return {
        result: `MCP server information: ${JSON.stringify(initData.result || initData, null, 2)}\n\nPlease check if the server implementation correctly exposes the tools.`
      };
    }
  }

  private updateAIMessage(response: any): void {
    const lastMessageIndex = this.messageHistoryRef.current.length - 1;

    if (response.error) {
      throw new Error(`MCP server error ${response.error.code}: ${response.error.message}`);
    }

    if (response.result !== undefined) {
      const resultText = this.extractResultText(response.result);
      this.messageHistoryRef.current[lastMessageIndex].content = resultText;
    } else {
      this.messageHistoryRef.current[lastMessageIndex].content =
        `Received MCP server response, but format unknown: ${JSON.stringify(response, null, 2)}`;
    }
  }

  private extractResultText(result: any): string {
    if (typeof result === 'string') {
      return result;
    }

    if (Array.isArray(result)) {
      try {
        const textItems = result
          .filter(item => item.type === 'text' && item.text)
          .map(item => item.text);

        if (textItems.length > 0) {
          return textItems.join('\n');
        }
      } catch (e) {
        console.error("Failed to parse result array:", e);
      }
    }

    return JSON.stringify(result, null, 2);
  }

  private handleError(error: any): void {
    console.error("MCP connection error:", error);
    const lastMessageIndex = this.messageHistoryRef.current.length - 1;
    this.messageHistoryRef.current[lastMessageIndex].content =
      `MCP connection error: ${error instanceof Error ? error.message : String(error)}`;
  }
}

// React Hook utilities
export function useMCPHandler(
  messageHistoryRef: React.MutableRefObject<ChatBotHistoryItem[]>,
  setState: (state: any) => void,
  props: ChatInputPanelProps
) {
  const { servers, getClient } = useMCP();

  const handler = React.useMemo(() => new MCPHandler({
    servers,
    getClient,
    messageHistoryRef,
    setState,
    props
  }), [servers, getClient, messageHistoryRef, setState, props]);

  return handler;
}