import { StatusIndicator } from "@cloudscape-design/components";
import { OptionsHelper } from "../../common/helpers/options-helper";
import { useSelector } from "react-redux";
import React, { useState, useEffect } from "react";
import styles from "../../styles/chat.module.scss";

export interface ModelNotificationProps {
    selectedModel: any;
    duration?: number;
}

const ModelNotification: React.FC<ModelNotificationProps> = ({
                                                                 selectedModel,
                                                                 duration = 3000
                                                             }) => {
    const userRoles = useSelector((state: any) => state?.rootReducer?.chatReducer?.userRoles || []);
    const [showNotification, setShowNotification] = useState<boolean>(false);
    const [displayedModelId, setDisplayedModelId] = useState<string | null>(null);

    useEffect(() => {
        let timeoutId: NodeJS.Timeout | null = null;

        if (selectedModel?.value && selectedModel.value !== displayedModelId) {
            setDisplayedModelId(selectedModel.value);
            setShowNotification(true);
            timeoutId = setTimeout(() => {
                setShowNotification(false);
            }, duration);
        }

        // 清理函数
        return () => {
            if (timeoutId) {
                clearTimeout(timeoutId);
            }
        };
    }, [selectedModel?.value, duration]);

    if (!showNotification || !selectedModel) {
        return null;
    }

    const { name, provider } = OptionsHelper.parseValue(selectedModel.value);
    const isModelOverridden = !userRoles.includes("OWNER") && provider === "bedrock";
    const displayModelName = isModelOverridden
        ? selectedModel.label
        : selectedModel.label;

    return (
        <div className={styles.model_notification}>
            <div className={styles.model_notification_content}>
                <StatusIndicator type="info">
                    Model: {displayModelName.split(":")[0]}
                    {isModelOverridden && " (Default Model)"}
                </StatusIndicator>
            </div>
        </div>
    );
};

export default ModelNotification;