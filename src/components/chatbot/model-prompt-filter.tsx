import { PromptsHelper } from "@/common/helpers/prompts-helper";
import { Utils } from "@/common/utils";
import promptService from "@/services/prompt.service";

// ModelPromptFilter.ts
export class ModelPromptFilter {
  static readonly MODEL_TYPES = {
    //add other models in future
    LLAMA3: 'llama3',
    FLANT5: 'flant5',
    CLAUDE: 'claude',
    GPT4: 'gpt4',
    OTHER: 'other'
  } as const;

  /**
   * Determines the type of model based on its name
   * @param modelName - The name of the model
   * @returns The type of the model from MODEL_TYPES
   */
  static getModelType(modelName: string): string {
    const nameLower = modelName.toLowerCase();
    
    // Get all model types except 'OTHER'
    const modelTypes = Object.entries(this.MODEL_TYPES)
      .filter(([key]) => key !== 'OTHER');
    
    // Find the first matching model type
    const matchedType = modelTypes.find(([_, value]) => 
      nameLower.includes(value)
    );

    // Return the matched type or OTHER if no match found
    return matchedType ? matchedType[1] : this.MODEL_TYPES.OTHER;
  }

  /**
   * Filters prompts based on the selected model
   * @param prompts - Array of prompts to filter
   * @param selectedModel - The selected model object
   * @returns Filtered array of prompts
   */
  static filterPromptsByModel(prompts: any[], selectedModel: any): any[] {
    if (!selectedModel) return prompts;

    const modelType = this.getModelType(selectedModel.label);
    
    return prompts.filter(prompt => {
      const promptTags = prompt.tags ? prompt.tags.toLowerCase() : '';
      return promptTags.includes(modelType);
    });
  }

  /**
   * Filters prompts based on workspace tag
   * @param prompts - Array of prompts to filter
   * @param selectedWorkspace - The selected workspace object
   * @returns Filtered array of prompts
   */
  static filterPromptsByWorkspace(prompts: any[], selectedWorkspace: any): any[] {
    if (!selectedWorkspace || !selectedWorkspace.value) return prompts;
    return prompts.filter(prompt => {
      const promptTags = prompt.tags ? prompt.tags.toLowerCase() : '';

      if( promptTags.includes(selectedWorkspace.label.toLowerCase())){
        return prompt
      }
    });
  }

  /**
   * Filters prompts based on both workspace and model
   * @param prompts - Array of prompts to filter
   * @param selectedWorkspace - The selected workspace object
   * @param selectedModel - The selected model object
   * @returns Filtered array of prompts
   */
  static filterPrompts(prompts: any[], selectedModel: any): any[] {
    let filteredPrompts = prompts;

    // Filter by model if selected
    if (selectedModel) {
      filteredPrompts = this.filterPromptsByModel(filteredPrompts, selectedModel);
    }

    return filteredPrompts;
  }
}
