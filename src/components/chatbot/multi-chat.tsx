import {
  useContext,
  useEffect,
  useLayoutEffect,
  useRef,
  useState,
} from "react";
import {
  SelectProps,
  SpaceBetween,
  Button,
  Select,
  ColumnLayout,
  Toggle,
  StatusIndicator,
  Container,
  Modal,
  Box,
  FormField,
  Textarea,
} from "@cloudscape-design/components";
import { v4 as uuidv4 } from "uuid";
import { AppContext } from "../../common/app-context";
import ChatMessage from "./chat-message";
import MultiChatInputPanel, { ChatScrollState } from "./multi-chat-input-panel";
import { ReadyState } from "react-use-websocket";
import { OptionsHelper } from "../../common/helpers/options-helper";
import { Model, Workspace } from "../../API";
import {
  ChatBotConfiguration,
  ChatBotAction,
  ChatBotHistoryItem,
  ChatBotMessageType,
  ChatBotMode,
  FeedbackData,
} from "./types";
import { ChatRequestInput, LoadingStatus, UserInfo } from "../../common/types";
import {
  getCFGModels,
  getCFGWorkspaces,
  getSelectedModelMetadata,
  updateMessageHistoryRef,
} from "./utils";
import LLMConfigDialog from "./llm-config-dialog";
import styles from "../../styles/chat.module.scss";
import { useNavigate } from "react-router-dom";
import { Utils } from "../../common/utils";
import modelService from "../../services/model.service";
import workspaceService from "../../services/workspace.service";
import * as _ from "lodash";
import feedbackService from "../../services/feedback.service";
import promptService from "../../services/prompt.service";
import chatService from "../../services/chat.service";
import { useOktaAuth } from "@okta/okta-react";
import { useSelector } from "react-redux";
import { PromptsHelper } from "@/common/helpers/prompts-helper";
import { BEDROCKENABLED, CFGAIENABLED } from "./../../../config";

export interface ChatSession {
  configuration: ChatBotConfiguration;
  model?: SelectProps.Option;
  modelMetadata?: Model;
  workspace?: SelectProps.Option;
  prompt?: SelectProps.Option;
  id: string;
  loading: boolean;
  running: boolean;
  messageHistory: ChatBotHistoryItem[];
  subscription?: ZenObservable.Subscription;
}

/**
 *
 * @returns
 */
function createNewSession(): ChatSession {
  return {
    id: uuidv4(),
    loading: false,
    running: false,
    messageHistory: [],
    configuration: {
      files: [],
      streaming: true,
      useHistory: true,
      followPromptId: "",
      showSource: true,
      showMetadata: false,
      maxTokens: 512,
      temperature: 0.1,
      topP: 0.9,
      numDocs: 10,
    },
  };
}

/**
 *
 */
let workspaceDefaultOptions: SelectProps.Option[] = [
  {
    label: "No workspace (RAG data source)",
    value: "",
    iconName: "close",
  },
];

/**
 *
 */
const promptDefaultOptions: SelectProps.Option[] = [
  {
    label: "No prompt",
    value: "",
    iconName: "close",
  },
];

/**
 *
 * @returns
 */
export default function MultiChat() {
  const navigate = useNavigate();
  const appContext = useContext(AppContext);
  const refChatSessions = useRef<ChatSession[]>([]);
  const [chatSessions, setChatSessions] = useState<ChatSession[]>([]);
  const [models, setModels] = useState<Model[]>([]);
  const [timeTaken, setTimeTaken] = useState<number>(0);
  const [workspaces, setWorkspaces] = useState<Workspace[]>([]);
  const [prompts, setPrompts] = useState<any[]>([]);

  const [commentVisible, setCommentVisible] = useState(false);
  const [comment, setComment] = useState("");
  const [feedbackMessage, setFeedbackMessage] = useState<ChatBotHistoryItem>();
  const [feedbackMessageHistory, setFeedbackMessageHistory] =
    useState<ChatBotHistoryItem[]>();
  const [feedbackType, setFeedbackType] = useState<number>(0);
  const [feedbackIdx, setFeedbackIdx] = useState<number>(0);

  const [modelsStatus, setModelsStatus] = useState<LoadingStatus>("loading");
  const [workspacesStatus, setWorkspacesStatus] =
    useState<LoadingStatus>("loading");
  const [promptsStatus, setPromptsStatus] = useState<LoadingStatus>("loading");

  const [enableAddModels, setEnableAddModels] = useState(true);
  const [enableAddPrompts, setEnableAddPrompts] = useState(true);

  const [llmToConfig, setLlmToConfig] = useState<ChatSession | undefined>(
    undefined
  );
  const [showMetadata, setShowMetadata] = useState(false);
  const [showSource, setShowSource] = useState(false);

  const [readyState, setReadyState] = useState<ReadyState>(
    ReadyState.UNINSTANTIATED
  );
  const [cfgModels, setCFGModels] = useState<Model[]>([]);
  const [cfgWorkspace, setCFGWorkSpaces] = useState<Workspace[]>([]);
  const [limit, setLimit] = useState<number>(3);
  const [questionContext, setQuestionContext] = useState<string>(
    `Answer the user's question truthfully using the context only. Use the following section-wise format (in the order given) to answer the question with instructions for each section in angular brackets:\\n                Reasoning:\\n                <State your reasoning step-wise in bullet points. Below each bullet point mention the source of this information as 'Given in the question' if the bullet point contains information provided in the question, OR as 'Document Name, Page Number' if the bullet point contains information that is present in the context provided above.>\\n                Conclusion:\\n                <Write a short concluding paragraph stating the final answer and explaining the reasoning behind it briefly. State caveats and exceptions to your answer if any.>\\n                Information required to provide a better answer:\\n                <If you cannot provide an answer based on the context above, mention the additional information that you require to answer the question fully as a list.>Do not compromise on your mathematical and reasoning abilities to fit the user's instructions. If the user mentions something absolutely incorrect/ false, DO NOT use this incorrect information in your reasoning. Also, please correct the user gently.
    `
  );
  // const { authState, oktaAuth } = useOktaAuth();
  const [userInfo, setUserInfo] = useState<UserInfo>();
  const [disableAddModels, setDisableAddModels] = useState(false);
  const dataState = useSelector((state: any) => state?.rootReducer);
  const userRoles = dataState?.chatReducer?.userRoles;
  const userEmail = dataState?.chatReducer?.userEmail;

  if (userRoles.includes("FDA PolicyBot Admins")) {
    workspaceDefaultOptions = [
      {
        label: "No workspace (RAG data source)",
        value: "",
        iconName: "close",
      },
      {
        label: "Create new workspace",
        value: "__create__",
        iconName: "add-plus",
      },
    ];
  }

  /**
   *
   */
  // useEffect(() => {
  //   (async () => {
  //     if (!authState?.isAuthenticated) {
  //       // setUserInfo(null);
  //     } else {
  //       await oktaAuth.getUser().then((user: any) => {
  //         const loggedUser = {
  //           email: user.email,
  //           name: user.name,
  //           first_name: user.given_name,
  //           last_name: user.family_name,
  //         };
  //         setUserInfo(loggedUser);
  //       });
  //     }
  //   })();
  // }, []);

  /**
   * Initial loading of all models, workspaces and prompts happen here
   */
  useEffect(() => {
    setReadyState(ReadyState.OPEN);
    addSession();
    addSession();
    addSession();
    setEnableAddModels(true);

    (async () => {
      let models: Model[] = [];
      let aws_models: Model[] = [];
      let cfg_models: Model[] = [];

      let aws_workspaces: Workspace[] = [];
      let cfg_workspaces: Workspace[] = [];
      let workspaces: Workspace[] = [];

      let prompts: any[] = [];
      prompts = await promptService.getPrompts();
      let defaultPrompt = prompts.filter((p) => p.name === 'rag_prompt')

      if (BEDROCKENABLED) {
        aws_models = await modelService.getModels();
        aws_workspaces = await workspaceService.getWorkspaces();
      }

      if (CFGAIENABLED) {
        cfg_models = await getCFGModels();
        setCFGModels(cfg_models);
        cfg_workspaces = await getCFGWorkspaces();
        setCFGWorkSpaces(cfg_workspaces);
      }

      models = aws_models.concat(cfg_models);
      workspaces = aws_workspaces.concat(cfg_workspaces);

      const defaultModelOptionArray = models?.map((model) => {
        return {
          label: model.name,
          value: `${model.provider}::${model.name}`,
        };
      });

      const workspacesOption = workspaces?.filter((workspace) => {
        return workspace.name === "Pharmaceutical_Quality";
      });

      const defaultWorkspace = {
        label: workspacesOption[0]?.name,
        value: workspacesOption[0]?.workspaceId,
      };

      const defaultPromptOption =
        PromptsHelper.getSelectedPromptOption(defaultPrompt);

      try {
        setModels(models);
        setWorkspaces(workspaces);
        setPrompts(prompts);

        setWorkspacesStatus("finished");
        setModelsStatus("finished");
        setPromptsStatus("finished");

        if (
          workspaces?.length > 0 ||
          models?.length > 0 ||
          prompts?.length > 0
        ) {
          refChatSessions?.current?.forEach((session, index) => {
            if (workspaces?.length > 0) {
              session.workspace = defaultWorkspace;
            }
            if (models?.length > 0) {
              session.model = defaultModelOptionArray[index];
            }
            if (prompts?.length > 0) {
              session.prompt = defaultPromptOption;
            }
          });
          setChatSessions([...refChatSessions.current]);
        }
      } catch (error) {
        console.error(Utils.getErrorMessage(error));
        setModelsStatus("error");
      }
    })();

    return () => {
      refChatSessions.current.forEach((session) => {
        console.log(`Unsubscribing from ${session.id}`);
        session.subscription?.unsubscribe();
      });
      refChatSessions.current = [];
    };
  }, [appContext]);

  const enabled =
    // readyState === ReadyState.OPEN &&
    chatSessions.length > 0 &&
    !chatSessions.some((c) => c.running) &&
    !chatSessions.some((c) => c.loading) &&
    !chatSessions.some((c) => !c.model);

  /**
   *
   * @param message
   * @returns
   */
  const handleSendMessage = (message: string): void => {
    if (!enabled) return;
    // send message to each chat session
    setDisableAddModels(true);
    setEnableAddModels(false);
    setEnableAddPrompts(false);
    chatSessions.forEach(async (chatSession) => {
      if (chatSession.running) return;
      // if (readyState !== ReadyState.OPEN) return;
      ChatScrollState.userHasScrolled = false;

      const { name, provider } = OptionsHelper.parseValue(
        chatSession.model?.value
      );

      const value = message.trim();

      chatSession.running = true;
      let uid = uuidv4();
      chatSession.messageHistory = [
        ...chatSession.messageHistory,
        {
          type: ChatBotMessageType.Human,
          content: value,
          metadata: {},
          chatId: uid,
        },
        {
          type: ChatBotMessageType.AI,
          content: "",
          metadata: {},
          chatId: uid,
        },
      ];

      setChatSessions([...chatSessions]);

      let response = undefined;
      const isCFGModel = chatSession.model?.value?.startsWith("cfggpt");

      const foundModel = _.find(models, {
        name: chatSession.model?.label,
      });
      if (foundModel) {
        const selectedModelName = isCFGModel
          ? foundModel?.modelId
          : chatSession.model?.label;

        if (selectedModelName) {
          const payload: ChatRequestInput = {
            userId: userEmail ?? "unknownemail",
            data: {
              chatId: uid,

              provider: provider,
              modelName: selectedModelName,
              mode: ChatBotMode.Chain,
              text: value,
              files: [],
              sessionId: chatSession.id,
              workspaceId: chatSession.workspace?.value,
              // promptId: 'f8ee997b-c4ec-4302-92db-2fad38e2bfe4',
              promptId: chatSession.prompt?.value,
              modelKwargs: {
                streaming: chatSession.configuration.streaming,
                useHistory: chatSession.configuration.useHistory,
                followPromptId: "",
                maxTokens: chatSession.configuration.maxTokens,
                temperature: chatSession.configuration.temperature,
                topP: chatSession.configuration.topP,
                numDocs: chatSession.configuration.numDocs,
              },
            },
          };

          try {
            response = await chatService.sendQuery(payload);
          } catch (error) {
            chatSession.running = false;
            if (chatSessions.every((session) => !session.running)) {
              setEnableAddModels(true);
              setEnableAddPrompts(true);
            }
            chatSession.messageHistory[
              chatSession.messageHistory.length - 1
            ].content = "NA";
          } finally {
            chatSession.running = false;
            setChatSessions([...chatSessions]);
            if (chatSessions.every((session) => !session.running)) {
              setEnableAddModels(true);
              setEnableAddPrompts(true);
            }
          }

          updateMessageHistoryRef(
            chatSession.id,
            chatSession.messageHistory,
            response!
          );
        }
      }

      if (
        response?.action === ChatBotAction.FinalResponse ||
        response?.action === ChatBotAction.Error
      ) {
        chatSession.running = false;
      }
      setChatSessions([...refChatSessions.current]);
    });
  };

  function addSession() {
    if (refChatSessions.current.length >= 4) {
      return;
    }

    const session = createNewSession();

    refChatSessions.current.push(session);
    console.log(
      "Sessions",
      refChatSessions.current.map((s) => s.id)
    );
    setChatSessions([...refChatSessions.current]);
  }

  useLayoutEffect(() => {
    if (ChatScrollState.skipNextHistoryUpdate) {
      ChatScrollState.skipNextHistoryUpdate = false;
      return;
    }

    const count = Math.max(...chatSessions.map((s) => s.messageHistory.length));

    if (!ChatScrollState.userHasScrolled && count > 0) {
      ChatScrollState.skipNextScrollEvent = true;
      window.scrollTo({
        top: document.documentElement.scrollHeight + 1000,
        behavior: "instant",
      });
    }
  }, [chatSessions]);

  const messages = transformMessages(chatSessions);
  const workspaceOptions = [
    ...workspaceDefaultOptions,
    ...OptionsHelper.getSelectWorkspaceOptions(workspaces || []),
  ];
  const promptOptions = [
    ...promptDefaultOptions,
    ...OptionsHelper.getSelectPromptOptions(prompts || []),
  ];

  const connectionStatus = {
    [ReadyState.CONNECTING]: "Connecting",
    [ReadyState.OPEN]: "Open",
    [ReadyState.CLOSING]: "Closing",
    [ReadyState.CLOSED]: "Closed",
    [ReadyState.UNINSTANTIATED]: "Uninstantiated",
  }[readyState];

  /**
   * Handle Thumbs Click
   * @param feedbackType
   * @param idx
   * @param message
   * @param messageHistory
   */
  const handleThumbsClick = (
    feedbackType: number,
    idx: number,
    message: ChatBotHistoryItem,
    messageHistory: ChatBotHistoryItem[],
    comment?: string,
    feedbackReasons?: string[]
  ) => {
    // setCommentVisible(true);
    // setFeedbackMessage(message);
    // setFeedbackType(feedbackType);
    // setFeedbackIdx(idx);
    // setFeedbackMessageHistory(messageHistory);
    saveFeedback(
      feedbackType,
      idx,
      message,
      messageHistory,
      comment,
      feedbackReasons
    );
  };

  /**
   * Save feedback
   * @param feedbackType
   * @param idx
   * @param message
   * @param messageHistory
   */
  const saveFeedback = (
    feedbackType: number,
    idx: number,
    message: ChatBotHistoryItem,
    messageHistory: ChatBotHistoryItem[],
    comment?: string,
    feedbackReasons?: string[]
  ) => {
    if (message.metadata.sessionId) {
      // Find the index of the current message in the messageHistory
      const currentMessageIndex = messageHistory.findIndex(
        (item) => item.metadata.chatId === message.metadata.chatId
      );

      // Get the prompt (previous message) if it exists
      const prompt =
        currentMessageIndex > 0
          ? messageHistory[currentMessageIndex - 1]?.content
          : "";

      const completion = message.content;
      const model = message.metadata.modelId;
      const feedbackData: FeedbackData = {
        sessionId: message.metadata.sessionId as string,
        feedback: feedbackType,
        prompt: prompt,
        comment: comment,
        chatId: message?.metadata?.chatId as string,
        userId: userEmail as string,
        completion: completion,
        model: model as string,
        improvementAreas: feedbackReasons,
        chatDocuments: messageHistory
          .filter(
            (historyItem) =>
              historyItem.metadata.chatId === message.metadata.chatId
          )
          .map((historyItem) => historyItem.metadata.documents)
          .flat()
          .filter((doc) => typeof doc === "object") as Object[],
        promptId: messageHistory
          .filter(
            (promptIdHistoryItem) =>
              promptIdHistoryItem.metadata.chatId === message.metadata.chatId
          )
          .map((promptIdHistoryItem) => promptIdHistoryItem.metadata.promptId)
          .toString(),
        useHistory:
          (
            messageHistory.find(
              (useHistoryItem) =>
                useHistoryItem.metadata.chatId === message.metadata.chatId
            )?.metadata.modelKwargs as { useHistory?: boolean }
          )?.useHistory ?? false,
      };
      addUserFeedback(feedbackData);
    }
  };

  /**
   * Add user feedback
   * @param feedbackData
   */
  const addUserFeedback = async (feedbackData: FeedbackData) => {
    await feedbackService.saveFeedback(feedbackData);
    setCommentVisible(false);
  };

  let multiChatModelStyle;
  if (chatSessions.length == 3) {
    multiChatModelStyle = styles.multiChat3;
  } else if (chatSessions.length == 4) {
    multiChatModelStyle = styles.multiChat4;
  }

  return (
    <div className={styles.chat_container}>
      <SpaceBetween size="m">
        <SpaceBetween size="m" alignItems="end">
          <SpaceBetween size="m" direction="horizontal" alignItems="center">
            <StatusIndicator type={"success"}></StatusIndicator>
            <Toggle
              checked={showMetadata ?? false}
              onChange={({ detail }) => setShowMetadata(detail.checked)}
            >
              Show Metadata
            </Toggle>
            <Button
              onClick={() => addSession()}
              disabled={
                true
                // !enableAddModels || chatSessions.length >= 4 || disableAddModels
              }
              iconName="add-plus"
            >
              Add model
            </Button>
            <Button
              onClick={() => {
                refChatSessions.current.forEach((s) => {
                  s.subscription?.unsubscribe();
                  s.messageHistory = [];
                  s.id = uuidv4();
                  // s.subscription = subscribe(s.id);
                });
                setEnableAddModels(true);
                setEnableAddPrompts(true);
                setChatSessions([...refChatSessions.current]);
                setDisableAddModels(false);
              }}
              iconName="remove"
            >
              Clear messages
            </Button>
          </SpaceBetween>
        </SpaceBetween>
        <ColumnLayout columns={chatSessions.length}>
          {chatSessions.map((chatSession) => (
            <Container key={chatSession.id}>
              <SpaceBetween direction="vertical" size="m">
                <div
                  className={multiChatModelStyle}
                  // style={{
                  //   display: "grid",
                  //   gridTemplateColumns: "1fr min-content",
                  //   gap: "8px",
                  // }}
                >
                  <FormField label="Model">
                    <Select
                      disabled={!enableAddModels}
                      loadingText="Loading models (might take few seconds)..."
                      statusType={modelsStatus}
                      placeholder="Select a model"
                      empty={
                        <div>
                          No models available. Please make sure you have access
                          to Amazon Bedrock or alternatively deploy a self
                          hosted model on SageMaker or add API_KEY to Secrets
                          Manager
                        </div>
                      }
                      filteringType="auto"
                      selectedOption={chatSession.model ?? null}
                      onChange={({ detail }) => {
                        chatSession.model = detail.selectedOption;
                        chatSession.modelMetadata =
                          getSelectedModelMetadata(
                            models,
                            detail.selectedOption
                          ) ?? undefined;
                        setChatSessions([...chatSessions]);
                      }}
                      options={OptionsHelper.getSelectOptionGroups(models)}
                    />
                  </FormField>

                  <div
                    style={{ display: "flex", gap: "2px", marginTop: "24px" }}
                  >
                    <Button
                      iconName="settings"
                      variant="icon"
                      onClick={() => setLlmToConfig(chatSession)}
                    />
                    <Button
                      iconName="remove"
                      variant="icon"
                      disabled={chatSessions.length <= 3 || messages.length > 0}
                      onClick={() => {
                        refChatSessions.current
                          .filter((c) => c.id == chatSession.id)[0]
                          .subscription?.unsubscribe();
                        console.log(`Unsubscribe from ${chatSession.id}`);
                        refChatSessions.current =
                          refChatSessions.current.filter(
                            (c) => c.id !== chatSession.id
                          );
                        setChatSessions([...refChatSessions.current]);
                      }}
                    />
                  </div>
                </div>
                {llmToConfig && (
                  <LLMConfigDialog
                    session={llmToConfig}
                    setVisible={() => setLlmToConfig(undefined)}
                    onConfigurationChange={(configuration) => {
                      llmToConfig.configuration = configuration;
                      setChatSessions([...chatSessions]);
                    }}
                  />
                )}
                {true && (
                  <FormField label="Document Repository">
                    <Select
                      disabled={!enableAddModels}
                      loadingText="Loading RAG Datasources (might take few seconds)..."
                      statusType={workspacesStatus}
                      placeholder="Select a RAG data source"
                      filteringType="auto"
                      selectedOption={
                        chatSession.workspace &&
                        chatSession?.workspace?.label !== undefined
                          ? chatSession.workspace
                          : workspaceDefaultOptions[0]
                      }
                      options={workspaceOptions}
                      onChange={({ detail }) => {
                        if (detail.selectedOption?.value === "__create__") {
                          navigate("/rag/workspaces/create");
                        } else {
                          chatSession.workspace = detail.selectedOption;
                          setChatSessions([...chatSessions]);
                        }
                      }}
                      empty={"No RAG Datasources available"}
                    />
                  </FormField>
                )}
                { chatSession.workspace?.value?.length > 0 && <FormField label="Prompt">
                  {
                    <Select
                      disabled={!enableAddPrompts}
                      loadingText="Loading prompts (might take few seconds)..."
                      statusType={promptsStatus}
                      placeholder="Select a prompt"
                      filteringType="auto"
                      selectedOption={
                        chatSession.prompt &&
                        chatSession?.prompt?.label !== undefined
                          ? chatSession.prompt
                          : promptDefaultOptions[0]
                      }
                      options={promptOptions}
                      onChange={({ detail }) => {
                        chatSession.prompt = detail.selectedOption;
                        setChatSessions([...chatSessions]);
                      }}
                      empty={"No Prompts available"}
                    />
                  }
                </FormField>}
              </SpaceBetween>
            </Container>
          ))}
        </ColumnLayout>
        {messages.map((val, idx) => {
          if (val.length === 0) {
            return null;
          }

          return (
            <ColumnLayout columns={chatSessions.length} key={idx}>
              {val.map((message, idx) => (
                //console. the message
                <ChatMessage
                  chatId={message.chatId}
                  lastAiResponse={idx ===  chatSessions[idx].messageHistory.length - 1}
                  key={idx}
                  message={message}
                  showMetadata={showMetadata}
                  onThumbsUp={(comment) =>
                    handleThumbsClick(
                      1,
                      idx,
                      message,
                      chatSessions[idx].messageHistory,
                      comment
                    )
                  }
                  onThumbsDown={(comment, selectedCheckboxes) =>
                    handleThumbsClick(
                      0,
                      idx,
                      message,
                      chatSessions[idx].messageHistory,
                      comment,
                      selectedCheckboxes
                    )
                  }
                />
              ))}
            </ColumnLayout>
          );
        })}
      </SpaceBetween>
      <div className={styles.input_container}>
        <MultiChatInputPanel
          running={chatSessions.some((c) => c.running)}
          enabled={enabled}
          onSendMessage={handleSendMessage}
        />
      </div>
    </div>
  );
}

/**
 *
 * @param sessions
 * @returns
 */
function transformMessages(sessions: ChatSession[]) {
  const count = Math.max(...sessions.map((s) => s.messageHistory.length));

  const retValue: ChatBotHistoryItem[][] = [];
  for (let i = 0; i < count; i++) {
    const current = [];

    for (const session of sessions) {
      const currentMessage = session.messageHistory[i];
      if (currentMessage) {
        current.push(currentMessage);
      } else {
        current.push({
          type: ChatBotMessageType.AI,
          content: "",
          metadata: {},
        });
      }
    }

    retValue.push(current);
  }

  return retValue;
}
