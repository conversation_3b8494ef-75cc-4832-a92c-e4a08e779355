export const requiredOption = {
    label: "1.0 General Terms",
    value:
        `Generate a comprehensive Data Use Agreement (DUA) template for data sharing between [PARTY A] and [PARTY B].
This template must strictly follow the structure and contents outlined below.
Ensure every subsection is generated with clear section numbering, formal legal tone, and all placeholders in [BRACKETS] for easy customization.
Ensure the final section includes a full table capturing dataset change tracking as specified.
Template Structure Title Page Include a title that clearly states this is a data use agreement Identify the organizations entering into the agreement
Name the specific data or systems involved Include version number and date Add logo placeholder for the originating organization Version History Table
Include a version history table with the following columns: Version Number Written By Revision Date Approved By Approval Date Description of Change
Table of Contents Generate a comprehensive Table of Contents covering all sections below.
IMPORTANT: If we choose to have the addendum, it must be included at the end of the document as a table and should be clearly labeled and properly formatted: Appendix: Data Change Tracking Table
| Date of Data Change Request (MM/DD/YYYY) | Target Date of Completion (MM/DD/YYYY) | Data Change Requestor(s) | Brief Description of Data Change Request | Reason for Data Change Request | Special Conditions/Stipulations, if any | [PARTY A] Signature | [PARTY B] Signature | Formatting Guidelines
Use clear section numbering (e.g., 1.1, 1.2, etc.)
Use [BRACKETS] for all placeholders
Use table format for contact info and signature blocks
Maintain professional formatting (spacing, headers/footers, page breaks)
Apply precise and legally appropriate language
Ensure compliance with federal data security standards
Document should be suitable for intra-agency or inter-agency use
ADD "This DUA will be due for an annual review on MM/DD/YYYY" to the bottom of the response 
ONLY use below selected sections`
// please response with markdown format without starting with \`\`\`markdown`
};


export const allOptions = [
    {
        label: "1.0 Agreement",
        options: [
            {
                label: "1.0 General Terms",
                value: requiredOption.value,
                disabled: true
            },
            {
                label: "1.1 Parties to Agreement",
                value: "Language identifying the organizations and individuals entering the agreement, including full legal names and a table for contact information.",
            },
            {
                label: "1.2 Period of Agreement",
                value: "Define effective dates, renewal terms, and notification procedures.",
            },
            {
                label: "1.3 Scope of Agreement",
                value: "Describe the high-level purpose, involved systems, and types of data. Use [BRACKETS] for placeholders.",
            },
            {
                label: "1.4 Authority to Share Data (as applicable)",
                value: "Cite relevant legal or regulatory basis, with placeholders for jurisdiction-specific laws.",
            },
            {
                label: "1.5 Modification",
                value: "Describe how changes must be proposed, documented, and agreed upon.",
            },
            {
                label: "1.6 Termination",
                value: "Outline termination processes for both cause and no cause, with data return/destruction procedures.",
            },
            {
                label: "1.7 Violation of Terms",
                value: "Specify reporting timelines, processes, and potential consequences.",
            },
            {
                label: "1.8 Indemnification",
                value: "Assign responsibility for claims, subject to federal/state law.",
            },
            {
                label: "1.9 Acknowledgements",
                value: "Include affirmation of authority, understanding, and compliance.",
            },
            {
                label: "1.10 Roles and Responsibilities",
                value: "Detail key roles and contacts (e.g., project officer, data custodian), with placeholders in a table format.",
            },
            {
                label: "1.11 Funding",
                value: "Outline any financial terms or payments, with [BRACKETS] for dollar amounts.",
            },
            {
                label: "1.12 Other",
                value: "Include clauses on: assignment, dispute resolution, captions, choice of law, costs and damages, counterparts, entire agreement, flow-down, order of precedence, independent entities, severability, survival, prohibition on third party beneficiaries, public availability."
            }
        ]
    },
    {
        label: "2.0 Purpose/Use Case",
        options: [
            {
                label: "2.0 Purpose/Use Case",
                value: "Clearly define the context, objectives, analytic methods, and expected outcomes.",
            }
        ]
    },
    {
        label: "3.0 Scope of Data",
        options: [
            {
                label: "3.1 Data Description",
                value: "High-level overview of data, with status of PII/PHI/BII, federal program source, and reference to detailed appendix of data specifications.",
            },
            {
                label: "3.2 Data Ownership",
                value: "State ownership rights and data use limitations.",
            },
            {
                label: "3.3 Service Level",
                value: "Include: frequency, transmission methods, platforms/tools/support, data standards.",
            },
            {
                label: "3.3.2 Data Quality",
                value: "Specify: quality assurances or limitations, bias mitigation, recommended analytic methods, handling of transmission errors.",
            }
        ]
    },
    {
        label: "4.0 Data Controls/De-identification/Sharing",
        options: [
            {
                label: "4.1 Disclosure and Use",
                value: "Cover: purpose limitations, consent, legal compliance, role-based access, user logs, notification for compelled disclosure, IRB requirements.",
            },
            {
                label: "4.2 Control of Identifiable Data",
                value: "Prohibit identifiable data sharing except under well-defined exceptions.",
            },
            {
                label: "4.3.1 Person",
                value: "standards and anti-reidentification practices.",
            },
            {
                label: "4.3.2 Organization",
                value: "similar requirements for org-level data.",
            },
            {
                label: "4.4.1 Data Linkage",
                value: "disallow unauthorized linkage.",
            },
            {
                label: "4.4.2 Data Reuse",
                value: "prohibit unauthorized reuse.",
            },
            {
                label: "4.4.3 Data Redisclosure",
                value: "prohibit redisclosure; outline exceptions (e.g., FOIA); define redisclosure authorization and violation handling.",
            },
            {
                label: "4.4.3.1 Identifiable Data as De-identified Data",
                value: "prevent publication or sharing that allows re-identification.",
            },
            {
                label: "4.5 Data Disposition",
                value: "Specify retention time, destruction method, and certification.",
            },
            {
                label: "4.6 Publication",
                value: "Define data provider approval process and attribution requirements.",
            }
        ]
    },
    {
        label: "5.0 Security",
        options: [
            {
                label: "5.1 General",
                value: "Include broad safeguard requirements.",
            },
            {
                label: "5.2 Data Access Controls",
                value: "Limit access to authorized users; include remote user policies.",
            },
            {
                label: "5.3 Network Access",
                value: "Require secure networking practices.",
            },
            {
                label: "5.4 Physical Access",
                value: "Specify facility and storage controls.",
            },
            {
                label: "5.5 Transmission and Storage",
                value: "Require encryption and other security safeguards.",
            },
            {
                label: "5.6 Authority to Operate",
                value: "Align with federal control standards.",
            },
            {
                label: "5.7 Cloud Computing",
                value: "Require FedRAMP authorization and approval.",
            },
            {
                label: "5.8 Incidents Including Reporting",
                value: "Define breaches, set reporting procedures and timelines, require investigation records and remediation.",
            },
            {
                label: "5.9 Audit",
                value: "Enable inspection rights to verify compliance.",
            }
        ]
    },
    {
        label: "6.0 Reporting Requirements",
        options: [
            {
                label: "6.0 Reporting Requirements",
                value: "Define types, frequency, and content of required reports.",
            }
        ]
    },
    {
        label: "7.0 Applicable Law and Regulations",
        options: [
            {
                label: "7.0 Applicable Law and Regulations",
                value: "List relevant laws and describe their impact on data handling.",
            }
        ]
    },
    {
        label: "8.0 Signatures",
        options: [
            {
                label: "8.0 Signatures",
                value: "Include signature blocks with: Name, Title, Organization, Date.",
            }
        ]
    },
    {
        label: "9.0 Appendices",
        options: [
            {
                label: "9.0 Appendices",
                value: "Include placeholders for: Detailed data documentation Technical specifications FedRAMP or other certifications",
            }
        ]
    },
    {
        label: "10.0 Addendum Template",
        options: [
            {
                label: "10.0 Addendum Template",
                value: "Create a structured table format to track dataset change requests.",
            }
        ]
    },
    {
        label: "DUA Tables",
        options: [
            {
                label: "DUA Details Overview Table",
                value:`Include a table named “DUA Details Overview” at the end of the Word document generated, The first column should include the following fields: DUA Number, DUA Name, Status, Access Restrictions, Owner, DUA Developer Email, DUA Developer Role/Title, Created By, Last Modified Date`,
            },
            {
                label: "DUA Point of Contact",
                value:`Include a table named “DUA Point of Contact” at the end of the Word document generated. The first column should include the following fields:Primary Contact Name, Primary Contact Organization, Primary Contact OpDiv/StaffDiv, Primary Contact Role/Title, Second Contact Name, Second Contact Role/Title, Second Contact Organization, Second Contact OpDiv/StaffDiv, Third Contact Name, Third Contact Op Div/StaffDiv, Third Contact Role/Title`,
            },
            {
                label: "DUA Receiver Details",
                value:`Include a table named “Data Receiver Details” at the end of the Word document generated. The first column should include the following fields: Data Recipient Contact Name, Data Recipient Contact Email, Data Recipient Role/Title, Data Recipient Organization`,
            },
            {
                label: "Data Sharing Details",
                value:`Include a table named “Data Sharing Details” at the end of the Word document generated. The first column should include the following fields: Primary Sharing OpDiv/StaffDiv, Primary Sharing Organization, Purpose of Data Sharing, Secondary Sharing Op Div/Staff Div, Secondary Sharing Organization, Data Sharing Type`,
            }
        ]
    }
]