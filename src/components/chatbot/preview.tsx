import React from "react";
import { RootState } from "@/redux/store/configureStore";
import { useDispatch, useSelector } from "react-redux";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>etween,
  StatusIndicator,
} from "@cloudscape-design/components";
import styles from "../../styles/chat.module.scss";

interface PreviewProps {
  // 预留接口属性，后续可扩展
}

const Preview: React.FC<PreviewProps> = () => {
  // Get user name from somewhere - you might need to adjust this based on your user state management
  const dataState = useSelector((state: RootState) => {
    return state.rootReducer;
  });
  const dispatch = useDispatch();
  const userEmail = dataState?.chatReducer?.userEmail;
  const userName = dataState?.chatReducer?.userName; // Placeholder, adjust as needed

  return (
    <div>
      <div className={styles.chat_header_container} style={{ marginTop: '-20px' }}>
        <div className={styles.chat_header}>
          <h2
            style={{
              margin: "inherit",
              fontSize: "22px",
              fontWeight: "700",
              fontFamily: "Roboto Condensed",
            }}
          >
            Generate a Data Governance Document{" "}
          </h2>
        </div>
        <hr style={{ marginTop: '3px' }} />
        {/* WELCOME MESSAGE CENTER Welcome to Data Governance Document Builder! */}
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            marginTop: '10px'
          }}
        >
          <p className={styles.chat_welcome_message}>
            Welcome to Data Governance Document Builder! {userName?.firstName}{" "}
            {userName?.lastName}
          </p>
        </div>
      </div>
      <div style={{ padding: "20px" }}>
        TBA, showing the preview of the DUA
        {/* 暂时不显示任何内容 */}
      </div>
    </div>
  );
};

export default Preview;