import {
    Box,
    Button,
    Form,
    FormField,
    Input,
    Modal,
    Select,
    SpaceBetween,
    Textarea,
} from '@cloudscape-design/components';
import { useForm } from '../../common/hooks/use-form';
import { ChatBotPromptConfiguration } from './types';
import { Dispatch, useEffect, useState } from 'react';
import React from 'react';
import promptService from '../../services/prompt.service';
import { Prompt } from '../../API';

export interface PromptDialogProps {
    sessionId: string;
    visible: boolean;
    editMode: boolean;
    setVisible: (visible: boolean) => void;
    getPrompts: () => void;
    promptConfiguration: ChatBotPromptConfiguration;
    setPromptConfiguration: Dispatch<
        React.SetStateAction<ChatBotPromptConfiguration>
    >;
}

interface PromptDialogData {
    promptId?: string;
    name: string;
    description: string;
    prompt_type: string;
    tags: string;
    created_at: string;
    prompt: string;
}

export default function PromptDialog(props: PromptDialogProps) {
    const [selectedPromptType, setSelectedPromptType] = useState<any>({});

    const { data, onChange, errors, validate } = useForm<PromptDialogData>({
        initialValue: () => {
            console.log('props.promptConfiguration');
            console.log(props.promptConfiguration);
            const retValue = {
                id: props.promptConfiguration.id,
                name: props.promptConfiguration.name,
                description: props.promptConfiguration.description,
                prompt_type: props.promptConfiguration.prompt_type,
                tags: props.promptConfiguration.tags,
                created_at: props.promptConfiguration.created_at,
                prompt: props.promptConfiguration.prompt,
            };

            return retValue;
        },
        validate: () => {
            const errors: Record<string, string | string[]> = {};

            return errors;
        },
    });

    const savePrompt = async () => {
        if (!validate()) return;

        props.setPromptConfiguration({
            ...props.promptConfiguration,
            ...data,
        });
        console.log("is edit mode:", props.editMode)
        console.log("data:",data)
        console.log("created_at:", data.created_at)
        console.log("prompt", props.promptConfiguration.prompt)
        if(props.editMode) {
            const payload = {
                id: data.promptId,                
                name: data.name,
                description: data.description,
                prompt_type: data.prompt_type,
                tags: data.tags,
                created_at: data.created_at,
                prompt: data.prompt
            }            
            await promptService.updatePrompt(payload);
        } else {
            const newPrompt: Prompt = {
                __typename: 'Prompt',
                name: data.name,
                description: data.description,
                prompt_type: data.prompt_type,
                tags: data.tags,
            }            
            await promptService.createPrompt(newPrompt);
        }
        props.setVisible(false);
        props.getPrompts();
    };

    const cancelChanges = () => {
        onChange({
            ...props.promptConfiguration,
            prompt_type: props.promptConfiguration.prompt_type,
            tags: props.promptConfiguration.tags,
        });

        props.setVisible(false);
    };

    useEffect(() => {
        // if (props.editMode) {
        (data.promptId = props.promptConfiguration.id),
            (data.name = props.promptConfiguration.name),
            (data.description = props.promptConfiguration.description),
            (data.prompt_type = props.promptConfiguration.prompt_type),
            (data.tags = props.promptConfiguration.tags),
            (data.created_at = props.promptConfiguration.created_at),
            (data.prompt = props.promptConfiguration.prompt)
            setSelectedPromptType({
                label: props.promptConfiguration.prompt_type,
                value: props.promptConfiguration.prompt_type,
            });
        // }
    }, [props.promptConfiguration]);

    return (
        <Modal
            onDismiss={() => props.setVisible(false)}
            visible={props.visible}
            footer={
                <Box float="right">
                    <SpaceBetween
                        direction="horizontal"
                        size="xs"
                        alignItems="center"
                    >
                        <Button variant="link" onClick={cancelChanges}>
                            Cancel
                        </Button>
                        <Button variant="primary" onClick={savePrompt}>
                            {(props.editMode) ? 'Save Prompt' : 'Update Prompt' }
                        </Button>
                    </SpaceBetween>
                </Box>
            }
            header={props.editMode ? 'Edit Prompt' : 'Create Prompt'}
        >
            <Form>
                <SpaceBetween size="m">
                    {/* <FormField label="Session Id">{props.sessionId}</FormField> */}
                    <FormField
                        label="Name"
                        errorText={errors.name}
                        description="Enter Name."
                    >
                        <Input
                            value={data.name}
                            onChange={(event) =>
                                onChange({ name: event.detail.value })
                            }
                        />
                    </FormField>
                    <FormField
                        label="Description"
                        errorText={errors.description}
                        description="Enter Description."
                    >
                        <Textarea
                            onChange={({ detail }) =>
                                onChange({ description: detail.value })
                            }
                            value={data.description}
                            placeholder=""
                        />
                    </FormField>
                    <FormField
                        label="Type"
                        errorText={errors.description}
                        description="Enter Type."
                    >
                        <Select
                            selectedOption={selectedPromptType}
                            onChange={({ detail }) => {
                                setSelectedPromptType(detail.selectedOption);
                                onChange({
                                    prompt_type: detail.selectedOption.value,
                                });
                            }}
                            options={[
                                { label: 'Prompt', value: 'Prompt' },
                                { label: 'Chat Prompt', value: 'Chat Prompt' },
                            ]}
                        />
                    </FormField>
                    <FormField
                        label="Tags"
                        errorText={errors.description}
                        description="Enter Tags."
                    >
                        <Textarea
                            onChange={({ detail }) =>
                                onChange({ tags: detail.value })
                            }
                            value={data.tags}
                            placeholder=""
                        />
                    </FormField>
                </SpaceBetween>
            </Form>
        </Modal>
    );
}
