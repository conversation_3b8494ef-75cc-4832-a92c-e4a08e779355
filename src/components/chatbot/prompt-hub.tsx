import * as React from 'react';
import Cards from '@cloudscape-design/components/cards';
import Box from '@cloudscape-design/components/box';
import ReactTimeAgo from 'react-time-ago';
import SpaceBetween from '@cloudscape-design/components/space-between';
import Button from '@cloudscape-design/components/button';
import Header from '@cloudscape-design/components/header';
import ButtonDropdown from '@cloudscape-design/components/button-dropdown';
import Pagination from '@cloudscape-design/components/pagination';
import CollectionPreferences from '@cloudscape-design/components/collection-preferences';
import {
    Badge,
    PropertyFilter,
    TextContent,
} from '@cloudscape-design/components';
import { PropertyFilterI18nStrings } from '../../common/i18n/property-filter-i18n-strings';
import PromptDialog from './prompt-dialog';
import { useCallback, useEffect, useState } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { ChatBotPromptConfiguration } from './types';
import promptService from '../../services/prompt.service';
import { Utils } from '../../common/utils';
import { useCollection } from '@cloudscape-design/collection-hooks';
import TimeAgo from 'javascript-time-ago';
import en from 'javascript-time-ago/locale/en';
import moment from 'moment';
import { TextHelper } from '../../common/helpers/text-helper';
import { PromptsColumnFilteringProperties } from '../../pages/chatbot/prompts/column-definitions';
import { useNavigate } from 'react-router-dom';

TimeAgo.addDefaultLocale(en);

export const PromptHub = () => {
    const navigate = useNavigate();
    const [prompts, setPrompts] = useState<any[]>([]);
    const [loading, setLoading] = useState(true);
    const [editPromptMode, setEditPromptMode] = useState(false);
    const [promptDialogVisible, setPromptDialogVisible] = useState(false);
    const [promptConfiguration, setPromptConfiguration] =
        useState<ChatBotPromptConfiguration>(() => ({
            id: '',
            name: '',
            description: '',
            prompt_type: '',
            tags: '',
        }));
    const [selectedItems, setSelectedItems] = React.useState([]);
    const promptsLoaded = React.useRef(false);

    const sessionId = uuidv4();

    const { items, filteredItemsCount, paginationProps, propertyFilterProps } =
        useCollection(prompts, {
            propertyFiltering: {
                filteringProperties: PromptsColumnFilteringProperties,
            },
            pagination: { pageSize: 10 },
            selection: {},
        });

    const handleSelectionChangeItem = async (event: any) => {
        setSelectedItems(event.detail.selectedItems);
        if (event.detail.selectedItems.length > 0)
            console.log(event.detail.selectedItems[0]);
        setPromptConfiguration({
            id: event.detail.selectedItems[0].id,
            name: event.detail.selectedItems[0].name,
            description: event.detail.selectedItems[0].description,
            prompt_type: event.detail.selectedItems[0].prompt_type,
            tags: event.detail.selectedItems[0].tags,
            created_at: event.detail.selectedItems[0].created_at,
            prompt: event.detail.selectedItems[0].prompt
        });
    };

    const clickActionItem = async (event: any) => {
        if (event.detail.id === 'mv') {
            setEditPromptMode(true);
            setPromptDialogVisible(true);
        } else if (event.detail.id === 'rn') {
            await promptService.deletePromptById(promptConfiguration.id!);
            getPrompts();
        }
    };

    const createNewPrompt = async () => {
        setSelectedItems([]);
        setPromptConfiguration({
            name: '',
            description: '',
            prompt_type: '',
            tags: '',
        });
        setPromptDialogVisible(true);
    };

    const loadPrompts = async () => {
        setLoading(true);
        getPrompts();
    };

    const refreshIconButton = (
        <Button iconName="refresh" variant="primary" onClick={loadPrompts} />
    );

    const getPrompts = useCallback(async () => {
        if (!promptsLoaded.current) {
            setLoading(true);
            promptsLoaded.current = true;
            try {
                const result = await promptService.getPrompts();
                setPrompts(result);
            } catch (error) {
                console.error(Utils.getErrorMessage(error));
            } finally {
                setLoading(false);
            }
        }
    }, []);

    const getTimeAgo = (date: any) => {
        return moment.utc(date).toDate();
    };

    const openPromptPlayground = (itemId: string) => {
        navigate(`/chatbot/prompts/${itemId}`);
    };

    useEffect(() => {
        getPrompts();
    }, [getPrompts]);

    return (
        <>
            <PromptDialog
                sessionId={sessionId}
                visible={promptDialogVisible}
                editMode={editPromptMode}
                setVisible={setPromptDialogVisible}
                getPrompts={getPrompts}
                promptConfiguration={promptConfiguration}
                setPromptConfiguration={setPromptConfiguration}
            />
            <Cards
                onSelectionChange={handleSelectionChangeItem}
                selectedItems={selectedItems}
                ariaLabels={{
                    itemSelectionLabel: (e, t) => `select ${t.name}`,
                    selectionGroupLabel: 'Item selection',
                }}
                cardDefinition={{
                    header: (item) => (
                        <TextContent>
                            <h3>
                                {item.name}
                                <Button
                                    variant="inline-icon"
                                    iconName="external"
                                    onClick={() => {
                                        openPromptPlayground(item.id);
                                    }}
                                />
                            </h3>
                        </TextContent>
                    ),
                    sections: [
                        {
                            id: 'description',
                            header: 'Description',
                            content: (item: any) => item.description,
                        },
                        {
                            id: 'tags',
                            header: 'Tags',
                            content: (item: any) => {
                                let tagsArray: string[] = item.tags.split(',');
                                tagsArray.push(item.promptType);
                                return (
                                    <SpaceBetween
                                        size={'xs'}
                                        direction="horizontal"
                                    >
                                        {tagsArray.map((tag) => (
                                            <Badge key={uuidv4()}>{tag}</Badge>
                                        ))}
                                    </SpaceBetween>
                                );
                            },
                        },
                        {
                            id: 'timestamp',
                            header: 'Updated',
                            content: (item: any) => {
                                return (
                                    <SpaceBetween
                                        size={'xs'}
                                        direction="horizontal"
                                    >
                                        <ReactTimeAgo
                                            date={getTimeAgo(item.updated_at)}
                                            locale="en-US"
                                        />
                                    </SpaceBetween>
                                );
                            },
                        },
                    ],
                }}
                cardsPerRow={[{ cards: 1 }, { minWidth: 500, cards: 2 }]}
                items={items}
                loadingText="Loading resources"
                selectionType="single"
                trackBy="name"
                visibleSections={['description', 'tags', 'timestamp']}
                empty={
                    <Box
                        margin={{ vertical: 'xs' }}
                        textAlign="center"
                        color="inherit"
                    >
                        <SpaceBetween size="m">
                            <b>No prompts</b>
                            <Button variant="primary" onClick={createNewPrompt}>
                                Create Prompt
                            </Button>
                        </SpaceBetween>
                    </Box>
                }
                filter={
                    <PropertyFilter
                        {...propertyFilterProps}
                        i18nStrings={PropertyFilterI18nStrings}
                        filteringPlaceholder={'Filter Prompts'}
                        countText={TextHelper.getTextFilterCounterText(
                            filteredItemsCount,
                        )}
                        expandToViewport={true}
                    />
                }
                header={
                    <Header
                        counter={
                            items?.length ? '(' + items?.length + ')' : '(0)'
                        }
                        actions={
                            <SpaceBetween direction="horizontal" size="xs">
                                {refreshIconButton}
                                <ButtonDropdown
                                    onItemClick={clickActionItem}
                                    items={[
                                        {
                                            text: 'Edit',
                                            id: 'mv',
                                            disabled: false,
                                        },
                                        {
                                            text: 'Delete',
                                            id: 'rn',
                                            disabled: false,
                                        },
                                    ]}
                                >
                                    Actions
                                </ButtonDropdown>
                                <Button
                                    variant="primary"
                                    onClick={createNewPrompt}
                                >
                                    Create Prompt
                                </Button>
                            </SpaceBetween>
                        }
                    >
                        Prompts Hub
                    </Header>
                }
                pagination={<Pagination {...paginationProps} />}
                preferences={
                    <CollectionPreferences
                        title="Preferences"
                        confirmLabel="Confirm"
                        cancelLabel="Cancel"
                        preferences={{
                            pageSize: 6,
                            visibleContent: ['description', 'type', 'size'],
                        }}
                        pageSizePreference={{
                            title: 'Page size',
                            options: [
                                { value: 6, label: '6 prompts' },
                                { value: 12, label: '12 prompts' },
                            ],
                        }}
                        visibleContentPreference={{
                            title: 'Select visible content',
                            options: [
                                {
                                    label: 'Main distribution properties',
                                    options: [
                                        {
                                            id: 'description',
                                            label: 'Description',
                                        },
                                        {
                                            id: 'promptType',
                                            label: 'Prompt Type',
                                        },
                                        { id: 'tags', label: 'Tags' },
                                    ],
                                },
                            ],
                        }}
                    />
                }
            />
        </>
    );
};
