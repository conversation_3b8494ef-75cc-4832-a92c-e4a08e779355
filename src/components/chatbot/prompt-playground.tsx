/* eslint-disable @typescript-eslint/no-non-null-asserted-optional-chain */
/* eslint-disable @typescript-eslint/no-non-null-assertion */
import { useCallback, useContext, useEffect, useState } from 'react';
import {
    BreadcrumbGroup,
    Button,
    Container,
    ContentLayout,
    ExpandableSection,
    Form,
    FormField,
    Grid,
    Header,
    Input,
    Popover,
    Select,
    SpaceBetween,
    Spinner,
    StatusIndicator,
    Textarea,
    Toggle,
} from '@cloudscape-design/components';
import * as _ from 'lodash';
import { AppContext } from '../../common/app-context';
import { useForm } from '../../common/hooks/use-form';
import promptService from '../../services/prompt.service';
import { Utils } from '../../common/utils';
import { Model } from '../../API';
import styles from '../../styles/chat.module.scss';
import modelService from '../../services/model.service';
import {
    ChatBotMessageResponse,
    ChatBotMode,
    PromptPlaygroundState,
} from './types';
import { getCFGModels, getSelectedModelMetadata } from './utils';
import { StorageHelper } from '../../common/helpers/storage-helper';
import { OptionsHelper } from '../../common/helpers/options-helper';
import { NameValue } from '../../common/name-value';
import chatService from '../../services/chat.service';
import { ChatRequestInput } from '../../common/types';
import { v4 as uuidv4 } from 'uuid';
import { CHATBOT_NAME } from '../../common/constants';
import useOnFollow from '../../common/hooks/use-on-follow';
import { JsonView, darkStyles } from 'react-json-view-lite';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { useOktaAuth } from "@okta/okta-react";
import { UserInfo } from "../../common/types";
import { BEDROCKENABLED, CFGAIENABLED } from './../../../config';

interface PromptPlaygroundSettingsFormData {
    showMetadata: boolean;
    maxTokens: number;
    temperature: number;
    topP: number;
}

const defaultChatInputPrompt: NameValue[] = [
    {
        Name: 'SYSTEM',
        Value: 'SYSTEM',
    },
];

export default function PromptPlayground(props: { promptId?: string }) {
    const onFollow = useOnFollow();
    const appContext = useContext(AppContext);
    const [inputValue, setInputValue] = useState('');
    const [inputPromptValue, setInputPromptValue] = useState('');
    const [runningModel, setRunningModel] = useState(false);
    const [outputResponseValue, setOutputResponseValue] = useState('');
    const [promptName, setPromptName] = useState('');
    const [promptType, setPromptType] = useState('Prompt');
    const [chatPromptInputMessages, setChatPromptInputMessages] = useState<
        NameValue[]
    >(defaultChatInputPrompt);
    const [promptVariables, setPromptVariables] = useState<NameValue[]>([]);
    const [modelSelected, setModelSelected] = useState(true);
    const [selectedOption, setSelectedOption] = useState({});
    const [cfgModels, setCFGModels] = useState<Model[]>([]);
    const [state, setState] = useState<PromptPlaygroundState>({
        value: '',
        selectedModel: null,
        selectedModelMetadata: null,
        modelsStatus: 'loading',
    });
    const [chabotResponseData, setChatbotResponseData] =
        useState<ChatBotMessageResponse>({} as ChatBotMessageResponse);

    const modelsOptions = OptionsHelper.getSelectOptionGroups(
        state.models || [],
    );
    console.log("Testing")
//   const { authState, oktaAuth } = useOktaAuth();
  const [userInfo, setUserInfo ] = useState<UserInfo>(); 
  const { data, onChange, errors, validate } =
        useForm<PromptPlaygroundSettingsFormData>({
            initialValue: () => {
                const retValue = {
                    showMetadata: true,
                    maxTokens: 100,
                    temperature: 0.1,
                    topP: 0.5,
                };

                return retValue;
            },
            validate: (form) => {
                const errors: Record<string, string | string[]> = {};

                if (form.temperature < 0 || form.temperature > 1.0) {
                    errors.temperature =
                        'Temperature must be between 0 and 1.0';
                }

                return errors;
            },
        });

    const parseCurlyTokens = (str: string) => {
        // Regex to match curly brackets
        const regex = /\{(.*?)\}/g;
        // Match all occurrences
        const matches = str.match(regex);
        // Return array of matches
        return matches
            ? matches.map((match) => {
                  // Extract token inside brackets
                  const token = match.slice(1, -1);
                  return token;
              })
            : [];
    };

    const parsePrompt = (newValue: string) => {
        const tokens = parseCurlyTokens(newValue);
        setPromptVariables([]);

        let nvArray: NameValue[] = [];
        for (let token of tokens) {
            var nv = new NameValue(token, '');
            nvArray.push(nv);
        }
        setPromptVariables(nvArray);
    };


  const handleInputPromptValueChange = (value: string) => {
    parsePrompt(value);
    setInputPromptValue(value);
  };

  const handleInputPromptVariableValueChange = (
    value: string,
    name: string
  ) => {
    let nvArray: NameValue[] = [];
    for (let nv of promptVariables) {
      if (nv.Name == name) {
        nv.Value = value;
      }
      nvArray.push(nv);
    }
    setPromptVariables(nvArray);
  };

    const getPromptById = useCallback(async () => {
        try {
            const response = await promptService.getPromptById(
                props?.promptId!,
            );
            if (response) {
                setPromptName(response.name);
                setPromptType(response.prompt_type);
                // if (response.prompt_type === 'Chat Prompt') {
                //     setChatPromptInputMessages(JSON.parse(response.prompt));
                // } else {
                setInputPromptValue(response.prompt);
                parsePrompt(response.prompt);
                // }
            }
        } catch (error) {
            console.error(Utils.getErrorMessage(error));
        }
    }, [appContext]);

    const savePrompt = async () => {
        await promptService.updatePromptTemplate(
            props?.promptId!,
            inputPromptValue,
        );
    };

    const runModel = async () => {
        const replaceArray = _.map(promptVariables, 'Name');
        const replaceWith = _.map(promptVariables, 'Value');

        setOutputResponseValue('');
        let inputPrompt = inputPromptValue;
        for (var i = 0; i < replaceArray.length; i++) {
            inputPrompt = inputPrompt.replace(
                new RegExp('{' + replaceArray[i] + '}', 'gi'),
                replaceWith[i],
            );
        }

        const sessionId = uuidv4();
        const isCFGModel = state.selectedModel?.value?.startsWith('cfggpt');
        const modelTokens = state.selectedModel?.value?.split('::');

        const foundModel = _.find(state.models, {
            name: state.selectedModel?.label,
        });

        if (foundModel) {
            setModelSelected(false);

            const selectedModelName = isCFGModel
                ? foundModel?.modelId
                : state.selectedModel?.label;

            setRunningModel(true);

            if (selectedModelName) {
                const payload: ChatRequestInput = {
                    userId: '<EMAIL>',
                    data: {
                        provider: foundModel.provider,
                        modelName: selectedModelName,
                        mode: ChatBotMode.Chain,
                        text: inputPrompt,
                        files: [],
                        sessionId: sessionId,
                        modelKwargs: {
                            streaming: false,
                            useHistory: true,
                            followPromptId: '',
                            maxTokens: data.maxTokens,
                            temperature: data.temperature,
                            topP: data.topP,
                            numDocs: 3,
                        },
                    },
                };

                // if (isCFGModel) {
                //     chatService.sendCFGQuery(payload).then((response) => {
                //         setRunningModel(false);
                //         if (response && response.data && response.data.content)
                //             setOutputResponseValue(response.data.content);
                //     });
                // } else {
                chatService.sendQuery(payload).then((response) => {
                    setRunningModel(false);
                    if (response && response.data && response.data.content) {
                        setOutputResponseValue(response.data.content);
                        setChatbotResponseData(response);
                    }
                });
                // }
            }
        } else {
            setModelSelected(true);
        }
    };

    const getAllModels = useCallback(async () => {
        let aws_models: Model[] = [];
        let cfg_models: Model[] = [];
        let models: Model[] = [];

        if (BEDROCKENABLED) {
            aws_models = await modelService.getModels();
        }

        if (CFGAIENABLED) {
            cfg_models = await getCFGModels();
            setCFGModels(cfgModels);
        }

        models = aws_models.concat(cfg_models);

        setState((state) => ({
            ...state,
            models: models,
        }));
        setState((state) => ({
            ...state,
            modelsStatus: 'finished',
        }));
    }, []);

    useEffect(() => {
        if (!appContext) return;
    }, [promptVariables]);

    const getPromptName = () => {
        return promptName;
    };

    useEffect(() => {
        getAllModels();
        getPromptById();
    }, [getPromptById]);

    return (
        <ContentLayout
            header={
                <SpaceBetween size="m">
                    <BreadcrumbGroup
                        onFollow={onFollow}
                        items={[
                            {
                                text: CHATBOT_NAME,
                                href: '/',
                            },
                            {
                                text: 'Prompts',
                                href: '/chatbot/prompts',
                            },
                            {
                                text: getPromptName(),
                                href: 'javascript:void(0)',
                            },
                        ]}
                    />
                    <Header
                        variant="h1"
                        description="The Prompt Playground allows developers to visualize, iterate, and version prompts."
                        actions={
                            <Button variant="primary" onClick={savePrompt}>
                                Save
                            </Button>
                        }
                    >
                        Prompt Playground
                    </Header>
                </SpaceBetween>
            }
        >
            <Container key={999}>
                <Grid
                    gridDefinition={[
                        { colspan: 4 },
                        { colspan: 4 },
                        { colspan: 4 },
                    ]}
                >
                    <div>
                        <Container
                            key={1}
                            header={<Header variant="h2">Template</Header>}
                        >
                            <FormField>
                                <Textarea
                                    onChange={({ detail }) =>
                                        handleInputPromptValueChange(
                                            detail.value,
                                        )
                                    }
                                    value={inputPromptValue}
                                    placeholder="Enter your prompt here..."
                                    rows={15}
                                />
                            </FormField>
                        </Container>
                    </div>
                    <div>
                        <SpaceBetween direction="vertical" size="xs">
                            <Container
                                key={2}
                                header={<Header variant="h2">Inputs</Header>}
                            >
                                <SpaceBetween size={'xs'} direction="vertical">
                                    {promptVariables.map((prompt, idx) => (
                                        <FormField
                                            key={idx}
                                            label={prompt.Name}
                                            errorText={errors.showMetadata}
                                        >
                                            <Textarea
                                                onChange={({ detail }) => {
                                                    handleInputPromptVariableValueChange(
                                                        detail.value,
                                                        prompt.Name,
                                                    );
                                                }}
                                                value={prompt.Value}
                                                placeholder=""
                                            />
                                        </FormField>
                                    ))}
                                </SpaceBetween>
                            </Container>
                            <Container
                                key={3}
                                header={
                                    <Header
                                        variant="h2"
                                        actions={
                                            <SpaceBetween
                                                direction="horizontal"
                                                size="xs"
                                            >
                                                {runningModel ? (
                                                    <>
                                                        Loading&nbsp;&nbsp;
                                                        <Spinner />
                                                    </>
                                                ) : (
                                                    <Button onClick={runModel}>
                                                        Run
                                                    </Button>
                                                )}
                                            </SpaceBetween>
                                        }
                                    >
                                        Response
                                    </Header>
                                }
                                footer={
                                    ((data.showMetadata &&
                                        chabotResponseData &&
                                        chabotResponseData.data &&
                                        chabotResponseData.data.metadata) ||
                                        (chabotResponseData &&
                                            chabotResponseData.data &&
                                            chabotResponseData.data.metadata &&
                                            data.showMetadata)) && (
                                        <ExpandableSection
                                            variant="footer"
                                            headerText="Metadata"
                                        >
                                            <JsonView
                                                shouldInitiallyExpand={(
                                                    level,
                                                ) => level < 2}
                                                data={JSON.parse(
                                                    JSON.stringify(
                                                        chabotResponseData.data
                                                            .metadata,
                                                    ).replace(/\\n/g, '\\\\n'),
                                                )}
                                                style={{
                                                    ...darkStyles,
                                                    stringValue: 'jsonStrings',
                                                    numberValue: 'jsonNumbers',
                                                    booleanValue: 'jsonBool',
                                                    nullValue: 'jsonNull',
                                                    container: 'jsonContainer',
                                                }}
                                            />
                                        </ExpandableSection>
                                    )
                                }
                            >
                                <ReactMarkdown
                                    children={outputResponseValue}
                                    remarkPlugins={[remarkGfm]}
                                    components={{
                                        pre(props) {
                                            const {
                                                children,
                                                className,
                                                node,
                                                ...rest
                                            } = props;
                                            return (
                                                <pre
                                                    {...rest}
                                                    className={
                                                        styles.codeMarkdown
                                                    }
                                                >
                                                    {children}
                                                </pre>
                                            );
                                        },
                                        table(props) {
                                            const { children, ...rest } = props;
                                            return (
                                                <table
                                                    {...rest}
                                                    className={
                                                        styles.markdownTable
                                                    }
                                                >
                                                    {children}
                                                </table>
                                            );
                                        },
                                        th(props) {
                                            const { children, ...rest } = props;
                                            return (
                                                <th
                                                    {...rest}
                                                    className={
                                                        styles.markdownTableCell
                                                    }
                                                >
                                                    {children}
                                                </th>
                                            );
                                        },
                                        td(props) {
                                            const { children, ...rest } = props;
                                            return (
                                                <td
                                                    {...rest}
                                                    className={
                                                        styles.markdownTableCell
                                                    }
                                                >
                                                    {children}
                                                </td>
                                            );
                                        },
                                    }}
                                />
                                {outputResponseValue &&
                                outputResponseValue.length > 0 ? (
                                    <div
                                        className={
                                            styles.btn_chabot_metadata_copy
                                        }
                                    >
                                        <Popover
                                            size="medium"
                                            position="top"
                                            triggerType="custom"
                                            dismissButton={false}
                                            content={
                                                <StatusIndicator type="success">
                                                    Copied to clipboard
                                                </StatusIndicator>
                                            }
                                        >
                                            <Button
                                                variant="inline-icon"
                                                iconName="copy"
                                                onClick={() => {
                                                    navigator.clipboard.writeText(
                                                        outputResponseValue,
                                                    );
                                                }}
                                            />
                                        </Popover>
                                    </div>
                                ) : (
                                    ''
                                )}
                            </Container>
                        </SpaceBetween>
                    </div>
                    <div>
                        <Container
                            key={4}
                            header={<Header variant="h2">Settings</Header>}
                        >
                            <Form>
                                <SpaceBetween size="m">
                                    <FormField
                                        label="Model"
                                        errorText={errors.showMetadata}
                                    >
                                        <Select
                                            statusType={state.modelsStatus}
                                            loadingText="Loading models (might take few seconds)..."
                                            placeholder="Select a model"
                                            empty={
                                                <div>
                                                    No models available. Please
                                                    make sure you have access to
                                                    Amazon Bedrock or
                                                    alternatively deploy a self
                                                    hosted model on SageMaker or
                                                    add API_KEY to Secrets
                                                    Manager
                                                </div>
                                            }
                                            filteringType="auto"
                                            selectedOption={state.selectedModel}
                                            onChange={({ detail }) => {
                                                setState((state: any) => ({
                                                    ...state,
                                                    selectedModel:
                                                        detail.selectedOption,
                                                    selectedModelMetadata:
                                                        getSelectedModelMetadata(
                                                            state.models,
                                                            detail.selectedOption,
                                                        ),
                                                }));
                                                if (
                                                    detail.selectedOption?.value
                                                ) {
                                                    StorageHelper.setSelectedLLM(
                                                        detail.selectedOption
                                                            .value,
                                                    );
                                                }
                                            }}
                                            options={modelsOptions}
                                        />
                                    </FormField>
                                    <FormField
                                        label="Max Tokens"
                                        errorText={errors.maxTokens}
                                    >
                                        <Input
                                            type="number"
                                            step={1}
                                            value={data.maxTokens.toString()}
                                            onChange={({
                                                detail: { value },
                                            }) => {
                                                onChange({
                                                    maxTokens: parseInt(value),
                                                });
                                            }}
                                        />
                                    </FormField>
                                    <FormField
                                        label="Temperature"
                                        errorText={errors.temperature}
                                    >
                                        <Input
                                            type="number"
                                            step={0.05}
                                            value={data.temperature.toFixed(2)}
                                            onChange={({
                                                detail: { value },
                                            }) => {
                                                let floatVal =
                                                    parseFloat(value);
                                                floatVal = Math.min(
                                                    1.0,
                                                    Math.max(0.0, floatVal),
                                                );
                                                onChange({
                                                    temperature: floatVal,
                                                });
                                            }}
                                        />
                                    </FormField>
                                    <FormField
                                        label="Top-P"
                                        errorText={errors.topP}
                                    >
                                        <Input
                                            type="number"
                                            step={0.1}
                                            value={data.topP.toFixed(2)}
                                            onChange={({
                                                detail: { value },
                                            }) => {
                                                let floatVal =
                                                    parseFloat(value);
                                                floatVal = Math.min(
                                                    1.0,
                                                    Math.max(0.0, floatVal),
                                                );
                                                onChange({ topP: floatVal });
                                            }}
                                        />
                                    </FormField>
                                    <FormField
                                        label="Metadata"
                                        errorText={errors.showMetadata}
                                    >
                                        <Toggle
                                            checked={data.showMetadata}
                                            onChange={({
                                                detail: { checked },
                                            }) =>
                                                onChange({
                                                    showMetadata: checked,
                                                })
                                            }
                                        >
                                            Show metadata
                                        </Toggle>
                                    </FormField>
                                </SpaceBetween>
                            </Form>
                        </Container>
                    </div>
                </Grid>
            </Container>
        </ContentLayout>
    );
}
