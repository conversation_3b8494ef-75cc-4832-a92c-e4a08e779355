// import Multiselect from "@cloudscape-design/components/multiselect";
import {partyOptions} from './formfield-options';
import React, {useState, useEffect, useRef} from "react";
import {
    FormField,
    Select,
    Multiselect,
    SelectProps,
    MultiselectProps,
    Button,
    Modal, Box, SpaceBetween, Input, Autosuggest
} from "@cloudscape-design/components";
import { requiredOption, allOptions } from './multi-selections-prompts.ts';
import styles from "../../styles/chat.module.scss";

// add new arg reset
const PromptSuggestions = ({suggestedPrompts, onOptionsClick, resetDocumentBuilder}) => {
    return (
        <div style={{display: 'flex', marginTop: '15px'}}>
            <p style={{color: '#827f7c', fontSize: '11px', fontWeight: '600'}}>Suggestions:</p>
            {suggestedPrompts.map((suggestion, idx) => {
                return (
                    <button key={idx} className={styles.prompt_suggestions_btn}
                            onClick={() => {
                                // reset Document Builder
                                if (resetDocumentBuilder) {
                                    resetDocumentBuilder();
                                }
                                onOptionsClick(suggestion.promptText)
                            }}>
                        {suggestion.option}
                    </button>
                )
            })}
        </div>
    )
}

const handleMultiselectChange = (
    detail,
    selectedOptions,
    setSelectedOptions,
    partyOptions,
    otherOptionValue = "other_party1",
    setDisplayOtherInput = null,
    previouslySelectedOther = false
) => {
    const newSelectedOptions = [...detail.selectedOptions];

    // 检查是否选择了"Other"选项
    const hasOtherOption = newSelectedOptions.some(
        option => option.value === otherOptionValue
    );

    // 如果选择了"Other"选项并且之前没有选择，显示输入对话框
    if (hasOtherOption && !previouslySelectedOther && setDisplayOtherInput) {
        setDisplayOtherInput(true);
    }

    // If nothing is selected, add back the placeholder option
    // if (newSelectedOptions.length === 0) {
    //     setSelectedOptions([partyOptions[0]]);
    // } else {
    //     // Convert readonly array to regular array with spread operator
    //     setSelectedOptions([...newSelectedOptions]);
    // }

    // Always set selected options directly - allow empty array
    setSelectedOptions([...newSelectedOptions]);
};

// 1. 修改 MultiplePromptSuggestions 组件以检查是否只选择了默认选项
interface MultiplePromptSuggestionsProps {
    running?: boolean; // Make this optional since you might not always pass it
    resetState?: boolean;
}

// export const MultiplePromptSuggestions: React.FC<MultiplePromptSuggestionsProps> = (props = {
//     running: false,
//     resetState: false
// }) => {
//     // this is of the layout
//     const outerContainerRef = useRef(null);
//     const multiselectContainerRef = useRef(null);
//     const buttonContainerRef = useRef(null);
//     const partyContainerRef = useRef(null);
//
//     // Document builder options
//     const documentBuilderOptions: SelectProps.Option[] = [
//         {
//             label: "Select",
//             value: "",
//             iconName: "close",
//         },
//         {
//             label: "DUA",
//             value: "dua",
//             iconName: "file",
//         },
//     ];
//
//     // Party1 options
//     const party1Options: MultiselectProps.Option[] = [
//         {...partyOptions[0], label: "Select Party 1"},
//         ...partyOptions.slice(1),
//         {
//             label: "Other",
//             value: "other_party1",
//             iconName: "edit",
//         }
//     ];
//
//     // Party2 options
//     const party2Options: MultiselectProps.Option[] = [
//         {...partyOptions[0], label: "Select Party 2"},
//         ...partyOptions.slice(1),
//         {
//             label: "Other",
//             value: "other_party2",
//             iconName: "edit",
//         }
//     ];
//
//     const [selectedDocumentBuilder, setSelectedDocumentBuilder] = useState<SelectProps.Option>(documentBuilderOptions[0]);
//
//     // change to multi for party1 and party2
//     // const [selectedParty1, setSelectedParty1] = useState<SelectProps.Option>(party1Options[1]);
//     const [selectedParty1Options, setSelectedParty1Options] = useState<MultiselectProps.Option[]>([]);
//     // const [selectedParty2, setSelectedParty2] = useState<SelectProps.Option>(party2Options[0]);
//     const [selectedParty2Options, setSelectedParty2Options] = useState<MultiselectProps.Option[]>([]);
//
//     // other option for party1 and party2
//     const [party1OtherText, setParty1OtherText] = useState<string>("");
//     const [party2OtherText, setParty2OtherText] = useState<string>("");
//
//     // Track display state for Other inputs
//     const [displayParty1OtherInput, setDisplayParty1OtherInput] = useState<boolean>(false);
//     const [displayParty2OtherInput, setDisplayParty2OtherInput] = useState<boolean>(false);
//
//     // 添加状态来跟踪是否已确认
//     const [party1Confirmed, setParty1Confirmed] = useState(false);
//     const [party2Confirmed, setParty2Confirmed] = useState(false);
//
// // 添加状态来跟踪上次确认的值
//     const [lastConfirmedParty1Text, setLastConfirmedParty1Text] = useState("");
//     const [lastConfirmedParty2Text, setLastConfirmedParty2Text] = useState("");
//
//
//
//     // Add an effect to reset the state when resetState prop changes
//     useEffect(() => {
//         if (props.resetState) {
//             setSelectedDocumentBuilder(documentBuilderOptions[0]);
//             // change the useEffect for multi
//             // setSelectedParty1(party1Options[0]);
//             // setSelectedParty2(party2Options[0]);
//
//             // setSelectedParty1Options([party1Options[0]]);
//             // setSelectedParty2Options([party2Options[0]]);
//             setSelectedParty1Options([]);
//             setSelectedParty2Options([]);
//             setParty1OtherText("");
//             setParty2OtherText("");
//             setDisplayParty1OtherInput(false);
//             setDisplayParty2OtherInput(false);
//             setParty1Confirmed(false);
//             setParty2Confirmed(false);
//             setLastConfirmedParty1Text("");
//             setLastConfirmedParty2Text("");
//         }
//     }, [props.resetState]);
//
//     // 初始状态只有必选项
//     const [selectedOptions, setSelectedOptions] = useState([requiredOption]);
//     // 用于存储合并后的提示词
//     const [combinedPrompt, setCombinedPrompt] = useState(requiredOption.value);
//     // 标记是否只选择了默认选项
//     const [onlyDefaultSelected, setOnlyDefaultSelected] = useState(true);
//     const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
//     const containerRef = useRef(null);
//     const [topOffset, setTopOffset] = useState(0);
//
//     // 创建一个平铺的选项列表，用于确定顺序
//     const flattenedOptions = allOptions.reduce((acc, group) => {
//         return [...acc, ...group.options];
//     }, []);
//
//     useEffect(() => {
//         const calculateOffset = () => {
//             const suggestionsLabel = document.querySelector('[style*="color: #827f7c"]');
//             if (suggestionsLabel && containerRef.current) {
//                 const suggestionRect = suggestionsLabel.getBoundingClientRect();
//                 const containerRect = containerRef.current.getBoundingClientRect();
//                 const neededOffset = suggestionRect.top - containerRect.top;
//                 setTopOffset(neededOffset);
//             }
//         };
//
//         calculateOffset();
//
//         window.addEventListener('resize', calculateOffset);
//         return () => window.removeEventListener('resize', calculateOffset);
//     }, []);
//     // 这里原本是自动传给 lexical editor感觉不是很好，解决不了muti selections自动关闭的问题
//     // Add effect to update prompt when document builder or parties change
//     // useEffect(() => {
//     //     if (selectedOptions.length > 0) {
//     //         const newPrompt = combineSelections(selectedOptions);
//     //         setCombinedPrompt(newPrompt);
//     //
//     //         // Dispatch event with updated prompt
//     //         const event = new CustomEvent('multiple-prompt-change', {
//     //             detail: {
//     //                 prompt: newPrompt,
//     //                 onlyDefaultSelected: onlyDefaultSelected
//     //             }
//     //         });
//     //         document.dispatchEvent(event);
//     //     }
//     // }, [selectedDocumentBuilder, selectedParty1, selectedParty2]);
//     //这里的修改是为了让选择mou的时候不会自动传递prompt给Lexical
//     useEffect(() => {
//         if (selectedOptions.length > 0) {
//             const newPrompt = combineSelections(selectedOptions);
//             setCombinedPrompt(newPrompt);
//
//             // Set unsaved changes flag if document or party selection changes
//             if (selectedDocumentBuilder.value ||
//                 // selectedParty1.value !== "" || selectedParty2.value !== "")
//                 // changed to multi
//                 (selectedParty1Options.length > 0 && selectedParty1Options[0].value !== "") ||
//                 (selectedParty2Options.length > 0 && selectedParty2Options[0].value !== ""))
//             {
//                 setHasUnsavedChanges(true);
//             }
//         }
//     },
//         // [selectedDocumentBuilder, selectedParty1, selectedParty2, selectedOptions]
//         [selectedDocumentBuilder, selectedParty1Options, selectedParty2Options, selectedOptions, party1OtherText, party2OtherText]
//     );
//
//     // 当选择变化时，组合所有选择项为一个提示词，并按定义顺序排序
//     const combineSelections = (selections) => {
//         // 首先确保必选项（1.0 General Terms）总是排在第一位
//         let sortedSelections = [...selections];
//
//         // 从排序后的数组中移除必选项，因为它将被单独处理
//         sortedSelections = sortedSelections.filter(option => option.label !== requiredOption.label);
//
//         // 根据 flattenedOptions 中的顺序排序其他选择项
//         sortedSelections.sort((a, b) => {
//             const indexA = flattenedOptions.findIndex(opt => opt.label === a.label);
//             const indexB = flattenedOptions.findIndex(opt => opt.label === b.label);
//             return indexA - indexB;
//         });
//
//         // 首先添加必选项的值（不包含标签）
//         let prompt = requiredOption.value;
//
//         // 替换party1 party2 单个版本
//         // if (selectedParty1.value) {
//         //     prompt = prompt.replace(/\[PARTY A\]/g, selectedParty1.label);
//         // }
//         //
//         // if (selectedParty2.value) {
//         //     prompt = prompt.replace(/\[PARTY B\]/g, selectedParty2.label);
//         // }
//         // 替换party1 - 多选版本，用逗号连接
//         // if (selectedParty1Options.length > 0) {
//         //     // Filter out the "Select Party 1" option if it's there
//         //     const actualParties = selectedParty1Options.filter(party =>
//         //         party.value !== "" && party.value !== "select_all_party1"
//         //     );
//         if (selectedParty1Options.length > 0) {
//             const labels = selectedParty1Options
//                 .filter(party => party.value !== "" && party.value !== "select_all_party1")
//                 .map(party =>
//                     (party.value === "other_party1") ? party1OtherText || null : party.label
//                 )
//                 .filter(Boolean); // 过滤掉null、undefined和空字符串
//
//             if (labels.length > 0) {
//                 prompt = prompt.replace(/\[PARTY A\]/g, labels.join(", "));
//             }
//         }
//         // if (selectedParty1Options && selectedParty1Options.length > 0) {
//         //     // Filter out the "Select Party 1" option if it's there
//         //     const actualParties = selectedParty1Options.filter(party =>
//         //         party.value !== "" && party.value !== "select_all_party1"
//         //     );
//         //
//         //     if (actualParties.length > 0) {
//         //         let party1Labels = actualParties.map(party => {
//         //             // 如果是"Other"选项且有自定义文本，则使用自定义文本
//         //             if (party.value === "other_party1" && party1OtherText) {
//         //                 return party1OtherText;
//         //             }
//         //             return party.label;
//         //         }).join(", ");
//         //         prompt = prompt.replace(/\[PARTY A\]/g, party1Labels);
//         //     }
//         // }
//
//         // 替换party2 - 多选版本，用逗号连接
//         // if (selectedParty2Options.length > 0) {
//         //     // Filter out the "Select Party 2" option if it's there
//         //     const actualParties = selectedParty2Options.filter(party =>
//         //         party.value !== "" && party.value !== "select_all_party2"
//         //     );
//         //
//         //     if (actualParties.length > 0) {
//         //         let party2Labels = actualParties.map(party => {
//         //             // 如果是"Other"选项且有自定义文本，则使用自定义文本
//         //             if (party.value === "other_party2" && party2OtherText) {
//         //                 return party2OtherText;
//         //             }
//         //             return party.label;
//         //         }).join(", ");
//         //         prompt = prompt.replace(/\[PARTY B\]/g, party2Labels);
//         //     }
//         // }
//         if (selectedParty2Options.length > 0) {
//             const labels = selectedParty2Options
//                 .filter(party => party.value !== "" && party.value !== "select_all_party2")
//                 .map(party =>
//                     (party.value === "other_party2") ? party2OtherText || null : party.label
//                 )
//                 .filter(Boolean); // 过滤掉null、undefined和空字符串
//
//             if (labels.length > 0) {
//                 prompt = prompt.replace(/\[PARTY B\]/g, labels.join(", "));
//             }
//         }
//
//         // 然后按排序后的顺序添加其他选项的标签和值
//         sortedSelections.forEach(option => {
//             prompt += `\n${option.label}\n${option.value}`;
//         });
//         return prompt;
//     };
//     // track if there are unsaved changes
//     // const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
//
//     // Handle multiselect change
//     const handleChange = ({detail}) => {
//         // fixed the auto close multi dropdown on local, not very useful i think
//         // if (detail.event) {
//         //     detail.event.preventDefault();
//         //     detail.event.stopPropagation();
//         // }
//
//         // 确保必选项总是包含在选择中
//         const isRequiredOptionIncluded = detail.selectedOptions.some(
//             option => option.label === requiredOption.label
//         );
//
//         let newSelectedOptions = [...detail.selectedOptions];
//
//         if (!isRequiredOptionIncluded) {
//             newSelectedOptions = [requiredOption, ...newSelectedOptions];
//         }
//
//         // 检查是否只选择了默认选项
//         const hasOnlyDefaultOption = newSelectedOptions.length === 1 &&
//             newSelectedOptions[0].label === requiredOption.label;
//
//         setOnlyDefaultSelected(hasOnlyDefaultOption);
//         setSelectedOptions(newSelectedOptions);
//
//         // 组合所有选择项为一个提示词，按定义的顺序排序
//         const newCombinedPrompt = combineSelections(newSelectedOptions);
//         setCombinedPrompt(newCombinedPrompt);
//
//         // 关键修改：使用自定义事件通知 ChatInputPanel 已经有新的组合提示词
//         // 同时传递是否只选择了默认选项的信息
//         //     const event = new CustomEvent('multiple-prompt-change', {
//         //         detail: {
//         //             prompt: newCombinedPrompt,
//         //             onlyDefaultSelected: hasOnlyDefaultOption
//         //         }
//         //     });
//         //     document.dispatchEvent(event);
//         //
//         //     console.log("Combined prompt:", newCombinedPrompt);
//         //     console.log("Only default selected:", hasOnlyDefaultOption);
//         // };
//         // Mark that we have unsaved changes
//         setHasUnsavedChanges(true);
//     };
//
//     // New function to apply changes
//     const applyChanges = () => {
//         // Dispatch event to update the Lexical editor
//         const event = new CustomEvent('multiple-prompt-change', {
//             detail: {
//                 prompt: combinedPrompt,
//                 onlyDefaultSelected: onlyDefaultSelected
//             }
//         });
//         document.dispatchEvent(event);
//
//         // Reset the unsaved changes flag
//         setHasUnsavedChanges(false);
//     };
//
//     return (
//         // <div style={{ display: 'flex', alignItems: 'flex-start' }}>
//         <div
//             ref={outerContainerRef}
//             style={{
//                 // width: '220px',
//                 // maxWidth: '220px',
//                 marginLeft: `${topOffset}px`,
//                 marginTop: `${topOffset + 21}px`,
//                 position: 'relative',
//                 marginBottom: '20px'
//             }}
//         >
//             <div className={styles.document_library_container} style={{display: 'flex', alignItems: 'start'}}>
//                 {/* Document builder 组 - 水平布局 */}
//                 <div style={{display: 'flex', alignItems: 'center'}}>
//                     <FormField label="Document builder"/>
//                     <span>&nbsp;&nbsp;</span>
//                     <Select
//                         disabled={props.running}
//                         placeholder="Select document type"
//                         selectedOption={selectedDocumentBuilder}
//                         options={documentBuilderOptions}
//                         onChange={({detail}) => {
//                             setSelectedDocumentBuilder(detail.selectedOption);
//                             // Reset party selections when document type changes for multi
//                             // setSelectedParty1(party1Options[0]);
//                             // setSelectedParty2(party2Options[0]);
//                             // setSelectedParty1Options([party1Options[0]]);
//                             // setSelectedParty2Options([party2Options[0]]);
//                             setSelectedParty1Options([]);
//                             setSelectedParty2Options([]);
//                             setParty1OtherText("");
//                             setParty2OtherText("");
//                         }}
//                     />
//                 </div>
//
//                 {/* Only show Party1 and Party2 fields when MOU is selected */}
//                 {selectedDocumentBuilder.value === "dua" && (
//                     <>
//                         {/* Party 1 组 - 水平布局 */}
//                         {/*<div style={{display: 'flex', alignItems: 'left'}}>*/}
//                             {/*<FormField label="Party 1"/>*/}
//                             {/*<Select*/}
//                             {/*    disabled={props.running}*/}
//                             {/*    placeholder="Select Party 1"*/}
//                             {/*    selectedOption={selectedParty1}*/}
//                             {/*    options={party1Options}*/}
//                             {/*    onChange={({detail}) => setSelectedParty1(detail.selectedOption)}*/}
//                             {/*/>*/}
//                             {/*changed to multi*/}
//                             <div
//                                 ref={partyContainerRef}
//                                 style={{
//                                     width: '220px',
//                                     maxWidth: '220px',
//                                     marginLeft: `${topOffset+ 19}px`,
//                                     marginTop: `${topOffset}px`,
//                                     position: 'relative',
//                                     marginBottom: '20px'
//                                 }}
//                             >
//                             <Multiselect
//                                 disabled={props.running}
//                                 // disabled
//                                 // expandToViewport={true}
//                                 placeholder="Select Party 1"
//                                 selectedOptions={selectedParty1Options}
//                                 options={party1Options.slice(1)}
//                                 enableSelectAll
//                                 onChange={({detail}) => {
//                                     // 检查之前是否已选择了Other选项
//                                     const previouslySelectedOther = selectedParty1Options.some(
//                                         opt => opt.value === "other_party1"
//                                     );
//
//                                     handleMultiselectChange(
//                                         detail,
//                                         selectedParty1Options,
//                                         setSelectedParty1Options,
//                                         party1Options,
//                                         "other_party1",
//                                         setDisplayParty1OtherInput,
//                                         previouslySelectedOther
//                                     );
//                                 }}
//                                 i18nStrings={{
//                                     selectAllText: "Select All"
//                                 }}
//                                 tokenLimit={0}
//                             />
//                             {/* Party 1 的Autosuggest */}
//                             {selectedParty1Options.some(opt => opt.value === "other_party1") && (
//                                 <div style={{
//                                     marginTop: '10px',
//                                     width: '100%',
//                                     maxWidth: '200px'
//                                 }}>
//                                         <Autosuggest
//                                             value={party1OtherText}
//                                             onChange={({detail}) => {
//                                                 // 更新Party1的文本
//                                                 setParty1OtherText(detail.value);
//                                                 // 如果输入内容与上次确认的不同，允许再次确认
//                                                 if (detail.value !== lastConfirmedParty1Text) {
//                                                     setParty1Confirmed(false);
//                                                 }
//                                             }}
//                                             onSelect={({detail}) => {
//                                                 if (detail.value) {
//                                                     setParty1OtherText(detail.value);
//                                                     // 如果选择的值与上次确认的不同，允许再次确认
//                                                     if (detail.value !== lastConfirmedParty1Text) {
//                                                         setParty1Confirmed(false);
//                                                     }
//                                                 }
//                                             }}
//                                             options={partyOptions.slice(1)}
//                                             enteredTextLabel={value => `Use: "${value}"`}
//                                             placeholder="Type a custom name"
//                                             empty="Begin typing to create a custom name"
//                                             ariaLabel="Custom party name input"
//                                             expandToViewport={true}
//                                             onBlur={() => {
//                                                 if (!party1OtherText) {
//                                                     setSelectedParty1Options(selectedParty1Options.filter(
//                                                         option => option.value !== "other_party1"
//                                                     ));
//                                                     setDisplayParty1OtherInput(false);
//                                                 }
//                                             }}
//                                             filteringType="manual"
//                                         />
//                                     <div style={{
//                                         display: 'flex',
//                                         justifyContent: 'flex-end',
//                                         marginTop: '10px'
//                                     }}>
//                                         <SpaceBetween direction="horizontal" size="xs">
//                                             <Button
//                                                 variant="link"
//                                                 onClick={() => {
//                                                     setParty1OtherText("");
//                                                     setSelectedParty1Options(selectedParty1Options.filter(
//                                                         option => option.value !== "other_party1"
//                                                     ));
//                                                     setDisplayParty1OtherInput(false);
//                                                     setParty1Confirmed(false);
//                                                     setLastConfirmedParty1Text("");
//                                                 }}
//                                             >
//                                                 Cancel
//                                             </Button>
//                                             <Button
//                                                 variant="primary"
//                                                 onClick={() => {
//                                                     if (!party1OtherText) {
//                                                         setParty1OtherText("Custom Party");
//                                                     }
//                                                     // 记录当前确认的值
//                                                     setLastConfirmedParty1Text(party1OtherText);
//                                                     // 标记为已确认
//                                                     setParty1Confirmed(true);
//                                                     setDisplayParty1OtherInput(false);
//                                                 }}
//                                                 disabled={!party1OtherText || party1Confirmed}
//                                             >
//                                                 Save
//                                             </Button>
//                                         </SpaceBetween>
//                                     </div>
//                                 </div>
//                             )}
//                         </div>
//
//                         {/* Party 2 组 - 水平布局 */}
//                         <div style={{display: 'flex', alignItems: 'center'}}>
//                             {/*<FormField label="Party 2"/>*/}
//                             {/*<Select*/}
//                             {/*    disabled={props.running}*/}
//                             {/*    placeholder="Select Party 2"*/}
//                             {/*    selectedOption={selectedParty2}*/}
//                             {/*    options={party2Options}*/}
//                             {/*    onChange={({detail}) => setSelectedParty2(detail.selectedOption)}*/}
//                             {/*/>*/}
//                             <div
//                                 ref={partyContainerRef}
//                                 style={{
//                                     width: '220px',
//                                     maxWidth: '220px',
//                                     marginLeft: `${topOffset+ 19}px`,
//                                     marginTop: `${topOffset}px`,
//                                     position: 'relative',
//                                     marginBottom: '20px'
//                                 }}
//                             >
//                             <Multiselect
//                                 disabled={props.running}
//                                 // disabled
//                                 // expandToViewport={true}
//                                 placeholder="Select Party 2"
//                                 selectedOptions={selectedParty2Options}
//                                 options={party2Options.slice(1)}
//                                 onChange={({detail}) => {
//                                     // 检查之前是否已选择了Other选项
//                                     const previouslySelectedOther = selectedParty2Options.some(
//                                         opt => opt.value === "other_party2"
//                                     );
//
//                                     handleMultiselectChange(
//                                         detail,
//                                         selectedParty2Options,
//                                         setSelectedParty2Options,
//                                         party2Options,
//                                         "other_party2",
//                                         setDisplayParty2OtherInput,
//                                         previouslySelectedOther
//                                     );
//                                 }}
//                                 tokenLimit={0}
//                                 enableSelectAll
//                                 i18nStrings={{
//                                     selectAllText: "Select All"
//                                 }}
//                             />
//                             {selectedParty2Options.some(opt => opt.value === "other_party2") && (
//                                 <div style={{
//                                     marginTop: '10px',
//                                     width: '100%',
//                                     maxWidth: '400px'
//                                 }}>
//                                         <Autosuggest
//                                             value={party2OtherText}
//                                             onChange={({detail}) => {
//                                                 // 更新Party2的文本
//                                                 setParty2OtherText(detail.value);
//                                                 // 如果输入内容与上次确认的不同，允许再次确认
//                                                 if (detail.value !== lastConfirmedParty2Text) {
//                                                     setParty2Confirmed(false);
//                                                 }
//                                             }}
//                                             onSelect={({detail}) => {
//                                                 if (detail.value) {
//                                                     setParty2OtherText(detail.value);
//                                                     // 如果选择的值与上次确认的不同，允许再次确认
//                                                     if (detail.value !== lastConfirmedParty2Text) {
//                                                         setParty2Confirmed(false);
//                                                     }
//                                                 }
//                                             }}
//                                             options={partyOptions.slice(1)}
//                                             enteredTextLabel={value => `Use: "${value}"`}
//                                             placeholder="Type a custom name"
//                                             empty="Begin typing to create a custom name"
//                                             ariaLabel="Custom party name input"
//                                             expandToViewport={true}
//                                             onBlur={() => {
//                                                 if (!party2OtherText) {
//                                                     setSelectedParty2Options(selectedParty2Options.filter(
//                                                         option => option.value !== "other_party2"
//                                                     ));
//                                                     setDisplayParty2OtherInput(false);
//                                                 }
//                                             }}
//                                             filteringType="manual"
//                                         />
//                                     <div style={{
//                                         display: 'flex',
//                                         justifyContent: 'flex-end',
//                                         marginTop: '10px'
//                                     }}>
//                                         <SpaceBetween direction="horizontal" size="xs">
//                                             <Button
//                                                 variant="link"
//                                                 onClick={() => {
//                                                     setParty2OtherText("");
//                                                     setSelectedParty2Options(selectedParty2Options.filter(
//                                                         option => option.value !== "other_party2"
//                                                     ));
//                                                     setDisplayParty2OtherInput(false);
//                                                     setParty2Confirmed(false);
//                                                     setLastConfirmedParty2Text("");
//                                                 }}
//                                             >
//                                                 Cancel
//                                             </Button>
//                                             <Button
//                                                 variant="primary"
//                                                 onClick={() => {
//                                                     if (!party2OtherText) {
//                                                         setParty2OtherText("Custom Party");
//                                                     }
//                                                     // 记录当前确认的值
//                                                     setLastConfirmedParty2Text(party2OtherText);
//                                                     // 标记为已确认
//                                                     setParty2Confirmed(true);
//                                                     setDisplayParty2OtherInput(false);
//                                                 }}
//                                                 disabled={!party2OtherText || party2Confirmed}
//                                             >
//                                                 Save
//                                             </Button>
//                                         </SpaceBetween>
//                                     </div>
//                                 </div>
//                             )}
//                         </div>
//                         </div>
//                     </>
//                 )}
//
//                 <div
//                     ref={multiselectContainerRef}
//                     style={{
//                         width: '220px',
//                         maxWidth: '220px',
//                         marginLeft: `${topOffset+ 19}px`,
//                         marginTop: `${topOffset}px`,
//                         position: 'relative',
//                         marginBottom: '20px'
//                     }}
//                 >
//                     <Multiselect
//                         disabled={props.running}
//                         selectedOptions={selectedOptions}
//                         onChange={handleChange}
//                         options={allOptions}
//                         enableSelectAll
//                         i18nStrings={{
//                             selectAllText: "Select All"
//                         }}
//                         placeholder="Choose options"
//                         ariaLabel="Select multiple options"
//                         expandToViewport={true}
//                         tokenLimit={0}
//                         hideTokens
//                     />
//                 </div>
//                 <div
//                     ref={buttonContainerRef}
//                     style={{
//                         marginLeft: `${topOffset + 20}px`,
//                         marginTop: `${topOffset - 9}px`,
//                         position: 'relative',
//                         marginBottom: '20px'
//                     }}
//                 >
//                     {!onlyDefaultSelected && (
//                         <button
//                             style={{
//                                 marginTop: '8px',
//                             }}
//                             className={styles.send_input_btn}
//                             onClick={applyChanges}
//                         >
//                             Submit
//                         </button>
//                     )}
//                 </div>
//             </div>
//         </div>
//     );
// };
export const MultiplePromptSuggestions: React.FC<MultiplePromptSuggestionsProps> = (props = {
    running: false,
    resetState: false
}) => {
    // this is of the layout
    const outerContainerRef = useRef(null);
    const multiselectContainerRef = useRef(null);
    const buttonContainerRef = useRef(null);
    const partyContainerRef = useRef(null);

    // Document builder options
    const documentBuilderOptions: SelectProps.Option[] = [
        {
            label: "Select",
            value: "",
            iconName: "close",
        },
        {
            label: "DUA",
            value: "dua",
            iconName: "file",
        },
    ];

    // Party1 options
    const party1Options: MultiselectProps.Option[] = [
        {...partyOptions[0], label: "Select Party 1"},
        ...partyOptions.slice(1),
        {
            label: "Other",
            value: "other_party1",
            iconName: "edit",
        }
    ];

    // Party2 options
    const party2Options: MultiselectProps.Option[] = [
        {...partyOptions[0], label: "Select Party 2"},
        ...partyOptions.slice(1),
        {
            label: "Other",
            value: "other_party2",
            iconName: "edit",
        }
    ];

    const [selectedDocumentBuilder, setSelectedDocumentBuilder] = useState<SelectProps.Option>(documentBuilderOptions[0]);

    // change to multi for party1 and party2
    // const [selectedParty1, setSelectedParty1] = useState<SelectProps.Option>(party1Options[1]);
    const [selectedParty1Options, setSelectedParty1Options] = useState<MultiselectProps.Option[]>([]);
    // const [selectedParty2, setSelectedParty2] = useState<SelectProps.Option>(party2Options[0]);
    const [selectedParty2Options, setSelectedParty2Options] = useState<MultiselectProps.Option[]>([]);

    // other option for party1 and party2
    const [party1OtherText, setParty1OtherText] = useState<string>("");
    const [party2OtherText, setParty2OtherText] = useState<string>("");

    // Track display state for Other inputs
    const [displayParty1OtherInput, setDisplayParty1OtherInput] = useState<boolean>(false);
    const [displayParty2OtherInput, setDisplayParty2OtherInput] = useState<boolean>(false);

    // 跟踪 party1,2 的 other 是否与上次的输入一样
    const [party1Confirmed, setParty1Confirmed] = useState(false);
    const [party2Confirmed, setParty2Confirmed] = useState(false);

    //跟踪 Party1 和 Party2 的"Other" 自定义文本
    const [lastConfirmedParty1Text, setLastConfirmedParty1Text] = useState("");
    const [lastConfirmedParty2Text, setLastConfirmedParty2Text] = useState("");

    // Add an effect to reset the state when resetState prop changes
    useEffect(() => {
        if (props.resetState) {
            setSelectedDocumentBuilder(documentBuilderOptions[0]);
            // change the useEffect for multi
            // setSelectedParty1(party1Options[0]);
            // setSelectedParty2(party2Options[0]);

            // setSelectedParty1Options([party1Options[0]]);
            // setSelectedParty2Options([party2Options[0]]);
            setSelectedParty1Options([]);
            setSelectedParty2Options([]);
            setParty1OtherText("");
            setParty2OtherText("");
            setDisplayParty1OtherInput(false);
            setDisplayParty2OtherInput(false);
            setParty1Confirmed(false);
            setParty2Confirmed(false);
            setLastConfirmedParty1Text("");
            setLastConfirmedParty2Text("");
        }
    }, [props.resetState]);

    // 初始状态只有必选项
    const [selectedOptions, setSelectedOptions] = useState([requiredOption]);
    // 用于存储合并后的提示词
    const [combinedPrompt, setCombinedPrompt] = useState(requiredOption.value);
    // ③这个的作用就是， 多选的默认prompt，在除了默认prompt没有选择额外的选项的时候是无法触发send或者发送给大语言模型的
    const [onlyDefaultSelected, setOnlyDefaultSelected] = useState(true);
    const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
    const containerRef = useRef(null);
    const [topOffset, setTopOffset] = useState(0);

    // 创建一个平铺的选项列表，用于确定顺序
    const flattenedOptions = allOptions.reduce((acc, group) => {
        return [...acc, ...group.options];
    }, []);

    useEffect(() => {
        const calculateOffset = () => {
            const suggestionsLabel = document.querySelector('[style*="color: #827f7c"]');
            if (suggestionsLabel && containerRef.current) {
                const suggestionRect = suggestionsLabel.getBoundingClientRect();
                const containerRect = containerRef.current.getBoundingClientRect();
                const neededOffset = suggestionRect.top - containerRect.top;
                setTopOffset(neededOffset);
            }
        };

        calculateOffset();

        window.addEventListener('resize', calculateOffset);
        return () => window.removeEventListener('resize', calculateOffset);
    }, []);
    // 这里原本是自动传给 lexical editor感觉不是很好，解决不了muti selections自动关闭的问题
    // Add effect to update prompt when document builder or parties change
    // useEffect(() => {
    //     if (selectedOptions.length > 0) {
    //         const newPrompt = combineSelections(selectedOptions);
    //         setCombinedPrompt(newPrompt);
    //
    //         // Dispatch event with updated prompt
    //         const event = new CustomEvent('multiple-prompt-change', {
    //             detail: {
    //                 prompt: newPrompt,
    //                 onlyDefaultSelected: onlyDefaultSelected
    //             }
    //         });
    //         document.dispatchEvent(event);
    //     }
    // }, [selectedDocumentBuilder, selectedParty1, selectedParty2]);
    //这里的修改是为了让选择mou的时候不会自动传递prompt给Lexical

    // Function to create a formatted Lexical state
    function createFormattedLexicalState(text: string): string {
        // Match all variations of party placeholders
        const parts = text.split(/(\[PARTY A\]|\[PARTY B\]|\[partyA\]|\[partyB\])/g);

        // Create the Lexical nodes structure
        const nodes: any[] = [];
        let currentParagraph: any = {
            type: "paragraph",
            children: []
        };

        // Process each part and handle newlines
        for (let i = 0; i < parts.length; i++) {
            const part = parts[i];

            if (!part) continue; // Skip empty parts

            // Check if this is a placeholder
            if (part === '[PARTY A]' || part === '[PARTY B]' || part === '[partyA]' || part === '[partyB]') {
                // Create a text node with both bold format and background color highlighting
                currentParagraph.children.push({
                    type: "text",
                    text: part,
                    format: 1, // 1 is the value for BOLD in Lexical
                    style: part.includes('A') || part.includes('a') ?
                        "background-color: #FFFF00;" : // Light red highlight for Party A
                        "background-color: #FFFF00;", // Light blue highlight for Party B
                    mode: 0,
                    detail: 0
                });
            } else {
                // Handle regular text with potential line breaks
                if (part.includes('\n')) {
                    // Split by newlines
                    const lines = part.split('\n');

                    for (let j = 0; j < lines.length; j++) {
                        // Add the current line
                        if (lines[j].length > 0) {
                            currentParagraph.children.push({
                                type: "text",
                                text: lines[j],
                                format: 0,
                                style: "",
                                mode: 0,
                                detail: 0
                            });
                        }

                        // If not the last line, create a new paragraph
                        if (j < lines.length - 1) {
                            // Add the current paragraph to nodes if it has children
                            if (currentParagraph.children.length > 0) {
                                nodes.push(currentParagraph);
                            }
                            // Create a new paragraph
                            currentParagraph = {
                                type: "paragraph",
                                children: []
                            };
                        }
                    }
                } else {
                    // Regular text without line breaks
                    currentParagraph.children.push({
                        type: "text",
                        text: part,
                        format: 0,
                        style: "",
                        mode: 0,
                        detail: 0
                    });
                }
            }
        }

        // Add the last paragraph if it has children
        if (currentParagraph.children.length > 0) {
            nodes.push(currentParagraph);
        }

        // Create the full Lexical state
        const lexicalState = {
            root: {
                type: "root",
                children: nodes,
                direction: null,
                format: "",
                indent: 0,
                version: 1
            }
        };

        return JSON.stringify(lexicalState);
    }

    useEffect(() => {
        if (selectedOptions.length > 0) {
            const newPrompt = combineSelections(selectedOptions);
            setCombinedPrompt(newPrompt);

            // Set unsaved changes flag if document or party selection changes
            if (selectedDocumentBuilder.value ||
                // selectedParty1.value !== "" || selectedParty2.value !== "")
                // changed to multi
                (selectedParty1Options.length > 0 && selectedParty1Options[0].value !== "") ||
                (selectedParty2Options.length > 0 && selectedParty2Options[0].value !== ""))
            {
                setHasUnsavedChanges(true);
            }
        }
    },
        // [selectedDocumentBuilder, selectedParty1, selectedParty2, selectedOptions]
        [selectedDocumentBuilder, selectedParty1Options, selectedParty2Options, selectedOptions, party1OtherText, party2OtherText]
    );

    // 当选择变化时，组合所有选择项为一个提示词，并按定义顺序排序
    const combineSelections = (selections) => {
        // 首先确保必选项（1.0 General Terms）总是排在第一位
        let sortedSelections = [...selections];

        // 从排序后的数组中移除必选项，因为它将被单独处理
        sortedSelections = sortedSelections.filter(option => option.label !== requiredOption.label);

        // 根据 flattenedOptions 中的顺序排序其他选择项
        sortedSelections.sort((a, b) => {
            const indexA = flattenedOptions.findIndex(opt => opt.label === a.label);
            const indexB = flattenedOptions.findIndex(opt => opt.label === b.label);
            return indexA - indexB;
        });

        // 首先添加必选项的值（不包含标签）
        let prompt = requiredOption.value;

        // 替换party1 party2 单个版本
        // if (selectedParty1.value) {
        //     prompt = prompt.replace(/\[PARTY A\]/g, selectedParty1.label);
        // }
        //
        // if (selectedParty2.value) {
        //     prompt = prompt.replace(/\[PARTY B\]/g, selectedParty2.label);
        // }
        // 替换party1 - 多选版本，用逗号连接
        // if (selectedParty1Options.length > 0) {
        //     // Filter out the "Select Party 1" option if it's there
        //     const actualParties = selectedParty1Options.filter(party =>
        //         party.value !== "" && party.value !== "select_all_party1"
        //     );
        if (selectedParty1Options.length > 0) {
            const labels = selectedParty1Options
                .filter(party => party.value !== "" && party.value !== "select_all_party1")
                .map(party =>
                    (party.value === "other_party1") ? party1OtherText || null : party.label
                )
                .filter(Boolean); // 过滤掉null、undefined和空字符串

            if (labels.length > 0) {
                prompt = prompt.replace(/\[PARTY A\]/g, labels.join(", "));
            }
        }
        // if (selectedParty1Options && selectedParty1Options.length > 0) {
        //     // Filter out the "Select Party 1" option if it's there
        //     const actualParties = selectedParty1Options.filter(party =>
        //         party.value !== "" && party.value !== "select_all_party1"
        //     );
        //
        //     if (actualParties.length > 0) {
        //         let party1Labels = actualParties.map(party => {
        //             // 如果是"Other"选项且有自定义文本，则使用自定义文本
        //             if (party.value === "other_party1" && party1OtherText) {
        //                 return party1OtherText;
        //             }
        //             return party.label;
        //         }).join(", ");
        //         prompt = prompt.replace(/\[PARTY A\]/g, party1Labels);
        //     }
        // }

        // 替换party2 - 多选版本，用逗号连接
        // if (selectedParty2Options.length > 0) {
        //     // Filter out the "Select Party 2" option if it's there
        //     const actualParties = selectedParty2Options.filter(party =>
        //         party.value !== "" && party.value !== "select_all_party2"
        //     );
        //
        //     if (actualParties.length > 0) {
        //         let party2Labels = actualParties.map(party => {
        //             // 如果是"Other"选项且有自定义文本，则使用自定义文本
        //             if (party.value === "other_party2" && party2OtherText) {
        //                 return party2OtherText;
        //             }
        //             return party.label;
        //         }).join(", ");
        //         prompt = prompt.replace(/\[PARTY B\]/g, party2Labels);
        //     }
        // }
        if (selectedParty2Options.length > 0) {
            const labels = selectedParty2Options
                .filter(party => party.value !== "" && party.value !== "select_all_party2")
                .map(party =>
                    (party.value === "other_party2") ? party2OtherText || null : party.label
                )
                .filter(Boolean); // 过滤掉null、undefined和空字符串

            if (labels.length > 0) {
                prompt = prompt.replace(/\[PARTY B\]/g, labels.join(", "));
            }
        }

        // 然后按排序后的顺序添加其他选项的标签和值
        sortedSelections.forEach(option => {
            prompt += `\n${option.label}\n${option.value}`;
        });
        return prompt;
    };
    // track if there are unsaved changes
    // const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

    // Handle multiselect change
    const handleChange = ({detail}) => {
        // 确保必选项总是包含在选择中
        const isRequiredOptionIncluded = detail.selectedOptions.some(
            option => option.label === requiredOption.label
        );

        let newSelectedOptions = [...detail.selectedOptions];

        if (!isRequiredOptionIncluded) {
            newSelectedOptions = [requiredOption, ...newSelectedOptions];
        }

        // 检查是否只选择了默认选项
        const hasOnlyDefaultOption = newSelectedOptions.length === 1 &&
            newSelectedOptions[0].label === requiredOption.label;

        setOnlyDefaultSelected(hasOnlyDefaultOption);
        setSelectedOptions(newSelectedOptions);

        // 组合所有选择项为一个提示词，按定义的顺序排序
        const newCombinedPrompt = combineSelections(newSelectedOptions);
        setCombinedPrompt(newCombinedPrompt);

        // 关键修改：使用自定义事件通知 ChatInputPanel 已经有新的组合提示词
        // 同时传递是否只选择了默认选项的信息
        //     const event = new CustomEvent('multiple-prompt-change', {
        //         detail: {
        //             prompt: newCombinedPrompt,
        //             onlyDefaultSelected: hasOnlyDefaultOption
        //         }
        //     });
        //     document.dispatchEvent(event);
        //
        //     console.log("Combined prompt:", newCombinedPrompt);
        //     console.log("Only default selected:", hasOnlyDefaultOption);
        // };
        // Mark that we have unsaved changes
        setHasUnsavedChanges(true);
    };

    // New function to apply changes with formatting
    const applyChanges = () => {
        // 设置活动提示系统，multi 无论如何都会发送
        window.dispatchEvent(new CustomEvent('set-active-prompt-system', {
            detail: {
                system: 'multiple'
            }
        }));
        // ①Create the formatted Lexical state with highlighted placeholders
        const formattedLexicalState = createFormattedLexicalState(combinedPrompt);

        // Dispatch event to update the Lexical editor
        const event = new CustomEvent('multiple-prompt-change', {
            detail: {
                prompt: combinedPrompt,
                onlyDefaultSelected: onlyDefaultSelected,
                lexicalState: formattedLexicalState // Add the formatted state
            }
        });
        document.dispatchEvent(event);

        // Reset the unsaved changes flag
        setHasUnsavedChanges(false);
    };

    return (
        <div
            ref={outerContainerRef}
            style={{
                marginLeft: `${topOffset}px`,
                marginTop: `${topOffset + 21}px`,
                position: 'relative',
                marginBottom: '20px'
            }}
        >
            <div className={styles.document_library_container} style={{display: 'flex', alignItems: 'start'}}>
                {/* Document builder 组 - 水平布局 */}
                <div style={{display: 'flex', alignItems: 'center'}}>
                    <FormField label="Document builder"/>
                    <span>&nbsp;&nbsp;</span>
                    <Select
                        disabled={props.running}
                        placeholder="Select document type"
                        selectedOption={selectedDocumentBuilder}
                        options={documentBuilderOptions}
                        onChange={({detail}) => {
                            setSelectedDocumentBuilder(detail.selectedOption);
                            // Reset party selections when document type changes for multi
                            // setSelectedParty1(party1Options[0]);
                            // setSelectedParty2(party2Options[0]);
                            // setSelectedParty1Options([party1Options[0]]);
                            // setSelectedParty2Options([party2Options[0]]);
                            setSelectedParty1Options([]);
                            setSelectedParty2Options([]);
                            setParty1OtherText("");
                            setParty2OtherText("");
                        }}
                    />
                </div>

                {/* Only show Party1 and Party2 fields when MOU is selected */}
                {selectedDocumentBuilder.value === "dua" && (
                    <>
                        {/* Party 1 组 - 水平布局 */}
                        {/*<div style={{display: 'flex', alignItems: 'left'}}>*/}
                        {/*<FormField label="Party 1"/>*/}
                        {/*<Select*/}
                        {/*    disabled={props.running}*/}
                        {/*    placeholder="Select Party 1"*/}
                        {/*    selectedOption={selectedParty1}*/}
                        {/*    options={party1Options}*/}
                        {/*    onChange={({detail}) => setSelectedParty1(detail.selectedOption)}*/}
                        {/*/>*/}
                        {/*changed to multi*/}
                        <div
                            ref={partyContainerRef}
                            style={{
                                width: '220px',
                                maxWidth: '220px',
                                marginLeft: `${topOffset+ 19}px`,
                                marginTop: `${topOffset}px`,
                                position: 'relative',
                                marginBottom: '20px'
                            }}
                        >
                            <Multiselect
                                disabled={props.running}
                                placeholder="Select Party 1"
                                selectedOptions={selectedParty1Options}
                                options={party1Options.slice(1)}
                                enableSelectAll
                                onChange={({detail}) => {
                                    // 检查之前是否已选择了Other选项
                                    const previouslySelectedOther = selectedParty1Options.some(
                                        opt => opt.value === "other_party1"
                                    );

                                    handleMultiselectChange(
                                        detail,
                                        selectedParty1Options,
                                        setSelectedParty1Options,
                                        party1Options,
                                        "other_party1",
                                        setDisplayParty1OtherInput,
                                        previouslySelectedOther
                                    );
                                }}
                                i18nStrings={{
                                    selectAllText: "Select All"
                                }}
                                tokenLimit={0}
                            />
                            {/* Party 1 的Autosuggest */}
                            {selectedParty1Options.some(opt => opt.value === "other_party1") && (
                                <div style={{
                                    marginTop: '10px',
                                    width: '100%',
                                    maxWidth: '200px'
                                }}>
                                    <Autosuggest
                                        value={party1OtherText}
                                        onChange={({detail}) => {
                                            // 更新Party1的文本
                                            setParty1OtherText(detail.value);
                                            // 如果输入内容与上次确认的不同，允许再次确认
                                            if (detail.value !== lastConfirmedParty1Text) {
                                                setParty1Confirmed(false);
                                            }
                                        }}
                                        onSelect={({detail}) => {
                                            if (detail.value) {
                                                setParty1OtherText(detail.value);
                                                // 如果选择的值与上次确认的不同，允许再次确认
                                                if (detail.value !== lastConfirmedParty1Text) {
                                                    setParty1Confirmed(false);
                                                }
                                            }
                                        }}
                                        options={partyOptions.slice(1)}
                                        enteredTextLabel={value => `Use: "${value}"`}
                                        placeholder="Type a custom name"
                                        empty="Begin typing to create a custom name"
                                        ariaLabel="Custom party name input"
                                        expandToViewport={true}
                                        onBlur={() => {
                                            if (!party1OtherText) {
                                                setSelectedParty1Options(selectedParty1Options.filter(
                                                    option => option.value !== "other_party1"
                                                ));
                                                setDisplayParty1OtherInput(false);
                                            }
                                        }}
                                        filteringType="manual"
                                    />
                                    <div style={{
                                        display: 'flex',
                                        justifyContent: 'flex-end',
                                        marginTop: '10px'
                                    }}>
                                        <SpaceBetween direction="horizontal" size="xs">
                                            <Button
                                                variant="link"
                                                onClick={() => {
                                                    setParty1OtherText("");
                                                    setSelectedParty1Options(selectedParty1Options.filter(
                                                        option => option.value !== "other_party1"
                                                    ));
                                                    setDisplayParty1OtherInput(false);
                                                    setParty1Confirmed(false);
                                                    setLastConfirmedParty1Text("");
                                                }}
                                            >
                                                Cancel
                                            </Button>
                                            <Button
                                                variant="primary"
                                                onClick={() => {
                                                    if (!party1OtherText) {
                                                        setParty1OtherText("Custom Party");
                                                    }
                                                    // 记录当前确认的值
                                                    setLastConfirmedParty1Text(party1OtherText);
                                                    // 标记为已确认
                                                    setParty1Confirmed(true);
                                                    setDisplayParty1OtherInput(false);
                                                }}
                                                disabled={!party1OtherText || party1Confirmed}
                                            >
                                                Save
                                            </Button>
                                        </SpaceBetween>
                                    </div>
                                </div>
                            )}
                        </div>

                        {/* Party 2 组 - 水平布局 */}
                        <div style={{display: 'flex', alignItems: 'center'}}>
                            <div
                                ref={partyContainerRef}
                                style={{
                                    width: '220px',
                                    maxWidth: '220px',
                                    marginLeft: `${topOffset+ 19}px`,
                                    marginTop: `${topOffset}px`,
                                    position: 'relative',
                                    marginBottom: '20px'
                                }}
                            >
                                <Multiselect
                                    disabled={props.running}
                                    placeholder="Select Party 2"
                                    selectedOptions={selectedParty2Options}
                                    options={party2Options.slice(1)}
                                    onChange={({detail}) => {
                                        // 检查之前是否已选择了Other选项
                                        const previouslySelectedOther = selectedParty2Options.some(
                                            opt => opt.value === "other_party2"
                                        );

                                        handleMultiselectChange(
                                            detail,
                                            selectedParty2Options,
                                            setSelectedParty2Options,
                                            party2Options,
                                            "other_party2",
                                            setDisplayParty2OtherInput,
                                            previouslySelectedOther
                                        );
                                    }}
                                    tokenLimit={0}
                                    enableSelectAll
                                    i18nStrings={{
                                        selectAllText: "Select All"
                                    }}
                                />
                                {selectedParty2Options.some(opt => opt.value === "other_party2") && (
                                    <div style={{
                                        marginTop: '10px',
                                        width: '100%',
                                        maxWidth: '400px'
                                    }}>
                                        <Autosuggest
                                            value={party2OtherText}
                                            onChange={({detail}) => {
                                                // 更新Party2的文本
                                                setParty2OtherText(detail.value);
                                                // 如果输入内容与上次确认的不同，允许再次确认
                                                if (detail.value !== lastConfirmedParty2Text) {
                                                    setParty2Confirmed(false);
                                                }
                                            }}
                                            onSelect={({detail}) => {
                                                if (detail.value) {
                                                    setParty2OtherText(detail.value);
                                                    // 如果选择的值与上次确认的不同，允许再次确认
                                                    if (detail.value !== lastConfirmedParty2Text) {
                                                        setParty2Confirmed(false);
                                                    }
                                                }
                                            }}
                                            options={partyOptions.slice(1)}
                                            enteredTextLabel={value => `Use: "${value}"`}
                                            placeholder="Type a custom name"
                                            empty="Begin typing to create a custom name"
                                            ariaLabel="Custom party name input"
                                            expandToViewport={true}
                                            onBlur={() => {
                                                if (!party2OtherText) {
                                                    setSelectedParty2Options(selectedParty2Options.filter(
                                                        option => option.value !== "other_party2"
                                                    ));
                                                    setDisplayParty2OtherInput(false);
                                                }
                                            }}
                                            filteringType="manual"
                                        />
                                    <div style={{
                                        display: 'flex',
                                        justifyContent: 'flex-end',
                                        marginTop: '10px'
                                    }}>
                                        <SpaceBetween direction="horizontal" size="xs">
                                            <Button
                                                variant="link"
                                                onClick={() => {
                                                    setParty2OtherText("");
                                                    setSelectedParty2Options(selectedParty2Options.filter(
                                                        option => option.value !== "other_party2"
                                                    ));
                                                    setDisplayParty2OtherInput(false);
                                                    setParty2Confirmed(false);
                                                    setLastConfirmedParty2Text("");
                                                }}
                                            >
                                                Cancel
                                            </Button>
                                            <Button
                                                variant="primary"
                                                onClick={() => {
                                                    if (!party2OtherText) {
                                                        setParty2OtherText("Custom Party");
                                                    }
                                                    // 记录当前确认的值
                                                    setLastConfirmedParty2Text(party2OtherText);
                                                    // 标记为已确认
                                                    setParty2Confirmed(true);
                                                    setDisplayParty2OtherInput(false);
                                                }}
                                                disabled={!party2OtherText || party2Confirmed}
                                            >
                                                Save
                                            </Button>
                                        </SpaceBetween>
                                    </div>
                                </div>
                            )}
                        </div>
                        </div>
                    </>
                )}

                <div
                    ref={multiselectContainerRef}
                    style={{
                        width: '220px',
                        maxWidth: '220px',
                        marginLeft: `${topOffset+ 19}px`,
                        marginTop: `${topOffset}px`,
                        position: 'relative',
                        marginBottom: '20px'
                    }}
                >
                    <Multiselect
                        disabled={props.running}
                        selectedOptions={selectedOptions}
                        onChange={handleChange}
                        options={allOptions}
                        enableSelectAll
                        i18nStrings={{
                            selectAllText: "Select All"
                        }}
                        placeholder="Choose options"
                        ariaLabel="Select multiple options"
                        expandToViewport={true}
                        tokenLimit={0}
                        hideTokens
                    />
                </div>
                <div
                    ref={buttonContainerRef}
                    style={{
                        marginLeft: `${topOffset + 20}px`,
                        marginTop: `${topOffset - 9}px`,
                        position: 'relative',
                        marginBottom: '20px'
                    }}
                >
                    {!onlyDefaultSelected && (
                        <button
                            style={{
                                marginTop: '8px',
                            }}
                            className={styles.send_input_btn}
                            onClick={applyChanges}
                        >
                            Submit
                        </button>
                    )}
                </div>
            </div>
        </div>
    );
};
// 默认导出 PromptSuggestions
export default PromptSuggestions;
