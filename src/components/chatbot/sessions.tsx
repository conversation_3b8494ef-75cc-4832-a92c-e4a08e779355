import {
  Box,
  SpaceBet<PERSON>en,
  Table,
  Pa<PERSON>ation,
  <PERSON>ton,
  TableProps,
} from "@cloudscape-design/components";
import { DateTime } from "luxon";
import { useState, useEffect, useContext, useCallback } from "react";
import { Link } from "react-router-dom";
import { v4 as uuidv4 } from "uuid";
import { useCollection } from "@cloudscape-design/collection-hooks";
import RouterButton from "../wrappers/router-button";
import { Session } from "../../API";
import sessionService from "../../services/session.service";
import { useOktaAuth } from "@okta/okta-react";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store/configureStore";

export interface SessionsProps {
  toolsOpen: boolean;
}

export default function Sessions(props: SessionsProps) {
  const [sessions, setSessions] = useState<Session[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const dataState = useSelector((state: any) => state?.rootReducer);
  const isFeedbackOpened = dataState?.chatReducer?.isFeedbackOpened;
  const userEmail = dataState?.chatReducer?.userEmail;
  // const { authState, oktaAuth } = useOktaAuth();

  const { items, collectionProps, paginationProps } = useCollection(sessions, {
    filtering: {
      empty: (
        <Box margin={{ vertical: "xs" }} textAlign="center" color="inherit">
          <SpaceBetween size="m">
            <b>No sessions</b>
          </SpaceBetween>
        </Box>
      ),
    },
    pagination: { pageSize: 18 },
    sorting: {
      defaultState: {
        sortingColumn: {
          sortingField: "startTime",
        },
        isDescending: true,
      },
    },
    selection: {},
  });

  /**
   * Get all sessions information for this user
   */
  const getSessions = useCallback(async () => {
    try {
      // await oktaAuth.getUser().then(async (user: any) => {
        
      const userId: string = userEmail;
        // const userId: string = "<EMAIL>";
        if (userId !== "") {
          const result = await sessionService.listSessions(userId);
          setSessions(result!);
        } else {
          console.log("Error: User id for Session is not found");
        }
      // });
    } catch (e) {
      console.log(e);
      setSessions([]);
    }
  }, []);

  /**
   * Get all sessions information for this user
   */
  useEffect(() => {
    if (!props.toolsOpen || !userEmail) return;

    const loadSessions = async () => {
      setIsLoading(true);
      try {
        const result = await sessionService.listSessions(userEmail);
        setSessions(result || []);
      } catch (e) {
        console.error(e);
        setSessions([]);
      }
      setIsLoading(false);
    };

    loadSessions();
  }, [props.toolsOpen, userEmail]);

  /**
   * Delete a session by id
   * @param sessionId
   * @returns
   */
  const deleteSession = async (sessionId: string) => {
    setIsLoading(true);
    // await oktaAuth.getUser().then(async (user: any) => {
      const userId: string = userEmail;
      // const userId: string = "<EMAIL>";
      if (userId !== "") {
        await sessionService.deleteSession(sessionId, userId);
        await getSessions();
        setIsLoading(false);
      } else {
        console.log("Error: User id for Session is not found");
        setIsLoading(false);
      }
    // });
  };

  /**
   * Delete all user sessions for this user
   * @returns
   */
  const deleteUserSessions = async () => {
    if (!confirm("Are you sure you want to delete all sessions?")) return;

    setIsLoading(true);
    // await oktaAuth.getUser().then(async (user: any) => {
      const userId: string = userEmail;
      // const userId: string = "<EMAIL>";
      if (userId !== "") {
        await sessionService.deleteSessions(userId);
        await getSessions();
        setIsLoading(false);
      } else {
        console.log("Error: User id for Session is not found");
        setIsLoading(false);
      }
    // });
  };

  /**
   * Get User information
   */
  // useEffect(() => {
  //   (async () => {
  //     if (!authState?.isAuthenticated) {
  //       // setUserInfo(null);
  //     } else {
  //     }
  //   })();
  // }, []);

  /**
   *
   */
  useEffect(() => {
    if (!props.toolsOpen) return;

    (async () => {
      setIsLoading(true);
      await getSessions();
      setIsLoading(false);
    })();
  }, [getSessions, props.toolsOpen]);

  /**
   * Render the component
   */
  return (
    <div style={{ padding: "0px 14px" }}>
      <Table
        {...collectionProps}
        variant="embedded"
        items={items}
        pagination={<Pagination {...paginationProps} />}
        loadingText="Loading history"
        loading={isLoading}
        resizableColumns
        header={
          <div style={{ paddingTop: "4px" }}>
            <h2>History</h2>
            <div>
              <SpaceBetween direction="horizontal" size="m" alignItems="center">
                <RouterButton
                  iconName="add-plus"
                  href={`/chatbot/playground/${uuidv4()}`}
                  variant="inline-link"
                >
                  New session
                </RouterButton>
                <Button
                  iconAlt="Refresh list"
                  iconName="refresh"
                  variant="inline-link"
                  onClick={() => getSessions()}
                >
                  Refresh
                </Button>
                {/* <Button
                  iconAlt="Delete all sessions"
                  iconName="delete-marker"
                  variant="inline-link"
                  onClick={() => deleteUserSessions()}
                >
                  Delete all sessions
                </Button> */}
              </SpaceBetween>
            </div>
          </div>
        }
        columnDefinitions={
          [
            {
              id: "title",
              header: "Title",
              sortingField: "title",
              cell: (e) => (
                <Link to={`/chatbot/playground/${e.id}`}>{e.title}</Link>
              ),
              isRowHeader: true,
            },
            {
              id: "startTime",
              header: "Time",
              sortingField: "startTime",
              cell: (e: Session) =>
                DateTime.fromISO(
                  new Date(e.startTime).toISOString()
                ).toLocaleString(DateTime.DATETIME_SHORT),
              sortingComparator: (a, b) => {
                return (
                  new Date(b.startTime).getTime() -
                  new Date(a.startTime).getTime()
                );
              },
            },
            {
              id: "open",
              header: "Open",
              cell: (item) => (
                <RouterButton
                  variant="inline-link"
                  href={`/chatbot/playground/${item.id}`}
                >
                  Open
                </RouterButton>
              ),
            },
            // {
            //   id: "delete",
            //   header: "Delete",
            //   cell: (item) => (
            //     <Button
            //       variant="inline-link"
            //       onClick={() => deleteSession(item.id)}
            //     >
            //       Delete
            //     </Button>
            //   ),
            // },
          ] as TableProps.ColumnDefinition<Session>[]
        }
      />
    </div>
  );
}

