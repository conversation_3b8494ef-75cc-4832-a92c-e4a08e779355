// suggestion-prompts.tsx

export const suggestedPrompts = [
    {
        option: 'Generate a customized DUA ',
        promptText:
`Generate a comprehensive Data Use Agreement (DUA) template for data sharing between [PARTY A] and [PARTY B].
This template must strictly follow the structure and contents outlined below.
Do not omit or summarize any section from 1.0 to 10.0.
Ensure every subsection is generated with clear section numbering, formal legal tone, and all placeholders in [BRACKETS] for easy customization.
Ensure the final section includes a full table capturing dataset change tracking as specified.
Template Structure
Title Page
Include a title that clearly states this is a Data Use Agreement
Identify the organizations entering into the agreement
Name the specific data or systems involved
Include version number and date
Add logo placeholder for the originating organization
Version History Table
Include a version history table with the following columns:
Version Number
Written By
Revision Date
Approved By
Approval Date
Description of Change
Table of Contents Generate a comprehensive Table of Contents covering all sections below including appendices and addendum.
1.0 General Terms
1.1 Parties to Agreement
Language identifying the organizations and individuals entering the agreement, including full legal names and a table for contact information.
1.2 Period of Agreement
Define effective dates, renewal terms, and notification procedures.
1.3 Scope of Agreement
Describe the high-level purpose, involved systems, and types of data. Use [BRACKETS] for placeholders.
1.4 Authority to Share Data
Cite relevant legal or regulatory basis, with placeholders for jurisdiction-specific laws.
1.5 Modification
Describe how changes must be proposed, documented, and agreed upon.
1.6 Termination
Outline termination processes for both cause and no cause, with data return/destruction procedures.
1.7 Violation of Terms
Specify reporting timelines, processes, and potential consequences.
1.8 Indemnification
Assign responsibility for claims, subject to federal/state law.
1.9 Acknowledgements
Include affirmation of authority, understanding, and compliance.
1.10 Roles and Responsibilities
Detail key roles and contacts (e.g., project officer, data custodian), with placeholders in a table format.
1.11 Funding
Outline any financial terms or payments, with [BRACKETS] for dollar amounts.
1.12 Other Common Terms
Include clauses on: assignment, dispute resolution, captions, choice of law, costs and damages, counterparts, entire agreement, flow-down, order of precedence, independent entities, severability, survival, prohibition on third party beneficiaries, public availability.
2.0 Purpose/Use Case
Clearly define the context, objectives, analytic methods, and expected outcomes.
3.0 Scope of Data
3.1 Data Description
High-level overview of data, with status of PII/PHI/BII, federal program source, and reference to detailed appendix of data specifications.
3.2 Data Ownership
State ownership rights and data use limitations.
3.3 Service Level
Include: frequency, transmission methods, platforms/tools/support, data standards.
3.3.2 Data Quality
Specify: quality assurances or limitations, bias mitigation, recommended analytic methods, handling of transmission errors.
4.0 Data Controls
4.1 Disclosure and Use
Cover: purpose limitations, consent, legal compliance, role-based access, user logs, notification for compelled disclosure, IRB requirements.
4.2 Control of Identifiable Data
Prohibit identifiable data sharing except under well-defined exceptions.
4.3 Data Deidentification
4.3.1 Person — standards and anti-reidentification practices.
4.3.2 Organization — similar requirements for org-level data.
4.4 Data Sharing
4.4.1 Data Linkage — disallow unauthorized linkage.
4.4.2 Data Reuse — prohibit unauthorized reuse.
4.4.3 Data Redisclosure — prohibit redisclosure; outline exceptions (e.g., FOIA); define redisclosure authorization and violation handling.
******* Identifiable Data as De-identified Data — prevent publication or sharing that allows re-identification.
4.5 Data Disposition
Specify retention time, destruction method, and certification.
4.6 Publication
Define data provider approval process and attribution requirements.
5.0 Security
5.1 General — Include broad safeguard requirements.
5.2 Data Access Controls — Limit access to authorized users; include remote user policies.
5.3 Network Access — Require secure networking practices.
5.4 Physical Access — Specify facility and storage controls.
5.5 Transmission and Storage — Require encryption and other security safeguards.
5.6 Authority to Operate — Align with federal control standards.
5.7 Cloud Computing — Require FedRAMP authorization and approval.
5.8 Incidents Including Reporting — define breaches, set reporting procedures and timelines, require investigation records and remediation.
5.9 Audit — Enable inspection rights to verify compliance.
6.0 Reporting Requirements
Define types, frequency, and content of required reports.
7.0 Applicable Law and Regulations
List relevant laws and describe their impact on data handling.
8.0 Signatures
Include signature blocks with: Name, Title, Organization, Date.
9.0 Appendices
Include placeholders for:
Detailed data documentation
Technical specifications
FedRAMP or other certifications
10.0 Addendum Template
Create a structured table format to track dataset change requests.
IMPORTANT: The following must be included at the end of the document as a table and should be clearly labeled and properly formatted:
Appendix: Data Change Tracking Table
| Date of Data Change Request (MM/DD/YYYY) | Target Date of Completion (MM/DD/YYYY) | Data Change Requestor(s) | Brief Description of Data Change Request | Reason for Data Change Request | Special Conditions/Stipulations, if any | [PARTY A] Signature | [PARTY B] Signature |
Formatting Guidelines
Use clear section numbering (e.g., 1.1, 1.2, etc.)
Use [BRACKETS] for all placeholders
Use table format for contact info and signature blocks
Maintain professional formatting (spacing, headers/footers, page breaks)
Apply precise and legally appropriate language
Ensure compliance with federal data security standards
Document should be suitable for intra-agency or inter-agency use
Do not omit or summarize any subsection
Add "This DUA will be due for an annual review on MM/DD/YYYY" to the end of the response
`}
// please response with markdown format without starting with \`\`\`markdown`}
];

// might not in use
export type SuggestedPrompt = {
    option: string;
    promptText: string;
};