import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext";
import { useCallback, useEffect, useState } from "react";
import {
    $getSelection,
    $isRangeSelection,
    $createParagraphNode,
    FORMAT_TEXT_COMMAND,
    FORMAT_ELEMENT_COMMAND,
    $getNodeByKey,
} from "lexical";
import { $isListNode, ListNode } from "@lexical/list";
import {
    $isHeadingNode,
    $createHeadingNode,
    HeadingNode,
    HeadingTagType
} from "@lexical/rich-text";
import {
    INSERT_ORDERED_LIST_COMMAND,
    INSERT_UNORDERED_LIST_COMMAND,
    REMOVE_LIST_COMMAND,
} from "@lexical/list";

import './toolbar-plugin.scss';

export function ToolbarPlugin() {
    const [editor] = useLexicalComposerContext();
    const [isBold, setIsBold] = useState(false);
    const [isItalic, setIsItalic] = useState(false);
    const [isUnderline, setIsUnderline] = useState(false);
    const [isList, setIsList] = useState(false);
    const [isOrderedList, setIsOrderedList] = useState(false);
    const [isHeading, setIsHeading] = useState(false);
    const [headingLevel, setHeadingLevel] = useState<number>(0);

    // 检查文本格式状态
    const updateToolbar = useCallback(() => {
        const selection = $getSelection();
        if ($isRangeSelection(selection)) {
            // 更新文本格式
            setIsBold(selection.hasFormat('bold'));
            setIsItalic(selection.hasFormat('italic'));
            setIsUnderline(selection.hasFormat('underline'));

            // 获取选区父节点
            const anchorNode = selection.anchor.getNode();

            let element = anchorNode.getParent();

            // 检查列表状态
            const isInList = $isListNode(element) || (element && $isListNode(element.getParent()));
            const isInOrderedList = isInList && element && element.getParent() &&
                $isListNode(element.getParent()) &&
                // 类型安全地检查标签
                (element.getParent() as ListNode).getTag() === 'ol';

            setIsList(isInList);
            setIsOrderedList(isInOrderedList);

            // 检查标题状态
            const isInHeading = element && $isHeadingNode(element);
            if (isInHeading) {
                setIsHeading(true);
                // 从标签中提取级别数字，例如 'h1' -> 1
                const tag = (element as HeadingNode).getTag();
                setHeadingLevel(parseInt(tag.charAt(1)));
            } else {
                setIsHeading(false);
                setHeadingLevel(0);
            }
        }
    }, [editor]);

    // 注册更新事件
    useEffect(() => {
        return editor.registerUpdateListener(({ editorState }) => {
            editorState.read(() => {
                updateToolbar();
            });
        });
    }, [editor, updateToolbar]);

    // 按钮动作处理函数
    const formatBold = () => {
        editor.dispatchCommand(FORMAT_TEXT_COMMAND, 'bold');
    };

    const formatItalic = () => {
        editor.dispatchCommand(FORMAT_TEXT_COMMAND, 'italic');
    };

    const formatUnderline = () => {
        editor.dispatchCommand(FORMAT_TEXT_COMMAND, 'underline');
    };

    const formatBulletList = () => {
        if (isList && !isOrderedList) {
            editor.dispatchCommand(REMOVE_LIST_COMMAND, undefined);
        } else {
            editor.dispatchCommand(INSERT_UNORDERED_LIST_COMMAND, undefined);
        }
    };

    const formatOrderedList = () => {
        if (isList && isOrderedList) {
            editor.dispatchCommand(REMOVE_LIST_COMMAND, undefined);
        } else {
            editor.dispatchCommand(INSERT_ORDERED_LIST_COMMAND, undefined);
        }
    };

    const formatHeading = (level: number) => {
        editor.update(() => {
            const selection = $getSelection();
            if ($isRangeSelection(selection)) {
                const anchorNode = selection.anchor.getNode();
                const anchorNodeParent = anchorNode.getParent();

                // 使用类型安全的方式处理标题
                if ($isHeadingNode(anchorNodeParent)) {
                    const headingNode = anchorNodeParent as HeadingNode;
                    const currentLevel = parseInt(headingNode.getTag().charAt(1));

                    if (currentLevel === level) {
                        // 如果已经是该级别的标题，则切换回段落
                        const paragraph = $createParagraphNode();
                        headingNode.replace(paragraph);
                        paragraph.select();
                    } else {
                        // 切换到不同级别的标题
                        const tag = `h${level}` as HeadingTagType; // 正确的类型断言
                        const heading = $createHeadingNode(tag);
                        headingNode.replace(heading);
                        heading.select();
                    }
                } else {
                    // 创建新的标题节点
                    const tag = `h${level}` as HeadingTagType; // 正确的类型断言
                    const heading = $createHeadingNode(tag);
                    if (anchorNodeParent) {
                        anchorNodeParent.insertBefore(heading);
                        anchorNodeParent.remove();
                    }
                    heading.select();
                }
            }
        });
    };

    return (
        <div className="toolbar">
            <button
                onClick={formatBold}
                className={isBold ? 'active' : ''}
                title="Bold"
            >
                <i className="format-icon bold-icon">B</i>
            </button>
            <button
                onClick={formatItalic}
                className={isItalic ? 'active' : ''}
                title="isItalic"
            >
                <i className="format-icon italic-icon">I</i>
            </button>
            <button
                onClick={formatUnderline}
                className={isUnderline ? 'active' : ''}
                title="Underline"
            >
                <i className="format-icon underline-icon">U</i>
            </button>
            <div className="divider"></div>
            <button
                onClick={formatBulletList}
                className={isList && !isOrderedList ? 'active' : ''}
                title="Unordered List"
            >
                <i className="format-icon list-icon">•</i>
            </button>
            <button
                onClick={formatOrderedList}
                className={isList && isOrderedList ? 'active' : ''}
                title="Ordered List"
            >
                <i className="format-icon ordered-list-icon">1.</i>
            </button>
            <div className="divider"></div>
            <button
                onClick={() => formatHeading(1)}
                className={isHeading && headingLevel === 1 ? 'active' : ''}
                title="Heading 1"
            >
                <i className="format-icon heading-icon">H1</i>
            </button>
            <button
                onClick={() => formatHeading(2)}
                className={isHeading && headingLevel === 2 ? 'active' : ''}
                title="Heading 2"
            >
                <i className="format-icon heading-icon">H2</i>
            </button>
        </div>
    );
}