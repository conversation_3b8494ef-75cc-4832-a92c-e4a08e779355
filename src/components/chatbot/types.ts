import { Model, Workspace } from "../../API";
import { LoadingStatus, ModelInterface } from "../../common/types";
import { SelectProps } from "@cloudscape-design/components";
import * as React from "react";
import {
  Dispatch,
  SetStateAction,
} from "react";

export interface ChatBotConfiguration {
  streaming: boolean;
  useHistory: boolean;
  followPromptId: string;
  showSource: boolean;
  showMetadata: boolean;
  maxTokens: number;
  temperature: number;
  topP: number;
  numDocs: number;
  files: ImageFile[] | null;
}

export interface ChatBotPromptConfiguration {
  id?: string;
  name: string;
  description: string;
  prompt_type: string;
  tags: string;
  created_at?: string;
  prompt?: string;
}

export interface ChatBotVectorDBResponseItem {
  pages: string;
  source: string;
  score: number;
  content: string;
}

export interface ChatInputState {
  value: string;
  workspaces?: Workspace[];
  models?: Model[];
  prompts?: any[];
  selectedModel: SelectProps.Option | null;
  selectedModelMetadata: Model | null;
  selectedWorkspace: SelectProps.Option | null;
  selectedPrompt: SelectProps.Option | null;
  modelsStatus: LoadingStatus;
  workspacesStatus: LoadingStatus;
  promptsStatus: LoadingStatus;
}

export interface ConfigDialogState {
  value: string;
  prompts?: any[];
  selectedPrompt: SelectProps.Option | null;
  promptsStatus: LoadingStatus;
}

export interface PromptPlaygroundState {
  value: string;
  models?: Model[];
  selectedModel: SelectProps.Option | null;
  selectedModelMetadata: Model | null;
  modelsStatus: LoadingStatus;
}

export enum ChatBotMessageType {
  AI = "ai",
  Human = "human",
}

export enum ChatBotAction {
  Heartbeat = "heartbeat",
  Run = "run",
  FinalResponse = "final_response",
  LLMNewToken = "llm_new_token",
  Error = "error",
}

export enum ChatBotModelInterface {
  Langchain = "langchain",
  Idefics = "idefics",
}

export enum ChatBotMode {
  Chain = "chain",
}

export enum FileStorageProvider {
  S3 = "s3",
}

export interface ImageFile {
  provider?: FileStorageProvider;
  key?: string;
  url?: string;
  bucketName: string;
  fileKey: string;
  fileType: string
}

export interface ChatBotHeartbeatRequest {
  action: ChatBotAction.Heartbeat;
  modelInterface: ModelInterface;
  data: {
    sessionId: string;
  };
}

export interface ChatBotRunRequest {
  action: ChatBotAction.Run;
  modelInterface: ModelInterface;
  data: {
    modelName: string;
    provider: string;
    sessionId: string;
    files: ImageFile[] | null;
    text: string;
    mode: string;
    workspaceId?: string;
    modelKwargs?: Record<string, string | boolean | number>;
  };
}

export interface ChatBotToken {
  sequenceNumber: number;
  runId?: string;
  value: string;
}

export interface RagDocument {
  page_content: string;
  metadata: {
    chunk_id: string;
    workspace_id: string;
    document_id: string;
    document_sub_id: string | null;
    document_type: string;
    document_sub_type: string | null;
    path: string;
    title: string | null;
    score: number;
  };
}

export interface ChatInputPanelProps {
  running: boolean;
  setRunning: Dispatch<SetStateAction<boolean>>;
  session: { id: string; loading: boolean };
  messageHistory: ChatBotHistoryItem[];
  setMessageHistory: (history: ChatBotHistoryItem[]) => void;
  configuration: ChatBotConfiguration;
  setConfiguration: Dispatch<React.SetStateAction<ChatBotConfiguration>>;
  closeNotification: (isError: boolean) => void;
  setErrorMessage: (message: string) => void;
  setError: (error: boolean) => void;
  chatId: string;
  onFileUploadStatusChange?: (isUploaded: boolean) => void
}

export interface ChatBotHistoryItem {
  type: ChatBotMessageType;
  content: string;
  metadata: any | Record<
    string,
    | string
    | boolean
    | number
    | null
    | undefined
    | ImageFile[]
    | string[]
    | string[][]
    | RagDocument[]
  >;
  tokens?: ChatBotToken[];
  isFeedbackSubmitted?: boolean;
  chatId?: string;
}

export interface userName {
  firstName: string;
  lastName: string;
}

export interface ChatBotMessageResponse {
  action: ChatBotAction;
  data: {
    sessionId: string;
    token?: ChatBotToken;
    content?: string;
    metadata: Record<
      string,
      | string
      | boolean
      | number
      | null
      | undefined
      | ImageFile[]
      | string[]
      | string[][]
      | RagDocument[]
    >;
  };
}

export enum ChabotInputModality {
  Text = "TEXT",
  Image = "IMAGE",
}

export enum ChabotOutputModality {
  Text = "TEXT",
  Image = "IMAGE",
  Embedding = "EMBEDDING",
}

export interface FeedbackData {
  sessionId: string;
  feedback: number;
  prompt: string;
  completion: string;
  comment: string;
  model: string;
  userId: string;
  improvementAreas?: string[];
  chatId?: string;
  chatDocuments?: Object[];
  promptId?: string;
  useHistory: boolean;
}
