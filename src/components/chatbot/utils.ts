// import { Storage } from "aws-amplify";
import { Dispatch, SetStateAction } from 'react';
import {
    ChatBotAction,
    ChatBotHistoryItem,
    ChatBotMessageResponse,
    ChatBotMessageType,
} from './types';
import { ChatSession } from './multi-chat';
import { SelectProps } from '@cloudscape-design/components';
import { OptionsHelper } from '../../common/helpers/options-helper';
import { Model, Workspace } from '../../API';
import modelService from '../../services/model.service';
import workspaceService from '../../services/workspace.service';

export function updateMessageHistory(
    sessionId: string,
    messageHistory: ChatBotHistoryItem[],
    setMessageHistory: Dispatch<SetStateAction<ChatBotHistoryItem[]>>,
    response: ChatBotMessageResponse,
) {
    if (response.data?.sessionId !== sessionId) return;

    if (
        response.action === ChatBotAction.LLMNewToken ||
        response.action === ChatBotAction.FinalResponse ||
        response.action === ChatBotAction.Error
    ) {
        const content = response.data?.content;
        let metadata = response.data?.metadata;
        const token = response.data?.token;
        const hasContent = typeof content !== 'undefined';
        const hasToken = typeof token !== 'undefined';
        const hasMetadata = typeof metadata !== 'undefined';

        if (
            messageHistory.length > 0 &&
            messageHistory[messageHistory.length - 1]['type'] !==
                ChatBotMessageType.Human
        ) {
            const lastMessage = messageHistory[messageHistory.length - 1];
            lastMessage.tokens = lastMessage.tokens || [];
            if (hasToken) {
                lastMessage.tokens.push(token);
            }

            lastMessage.tokens.sort(
                (a, b) => a.sequenceNumber - b.sequenceNumber,
            );
            if (lastMessage.tokens.length > 0) {
                const lastRunId =
                    lastMessage.tokens[lastMessage.tokens.length - 1].runId;
                if (lastRunId) {
                    lastMessage.tokens = lastMessage.tokens.filter(
                        (c) => c.runId === lastRunId,
                    );
                }
            }

            if (!hasMetadata) {
                metadata = lastMessage.metadata;
            }

            if (hasContent) {
                setMessageHistory((history) => [
                    ...history.slice(0, history.length - 1),
                    {
                        ...lastMessage,
                        type: ChatBotMessageType.AI,
                        content,
                        metadata,
                        tokens: lastMessage.tokens,
                    },
                ]);
            } else {
                const contentFromTokens = lastMessage.tokens
                    .map((c) => c.value)
                    .join('');

                setMessageHistory((history) => [
                    ...history.slice(0, history.length - 1),
                    {
                        ...lastMessage,
                        type: ChatBotMessageType.AI,
                        content: contentFromTokens,
                        metadata,
                        tokens: lastMessage.tokens,
                    },
                ]);
            }
        } else {
            if (hasContent) {
                const tokens = hasToken ? [token] : [];
                setMessageHistory((history) => [
                    ...history,
                    {
                        type: ChatBotMessageType.AI,
                        content,
                        metadata,
                        tokens,
                    },
                ]);
            } else if (typeof token !== 'undefined') {
                setMessageHistory((history) => [
                    ...history,
                    {
                        type: ChatBotMessageType.AI,
                        content: token.value,
                        metadata,
                        tokens: [token],
                    },
                ]);
            }
        }
    } else {
        console.error(`Unrecognized type ${response.action}`);
    }
}

export function updateMessageHistoryRef(
    sessionId: string,
    messageHistory: ChatBotHistoryItem[],
    response: ChatBotMessageResponse,
) {
    if (!response || !response.data) {
        return;
    }

    if (response.data?.sessionId !== sessionId) return;

    if (
        response.action === ChatBotAction.LLMNewToken ||
        response.action === ChatBotAction.FinalResponse ||
        response.action === ChatBotAction.Error
    ) {
        const content = response.data?.content;
        let metadata = response.data?.metadata;
        const token = response.data?.token;
        const hasContent = typeof content !== 'undefined';
        const hasToken = typeof token !== 'undefined';
        const hasMetadata = typeof metadata !== 'undefined';
        if (
            messageHistory.length > 0 &&
            messageHistory.at(-1)?.type !== ChatBotMessageType.Human
        ) {
            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
            const lastMessage = messageHistory.at(-1)!;
            lastMessage.tokens = lastMessage.tokens ?? [];
            if (hasToken) {
                // Workaround for text duplicates issue
                if (
                    !lastMessage.tokens
                        .map((t) => t.sequenceNumber)
                        .includes(token.sequenceNumber)
                ) {
                    lastMessage.tokens.push(token);
                } else {
                    return;
                }
            }

            lastMessage.tokens.sort(
                (a, b) => a.sequenceNumber - b.sequenceNumber,
            );
            if (lastMessage.tokens.length > 0) {
                const lastRunId =
                    lastMessage.tokens[lastMessage.tokens.length - 1].runId;
                if (lastRunId) {
                    lastMessage.tokens = lastMessage.tokens.filter(
                        (c) => c.runId === lastRunId,
                    );
                }
            }

            if (!hasMetadata) {
                metadata = lastMessage.metadata;
            }

            if (hasContent) {
                messageHistory[messageHistory.length - 1] = {
                    ...lastMessage,
                    type: ChatBotMessageType.AI,
                    content,
                    metadata,
                    tokens: lastMessage.tokens,
                };
            } else {
                const contentFromTokens = lastMessage.tokens
                    .map((c) => c.value)
                    .join('');

                messageHistory[messageHistory.length - 1] = {
                    ...lastMessage,
                    type: ChatBotMessageType.AI,
                    content: contentFromTokens,
                    metadata,
                    tokens: lastMessage.tokens,
                };
            }
        } else {
            if (hasContent) {
                const tokens = hasToken ? [token] : [];
                messageHistory.push({
                    type: ChatBotMessageType.AI,
                    content,
                    metadata,
                    tokens,
                });
            } else if (typeof token !== 'undefined') {
                messageHistory.push({
                    type: ChatBotMessageType.AI,
                    content: token.value,
                    metadata,
                    tokens: [token],
                });
            }
        }
    } else {
        console.error(`Unrecognized type ${response.action}`);
    }
}

// export function updateMessageHistoryRef(
//     sessionId: string,
//     messageHistory: ChatBotHistoryItem[],
//     response: ChatBotMessageResponse,
// ) {
//     if (!response || !response.data) {
//         return;
//     }
//
//     if (response.data?.sessionId !== sessionId) return;
//
//     if (
//         response.action === ChatBotAction.LLMNewToken ||
//         response.action === ChatBotAction.FinalResponse ||
//         response.action === ChatBotAction.Error
//     ) {
//         const content = response.data?.content;
//         let metadata = response.data?.metadata;
//         const token = response.data?.token;
//         const hasContent = typeof content !== 'undefined';
//         const hasToken = typeof token !== 'undefined';
//         const hasMetadata = typeof metadata !== 'undefined';
//
//         // Check if prompts exist and modify both the first and second prompts
//         if (hasMetadata && metadata.prompts && Array.isArray(metadata.prompts)) {
//             try {
//                 // Make sure there are at least two elements
//                 if (metadata.prompts.length < 2) {
//                     // Clone the structure of the first prompt if available
//                     if (metadata.prompts.length > 0) {
//                         const firstPromptStructure = [...(metadata.prompts[0] as any)];
//                         // Clear content but keep structure
//                         if (firstPromptStructure.length > 0) {
//                             firstPromptStructure[0] = "";
//                         }
//                         (metadata.prompts as any).push(firstPromptStructure);
//                     } else {
//                         // If no prompts exist, create a simple structure
//                         (metadata.prompts as any).push([""]);
//                         (metadata.prompts as any).push([""]);
//                     }
//                 }
//
//                 // Generate complete chat history string
//                 let chatHistoryText = "Chat History:\n";
//                 for (let i = 0; i < messageHistory.length; i++) {
//                     const msg = messageHistory[i];
//                     const role = msg.type === ChatBotMessageType.Human ? "Human" : "AI";
//                     chatHistoryText += `${role}: ${msg.content}\n`;
//                 }
//
//                 // Extract text function from chat-message.tsx
//                 const extractText = (content: any): string => {
//                     if (typeof content !== 'string') {
//                         content = JSON.stringify(content);
//                     }
//                     const textMatch = content.match(/"Text":\s*"([^"]*)"/);
//                     return textMatch ? textMatch[1] : content;
//                 };
//
//                 // Enhanced version to include document contents
//                 let filesText = "";
//                 if (metadata.files && Array.isArray(metadata.files) && metadata.files.length > 0) {
//                     filesText = "Attached Documents Content:\n";
//
//                     // Process each file
//                     metadata.files.forEach((file, index) => {
//                         // Safe property access
//                         const fileKey = typeof file === 'object' && file !== null && 'fileKey' in file
//                             ? file.fileKey
//                             : null;
//
//                         const fileName = fileKey
//                             ? String(fileKey).split('/').pop()
//                             : `File ${index + 1}`;
//
//                         filesText += `Document ${index + 1}: ${fileName}\n`;
//                     });
//                 }
//
//                 // Check if document content exists in metadata
//                 if (metadata.documents && Array.isArray(metadata.documents)) {
//                     // If no files section yet, add it
//                     if (!filesText) {
//                         filesText = "Attached Documents Content:\n";
//                     }
//
//                     // Process each document
//                     metadata.documents.forEach((doc, index) => {
//                         // Safe property access for document metadata
//                         let docName = `Document ${index + 1}`;
//
//                         if (typeof doc === 'object' && doc !== null) {
//                             // Try to get document path from metadata
//                             if ('metadata' in doc && typeof doc.metadata === 'object' && doc.metadata !== null && 'path' in doc.metadata) {
//                                 docName = String(doc.metadata.path);
//                             } else if ('name' in doc) {
//                                 docName = String(doc.name);
//                             }
//
//                             filesText += `Document: ${docName}\n`;
//                             filesText += "---CONTENT START---\n";
//
//                             // Extract content based on available properties
//                             if ('page_content' in doc) {
//                                 filesText += extractText(doc.page_content) + "\n";
//                             } else if ('content' in doc) {
//                                 filesText += String(doc.content) + "\n";
//                             } else if ('text' in doc) {
//                                 filesText += String(doc.text) + "\n";
//                             } else {
//                                 // Convert the entire document to a string if no specific content field is found
//                                 filesText += JSON.stringify(doc) + "\n";
//                             }
//
//                             filesText += "---CONTENT END---\n\n";
//                         }
//                     });
//                 }
//
//                 // Define content for the first prompt (follow-up handling with chat history)
//                 const customPrompt = `
//                 You are in a conversation with a user. You need to respond based on the information provided below.
//
//
//
// ${chatHistoryText}
//
// ${filesText}`;
//
//                 // Replace the first prompt's content safely
//                 if (Array.isArray(metadata.prompts[0])) {
//                     if ((metadata.prompts[0] as any).length > 0) {
//                         (metadata.prompts[0] as any)[0] = customPrompt;
//                     } else {
//                         (metadata.prompts[0] as any).push(customPrompt);
//                     }
//                 } else {
//                     // If the first prompt isn't an array (unlikely), replace it entirely
//                     (metadata.prompts as any)[0] = [customPrompt];
//                 }
//
//                 // Content for the second prompt (remains largely the same as your original code)
//                 const followUpPrompt = `When the user types input such as “continue,” “generate the rest,” or any similar instruction, continue the previous response exactly from where it left off, using the full context of the conversation so far, including all relevant chat history and AI responses. Additionally, incorporate and reference any documents I have uploaded, as needed, to ensure consistency and accuracy.
// Begin processing the content from the section marked CONTENT START onward.
// ${chatHistoryText}
// ${filesText}
// `;
//
//                 // Replace the second prompt's content safely
//                 if (Array.isArray(metadata.prompts[1])) {
//                     if ((metadata.prompts[1] as any).length > 0) {
//                         (metadata.prompts[1] as any)[0] = followUpPrompt;
//                     } else {
//                         (metadata.prompts[1] as any).push(followUpPrompt);
//                     }
//                 } else {
//                     // If the second prompt isn't an array (unlikely), replace it entirely
//                     (metadata.prompts as any)[1] = [followUpPrompt];
//                 }
//             } catch (error) {
//                 console.error("Error modifying prompts:", error);
//                 // Continue with original metadata if modification fails
//             }
//         }
//
//         // Process the message with updated metadata
//         if (
//             messageHistory.length > 0 &&
//             messageHistory.at(-1)?.type !== ChatBotMessageType.Human
//         ) {
//             // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
//             const lastMessage = messageHistory.at(-1)!;
//             lastMessage.tokens = lastMessage.tokens ?? [];
//             if (hasToken) {
//                 // Workaround for text duplicates issue
//                 if (
//                     !lastMessage.tokens
//                         .map((t) => t.sequenceNumber)
//                         .includes(token.sequenceNumber)
//                 ) {
//                     lastMessage.tokens.push(token);
//                 } else {
//                     return;
//                 }
//             }
//
//             lastMessage.tokens.sort(
//                 (a, b) => a.sequenceNumber - b.sequenceNumber,
//             );
//             if (lastMessage.tokens.length > 0) {
//                 const lastRunId =
//                     lastMessage.tokens[lastMessage.tokens.length - 1].runId;
//                 if (lastRunId) {
//                     lastMessage.tokens = lastMessage.tokens.filter(
//                         (c) => c.runId === lastRunId,
//                     );
//                 }
//             }
//
//             if (!hasMetadata) {
//                 metadata = lastMessage.metadata;
//             }
//
//             if (hasContent) {
//                 messageHistory[messageHistory.length - 1] = {
//                     ...lastMessage,
//                     type: ChatBotMessageType.AI,
//                     content,
//                     metadata,
//                     tokens: lastMessage.tokens,
//                 };
//             } else {
//                 const contentFromTokens = lastMessage.tokens
//                     .map((c) => c.value)
//                     .join('');
//
//                 messageHistory[messageHistory.length - 1] = {
//                     ...lastMessage,
//                     type: ChatBotMessageType.AI,
//                     content: contentFromTokens,
//                     metadata,
//                     tokens: lastMessage.tokens,
//                 };
//             }
//         } else {
//             if (hasContent) {
//                 const tokens = hasToken ? [token] : [];
//                 messageHistory.push({
//                     type: ChatBotMessageType.AI,
//                     content,
//                     metadata,
//                     tokens,
//                 });
//             } else if (typeof token !== 'undefined') {
//                 messageHistory.push({
//                     type: ChatBotMessageType.AI,
//                     content: token.value,
//                     metadata,
//                     tokens: [token],
//                 });
//             }
//         }
//     } else {
//         console.error(`Unrecognized type ${response.action}`);
//     }
// }

// 两个prompt 都可以了 但是缺word contents
// export function updateMessageHistoryRef(
//     sessionId: string,
//     messageHistory: ChatBotHistoryItem[],
//     response: ChatBotMessageResponse,
// ) {
//     if (!response || !response.data) {
//         return;
//     }
//
//     if (response.data?.sessionId !== sessionId) return;
//
//     if (
//         response.action === ChatBotAction.LLMNewToken ||
//         response.action === ChatBotAction.FinalResponse ||
//         response.action === ChatBotAction.Error
//     ) {
//         const content = response.data?.content;
//         let metadata = response.data?.metadata;
//         const token = response.data?.token;
//         const hasContent = typeof content !== 'undefined';
//         const hasToken = typeof token !== 'undefined';
//         const hasMetadata = typeof metadata !== 'undefined';
//
//         // Check if prompts exist and modify both the first and second prompts
//         if (hasMetadata && metadata.prompts && Array.isArray(metadata.prompts)) {
//             try {
//                 // Make sure there are at least two elements
//                 if (metadata.prompts.length < 2) {
//                     // Clone the structure of the first prompt if available
//                     if (metadata.prompts.length > 0) {
//                         const firstPromptStructure = [...(metadata.prompts[0] as any)];
//                         // Clear content but keep structure
//                         if (firstPromptStructure.length > 0) {
//                             firstPromptStructure[0] = "";
//                         }
//                         (metadata.prompts as any).push(firstPromptStructure);
//                     } else {
//                         // If no prompts exist, create a simple structure
//                         (metadata.prompts as any).push([""]);
//                         (metadata.prompts as any).push([""]);
//                     }
//                 }
//
//                 // Generate complete chat history string
//                 let chatHistoryText = "Chat History:\n";
//                 for (let i = 0; i < messageHistory.length; i++) {
//                     const msg = messageHistory[i];
//                     const role = msg.type === ChatBotMessageType.Human ? "Human" : "AI";
//                     chatHistoryText += `${role}: ${msg.content}\n`;
//                 }
//
//                 // Generate attached files information
//                 let filesText = "";
//                 if (metadata.files && Array.isArray(metadata.files) && metadata.files.length > 0) {
//                     filesText = "Attached Files:\n";
//                     metadata.files.forEach((file, index) => {
//                         const fileName = file.fileKey ? file.fileKey.split('/').pop() : 'Unknown';
//                         filesText += `File ${index + 1}: ${fileName}\n`;
//                     });
//                 }
//
//                 // Define content for the first prompt (follow-up handling with chat history)
//                 const followUpPrompt = `Analyze the follow-up input based on chat history and context: If the input is "continue", "generate the rest", "keep going" or similar requests, directly continue generating the previously truncated response, maintaining the same style and depth.
// If the input is a follow-up question, rephrase it into a standalone question by referencing relevant chat history. Eliminate unnecessary details and unrelated context. Focus solely on the main idea.
// Ensure the output is strictly the rephrased question, with no additional explanations or conversational tone.
// If the input is unrelated to the chat history, output only the current input as a standalone question
//
// ${chatHistoryText}
//
// ${filesText}`;
//
//                 // Replace the first prompt's content safely
//                 if (Array.isArray(metadata.prompts[0])) {
//                     if ((metadata.prompts[0] as any).length > 0) {
//                         (metadata.prompts[0] as any)[0] = followUpPrompt;
//                     } else {
//                         (metadata.prompts[0] as any).push(followUpPrompt);
//                     }
//                 } else {
//                     // If the first prompt isn't an array (unlikely), replace it entirely
//                     (metadata.prompts as any)[0] = [followUpPrompt];
//                 }
//
//                 // Content for the second prompt (remains largely the same as your original code)
//                 const customPrompt = `
// You are in a conversation with a user. You need to respond based on the information provided below.
//
// ${chatHistoryText}
//
// ${filesText}
//
// Ensure your response:
// 1. Directly addresses the user's question
// 2. Clearly references any information from the attachments if used
// 3. Is concise and doesn't repeat known information
// 4. Honestly acknowledges uncertainty and provides possible directions if unsure
//
// Please respond to the user's question appropriately.
// `;
//
//                 // Replace the second prompt's content safely
//                 if (Array.isArray(metadata.prompts[1])) {
//                     if ((metadata.prompts[1] as any).length > 0) {
//                         (metadata.prompts[1] as any)[0] = customPrompt;
//                     } else {
//                         (metadata.prompts[1] as any).push(customPrompt);
//                     }
//                 } else {
//                     // If the second prompt isn't an array (unlikely), replace it entirely
//                     (metadata.prompts as any)[1] = [customPrompt];
//                 }
//             } catch (error) {
//                 console.error("Error modifying prompts:", error);
//                 // Continue with original metadata if modification fails
//             }
//         }
//
//         // Process the message with updated metadata
//         if (
//             messageHistory.length > 0 &&
//             messageHistory.at(-1)?.type !== ChatBotMessageType.Human
//         ) {
//             // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
//             const lastMessage = messageHistory.at(-1)!;
//             lastMessage.tokens = lastMessage.tokens ?? [];
//             if (hasToken) {
//                 // Workaround for text duplicates issue
//                 if (
//                     !lastMessage.tokens
//                         .map((t) => t.sequenceNumber)
//                         .includes(token.sequenceNumber)
//                 ) {
//                     lastMessage.tokens.push(token);
//                 } else {
//                     return;
//                 }
//             }
//
//             lastMessage.tokens.sort(
//                 (a, b) => a.sequenceNumber - b.sequenceNumber,
//             );
//             if (lastMessage.tokens.length > 0) {
//                 const lastRunId =
//                     lastMessage.tokens[lastMessage.tokens.length - 1].runId;
//                 if (lastRunId) {
//                     lastMessage.tokens = lastMessage.tokens.filter(
//                         (c) => c.runId === lastRunId,
//                     );
//                 }
//             }
//
//             if (!hasMetadata) {
//                 metadata = lastMessage.metadata;
//             }
//
//             if (hasContent) {
//                 messageHistory[messageHistory.length - 1] = {
//                     ...lastMessage,
//                     type: ChatBotMessageType.AI,
//                     content,
//                     metadata,
//                     tokens: lastMessage.tokens,
//                 };
//             } else {
//                 const contentFromTokens = lastMessage.tokens
//                     .map((c) => c.value)
//                     .join('');
//
//                 messageHistory[messageHistory.length - 1] = {
//                     ...lastMessage,
//                     type: ChatBotMessageType.AI,
//                     content: contentFromTokens,
//                     metadata,
//                     tokens: lastMessage.tokens,
//                 };
//             }
//         } else {
//             if (hasContent) {
//                 const tokens = hasToken ? [token] : [];
//                 messageHistory.push({
//                     type: ChatBotMessageType.AI,
//                     content,
//                     metadata,
//                     tokens,
//                 });
//             } else if (typeof token !== 'undefined') {
//                 messageHistory.push({
//                     type: ChatBotMessageType.AI,
//                     content: token.value,
//                     metadata,
//                     tokens: [token],
//                 });
//             }
//         }
//     } else {
//         console.error(`Unrecognized type ${response.action}`);
//     }
// }

export function updateChatSessions(
    chatSession: ChatSession,
    response: ChatBotMessageResponse,
): void {
    if (response.data?.sessionId !== chatSession.id) return;

    const messageHistory = chatSession.messageHistory;
    if (
        response.action === ChatBotAction.FinalResponse ||
        response.action === ChatBotAction.Error
    ) {
        chatSession.running = false;
    }
    if (
        response.action === ChatBotAction.LLMNewToken ||
        response.action === ChatBotAction.FinalResponse ||
        response.action === ChatBotAction.Error
    ) {
        const content = response.data?.content;
        let metadata = response.data?.metadata;
        const token = response.data?.token;
        const hasContent = typeof content !== 'undefined';
        const hasToken = typeof token !== 'undefined';
        const hasMetadata = typeof metadata !== 'undefined';

        if (
            messageHistory.length > 0 &&
            messageHistory[messageHistory.length - 1]['type'] !==
            ChatBotMessageType.Human
        ) {
            const lastMessage = messageHistory[messageHistory.length - 1];
            if (lastMessage.metadata !== undefined) {
                return;
            }
            lastMessage.tokens = lastMessage.tokens || [];
            if (hasToken) {
                lastMessage.tokens.push(token);
            }

            lastMessage.tokens.sort(
                (a, b) => a.sequenceNumber - b.sequenceNumber,
            );
            if (lastMessage.tokens.length > 0) {
                const lastRunId =
                    lastMessage.tokens[lastMessage.tokens.length - 1].runId;
                if (lastRunId) {
                    lastMessage.tokens = lastMessage.tokens.filter(
                        (c) => c.runId === lastRunId,
                    );
                }
            }

            if (!hasMetadata) {
                metadata = lastMessage.metadata;
            }

            if (hasContent) {
                chatSession.messageHistory = [
                    ...messageHistory.slice(0, messageHistory.length - 1),
                    {
                        ...lastMessage,
                        type: ChatBotMessageType.AI,
                        content,
                        metadata,
                        tokens: lastMessage.tokens,
                    },
                ];
            } else {
                const contentFromTokens = lastMessage.tokens
                    .map((c) => c.value)
                    .join('');
                chatSession.messageHistory = [
                    ...messageHistory.slice(0, messageHistory.length - 1),
                    {
                        ...lastMessage,
                        type: ChatBotMessageType.AI,
                        content: contentFromTokens,
                        metadata,
                        tokens: lastMessage.tokens,
                    },
                ];
            }
        } else {
            if (hasContent) {
                const tokens = hasToken ? [token] : [];
                chatSession.messageHistory = [
                    ...messageHistory,
                    {
                        type: ChatBotMessageType.AI,
                        content,
                        metadata,
                        tokens,
                    },
                ];
            } else if (typeof token !== 'undefined') {
                chatSession.messageHistory = [
                    ...messageHistory,
                    {
                        type: ChatBotMessageType.AI,
                        content: token.value,
                        metadata,
                        tokens: [token],
                    },
                ];
            }
        }
    }
}

export async function getSignedUrl(key: string) {
    // const signedUrl = await Storage.get(key as string);
    const signedUrl = key;
    return signedUrl;
}

export function getSelectedModelMetadata(
    models: Model[] | undefined,
    selectedModelOption: SelectProps.Option | null,
): Model | null {
    let selectedModelMetadata: Model | null = null;

    if (selectedModelOption) {
        const { name, provider } = OptionsHelper.parseValue(
            selectedModelOption.value,
        );
        const targetModel = models?.find(
            (m) => m.name === name && m.provider === provider,
        );

        if (targetModel) {
            selectedModelMetadata = targetModel;
        }
    }

    return selectedModelMetadata;
}

export async function getCFGModels() {
    const ret_cfg_models: Model[] = [];
    const output = await modelService.getCFGModels();
    if (Array.isArray(output)) {
        for (const m of output) {
            const addModel: Model = {
                __typename: 'Model',
                name: m.name,
                provider: m.provider,
                modelId: m.modelId,
                interface: m.interface,
                ragSupported: m.ragSupported,
                inputModalities: m.inputModalities,
                outputModalities: m.outputModalities,
                streaming: m.streaming,
            };
            ret_cfg_models.push(addModel);
        }
    }
    return ret_cfg_models;
}

export async function getCFGWorkspaces() {
    const ret_cfg_workspaces: Workspace[] = [];
    const output = await workspaceService.getCFGWorkspaces();
    if (Array.isArray(output)) {
        for (const w of output) {
            const addWorkspace: Workspace = {
                __typename: 'Workspace',
                name: w.name,
                workspaceId: w.workspaceId,
                engine: w.engine,
                createdAt: w.createdAt,
                updatedAt: w.updatedAt,
            };
            ret_cfg_workspaces.push(addWorkspace);
        }
    }
    return ret_cfg_workspaces;
}

// solution 1
// export function updateMessageHistoryRef(
//     sessionId: string,
//     messageHistory: ChatBotHistoryItem[],
//     response: ChatBotMessageResponse,
// ) {
//     if (!response || !response.data) {
//         return;
//     }
//
//     if (response.data?.sessionId !== sessionId) return;
//
//     if (
//         response.action === ChatBotAction.LLMNewToken ||
//         response.action === ChatBotAction.FinalResponse ||
//         response.action === ChatBotAction.Error
//     ) {
//         const content = response.data?.content;
//         let metadata = response.data?.metadata;
//         const token = response.data?.token;
//         const hasContent = typeof content !== 'undefined';
//         const hasToken = typeof token !== 'undefined';
//         const hasMetadata = typeof metadata !== 'undefined';
//
//         // Check if prompts exist and modify the second prompt
//         if (hasMetadata && metadata.prompts && Array.isArray(metadata.prompts)) {
//             try {
//                 // Make sure there are at least two elements
//                 if (metadata.prompts.length < 2) {
//                     // Clone the structure of the first prompt if available
//                     if (metadata.prompts.length > 0) {
//                         const firstPromptStructure = [...(metadata.prompts[0] as any)];
//                         // Clear content but keep structure
//                         if (firstPromptStructure.length > 0) {
//                             firstPromptStructure[0] = "";
//                         }
//                         (metadata.prompts as any).push(firstPromptStructure);
//                     } else {
//                         // If no prompts exist, create a simple structure
//                         (metadata.prompts as any).push([""]);
//                     }
//                 }
//
//                 // Generate complete chat history string
//                 let chatHistoryText = "Chat History:\n";
//                 for (let i = 0; i < messageHistory.length; i++) {
//                     const msg = messageHistory[i];
//                     const role = msg.type === ChatBotMessageType.Human ? "Human" : "AI";
//                     chatHistoryText += `${role}: ${msg.content}\n`;
//                 }
//
//                 // Generate attached files information
//                 let filesText = "";
//                 if (metadata.files && Array.isArray(metadata.files) && metadata.files.length > 0) {
//                     filesText = "Attached Files:\n";
//                     metadata.files.forEach((file, index) => {
//                         const fileName = file.fileKey ? file.fileKey.split('/').pop() : 'Unknown';
//                         filesText += `File ${index + 1}: ${fileName}\n`;
//                     });
//                 }
//
//                 // Combine complete chat history and file information to create a new custom prompt
//                 const customPrompt = `
// You are in a conversation with a user. You need to respond based on the information provided below.
//
// ${chatHistoryText}
//
// ${filesText}
//
// Ensure your response:
// 1. Directly addresses the user's question
// 2. Clearly references any information from the attachments if used
// 3. Is concise and doesn't repeat known information
// 4. Honestly acknowledges uncertainty and provides possible directions if unsure
//
// Please respond to the user's question appropriately.
// `;
//
//                 // Replace the second prompt's content safely
//                 if (Array.isArray(metadata.prompts[1])) {
//                     if ((metadata.prompts[1] as any).length > 0) {
//                         (metadata.prompts[1] as any)[0] = customPrompt;
//                     } else {
//                         (metadata.prompts[1] as any).push(customPrompt);
//                     }
//                 } else {
//                     // If the second prompt isn't an array (unlikely), replace it entirely
//                     (metadata.prompts as any)[1] = [customPrompt];
//                 }
//             } catch (error) {
//                 console.error("Error modifying prompts:", error);
//                 // Continue with original metadata if modification fails
//             }
//         }
//
//         // Process the message with updated metadata
//         if (
//             messageHistory.length > 0 &&
//             messageHistory.at(-1)?.type !== ChatBotMessageType.Human
//         ) {
//             // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
//             const lastMessage = messageHistory.at(-1)!;
//             lastMessage.tokens = lastMessage.tokens ?? [];
//             if (hasToken) {
//                 // Workaround for text duplicates issue
//                 if (
//                     !lastMessage.tokens
//                         .map((t) => t.sequenceNumber)
//                         .includes(token.sequenceNumber)
//                 ) {
//                     lastMessage.tokens.push(token);
//                 } else {
//                     return;
//                 }
//             }
//
//             lastMessage.tokens.sort(
//                 (a, b) => a.sequenceNumber - b.sequenceNumber,
//             );
//             if (lastMessage.tokens.length > 0) {
//                 const lastRunId =
//                     lastMessage.tokens[lastMessage.tokens.length - 1].runId;
//                 if (lastRunId) {
//                     lastMessage.tokens = lastMessage.tokens.filter(
//                         (c) => c.runId === lastRunId,
//                     );
//                 }
//             }
//
//             if (!hasMetadata) {
//                 metadata = lastMessage.metadata;
//             }
//
//             if (hasContent) {
//                 messageHistory[messageHistory.length - 1] = {
//                     ...lastMessage,
//                     type: ChatBotMessageType.AI,
//                     content,
//                     metadata,
//                     tokens: lastMessage.tokens,
//                 };
//             } else {
//                 const contentFromTokens = lastMessage.tokens
//                     .map((c) => c.value)
//                     .join('');
//
//                 messageHistory[messageHistory.length - 1] = {
//                     ...lastMessage,
//                     type: ChatBotMessageType.AI,
//                     content: contentFromTokens,
//                     metadata,
//                     tokens: lastMessage.tokens,
//                 };
//             }
//         } else {
//             if (hasContent) {
//                 const tokens = hasToken ? [token] : [];
//                 messageHistory.push({
//                     type: ChatBotMessageType.AI,
//                     content,
//                     metadata,
//                     tokens,
//                 });
//             } else if (typeof token !== 'undefined') {
//                 messageHistory.push({
//                     type: ChatBotMessageType.AI,
//                     content: token.value,
//                     metadata,
//                     tokens: [token],
//                 });
//             }
//         }
//     } else {
//         console.error(`Unrecognized type ${response.action}`);
//     }
// }
//
// export function updateChatSessions(
//     chatSession: ChatSession,
//     response: ChatBotMessageResponse,
// ): void {
//     if (response.data?.sessionId !== chatSession.id) return;
//
//     const messageHistory = chatSession.messageHistory;
//     if (
//         response.action === ChatBotAction.FinalResponse ||
//         response.action === ChatBotAction.Error
//     ) {
//         chatSession.running = false;
//     }
//     if (
//         response.action === ChatBotAction.LLMNewToken ||
//         response.action === ChatBotAction.FinalResponse ||
//         response.action === ChatBotAction.Error
//     ) {
//         const content = response.data?.content;
//         let metadata = response.data?.metadata;
//         const token = response.data?.token;
//         const hasContent = typeof content !== 'undefined';
//         const hasToken = typeof token !== 'undefined';
//         const hasMetadata = typeof metadata !== 'undefined';
//
//         if (
//             messageHistory.length > 0 &&
//             messageHistory[messageHistory.length - 1]['type'] !==
//                 ChatBotMessageType.Human
//         ) {
//             const lastMessage = messageHistory[messageHistory.length - 1];
//             if (lastMessage.metadata !== undefined) {
//                 return;
//             }
//             lastMessage.tokens = lastMessage.tokens || [];
//             if (hasToken) {
//                 lastMessage.tokens.push(token);
//             }
//
//             lastMessage.tokens.sort(
//                 (a, b) => a.sequenceNumber - b.sequenceNumber,
//             );
//             if (lastMessage.tokens.length > 0) {
//                 const lastRunId =
//                     lastMessage.tokens[lastMessage.tokens.length - 1].runId;
//                 if (lastRunId) {
//                     lastMessage.tokens = lastMessage.tokens.filter(
//                         (c) => c.runId === lastRunId,
//                     );
//                 }
//             }
//
//             if (!hasMetadata) {
//                 metadata = lastMessage.metadata;
//             }
//
//             if (hasContent) {
//                 chatSession.messageHistory = [
//                     ...messageHistory.slice(0, messageHistory.length - 1),
//                     {
//                         ...lastMessage,
//                         type: ChatBotMessageType.AI,
//                         content,
//                         metadata,
//                         tokens: lastMessage.tokens,
//                     },
//                 ];
//             } else {
//                 const contentFromTokens = lastMessage.tokens
//                     .map((c) => c.value)
//                     .join('');
//                 chatSession.messageHistory = [
//                     ...messageHistory.slice(0, messageHistory.length - 1),
//                     {
//                         ...lastMessage,
//                         type: ChatBotMessageType.AI,
//                         content: contentFromTokens,
//                         metadata,
//                         tokens: lastMessage.tokens,
//                     },
//                 ];
//             }
//         } else {
//             if (hasContent) {
//                 const tokens = hasToken ? [token] : [];
//                 chatSession.messageHistory = [
//                     ...messageHistory,
//                     {
//                         type: ChatBotMessageType.AI,
//                         content,
//                         metadata,
//                         tokens,
//                     },
//                 ];
//             } else if (typeof token !== 'undefined') {
//                 chatSession.messageHistory = [
//                     ...messageHistory,
//                     {
//                         type: ChatBotMessageType.AI,
//                         content: token.value,
//                         metadata,
//                         tokens: [token],
//                     },
//                 ];
//             }
//         }
//     }
// }
//
// export async function getSignedUrl(key: string) {
//     // const signedUrl = await Storage.get(key as string);
//     const signedUrl = key;
//     return signedUrl;
// }
//
// export function getSelectedModelMetadata(
//     models: Model[] | undefined,
//     selectedModelOption: SelectProps.Option | null,
// ): Model | null {
//     let selectedModelMetadata: Model | null = null;
//
//     if (selectedModelOption) {
//         const { name, provider } = OptionsHelper.parseValue(
//             selectedModelOption.value,
//         );
//         const targetModel = models?.find(
//             (m) => m.name === name && m.provider === provider,
//         );
//
//         if (targetModel) {
//             selectedModelMetadata = targetModel;
//         }
//     }
//
//     return selectedModelMetadata;
// }
//
// export async function getCFGModels() {
//     const ret_cfg_models: Model[] = [];
//     const output = await modelService.getCFGModels();
//     if (Array.isArray(output)) {
//         for (const m of output) {
//             const addModel: Model = {
//                 __typename: 'Model',
//                 name: m.name,
//                 provider: m.provider,
//                 modelId: m.modelId,
//                 interface: m.interface,
//                 ragSupported: m.ragSupported,
//                 inputModalities: m.inputModalities,
//                 outputModalities: m.outputModalities,
//                 streaming: m.streaming,
//             };
//             ret_cfg_models.push(addModel);
//         }
//     }
//     return ret_cfg_models;
// }
//
// export async function getCFGWorkspaces() {
//     const ret_cfg_workspaces: Workspace[] = [];
//     const output = await workspaceService.getCFGWorkspaces();
//     if (Array.isArray(output)) {
//         for (const w of output) {
//             const addWorkspace: Workspace = {
//                 __typename: 'Workspace',
//                 name: w.name,
//                 workspaceId: w.workspaceId,
//                 engine: w.engine,
//                 createdAt: w.createdAt,
//                 updatedAt: w.updatedAt,
//             };
//             ret_cfg_workspaces.push(addWorkspace);
//         }
//     }
//     return ret_cfg_workspaces;
// }
