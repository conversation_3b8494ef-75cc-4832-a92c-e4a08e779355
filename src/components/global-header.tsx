import {
  ButtonDropdownProps,
  TopNavigation,
} from "@cloudscape-design/components";
import { Mode } from "@cloudscape-design/global-styles";
import { useEffect, useState, useRef } from "react";
import { StorageHelper } from "../common/helpers/storage-helper";
import useOnFollow from "../common/hooks/use-on-follow";
import { CHATBOT_NAME } from "../common/constants";
import { useOktaAuth } from "@okta/okta-react";
import { UserInfo } from "../common/types";
import { BUILDTIMESTAMP } from "./../../config";
import FDAIcon from "../assets/images/fda-logo-with-text.png";
import AnimatedIcon from "./animated-icon";
import { useLocation } from 'react-router-dom';
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store/configureStore";
import NewFeature from "./newFeature";
import { UPDATES_CONFIG, processUpdatesConfig } from '../configs/newFeature/updateConfig';
import { isUpdateRead } from '../stores/newFeature/updateStorage';

// 创建一个带数字徽章的Bell图标组件
const BellWithBadge = ({ unreadCount }: { unreadCount: number }) => {
  return (
    <div style={{ position: 'relative', display: 'inline-flex' }}>
      <AnimatedIcon 
        size={16} 
        color="#ffffff" 
        animationType="ring" 
        iconType="bell"
      />
      {unreadCount > 0 && (
        <span
          style={{
            position: 'absolute',
            top: '-6px',
            right: '-6px',
            backgroundColor: '#ef4444',
            color: 'white',
            borderRadius: '50%',
            minWidth: '16px',
            height: '16px',
            fontSize: '10px',
            fontWeight: '600',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            lineHeight: '1',
            // border: '2px solid #ffffff',
            boxSizing: 'border-box'
          }}
        >
          {unreadCount > 99 ? '99+' : unreadCount}
        </span>
      )}
    </div>
  );
};

export default function GlobalHeader() {
  const dataState = useSelector((state: RootState) => {
    return state.rootReducer;
  });
  const userLoginName = dataState?.chatReducer?.userName;
  
  const onFollow = useOnFollow();
  const [userName, setUserName] = useState<string | null>(null);
  const [theme, setTheme] = useState<Mode>(StorageHelper.getTheme());
  const dateTimeStamp: string = BUILDTIMESTAMP;
  // Okta and State hooks
  // const { authState, oktaAuth } = useOktaAuth();
  const [, setUserInfo] = useState<UserInfo>();
  const [initialLoad, setInitialLoad] = useState(true)
  const location = useLocation();

  // NewFeature 相关状态
  const [showNewFeature, setShowNewFeature] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);
  const newFeatureRef = useRef<HTMLDivElement>(null);

  // 计算未读数量
  useEffect(() => {
    const calculateUnreadCount = () => {
      const processedUpdates = processUpdatesConfig(UPDATES_CONFIG);
      const unreadUpdates = processedUpdates.filter(update => !isUpdateRead(update.id));
      setUnreadCount(unreadUpdates.length);
    };

    // 初始计算
    calculateUnreadCount();

    // 监听存储变化（当用户在其他标签页标记已读时）
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'feature_updates_read_status') {
        calculateUnreadCount();
      }
    };

    window.addEventListener('storage', handleStorageChange);
    
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

useEffect(() =>{
  if(location.pathname === '/'){
    setInitialLoad(true)
  }else{
    setInitialLoad(false)
  }
},[location.pathname])

  // 处理点击外部区域关闭面板
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (newFeatureRef.current && !newFeatureRef.current.contains(event.target as Node)) {
        setShowNewFeature(false);
      }
    };

    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setShowNewFeature(false);
      }
    };

    if (showNewFeature) {
      // 添加一个小延迟，避免立即关闭
      setTimeout(() => {
        document.addEventListener('mousedown', handleClickOutside);
        document.addEventListener('keydown', handleEscapeKey);
      }, 100);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, [showNewFeature]);

  // useEffect(() => {
  //   (async () => {
  //     if (!authState?.isAuthenticated) {
  //       // setUserInfo(null);
  //     } else {
  //       await oktaAuth.getUser().then((user: any) => {
  //         const loggedUser = {
  //           email: user.email,
  //           name: user.name,
  //           first_name: user.given_name,
  //           last_name: user.family_name,
  //         };
  //         setUserInfo(loggedUser);
  //         setUserName(user.name);
  //         // console.log(user)
  //       });
  //     }

  //     // oktaAuth.token.getUserInfo((info: UserInfo) => {
  //     //     setUserInfo(info);
  //     // })
  //     // }
  //     // // const result = await Auth.currentUserInfo();

  //     // // if (!result || Object.keys(result).length === 0) {
  //     // //   Auth.signOut();
  //     // //   return;
  //     // // }

  //     // const userName = result?.attributes?.email;
  //     // setUserName(userName);
  //   })();
  // }, []);

  const onChangeThemeClick = () => {
    if (theme === Mode.Dark) {
      setTheme(StorageHelper.applyTheme(Mode.Light));
    } else {
      setTheme(StorageHelper.applyTheme(Mode.Dark));
    }
  };

  const onUserProfileClick = ({
    detail,
  }: {
    detail: ButtonDropdownProps.ItemClickDetails;
  }) => {
    if (detail.id === "signout") {
      // oktaAuth.signOut();
    }
  };

  // NewFeature 相关处理函数
  const handleBellClick = () => {
    setShowNewFeature(!showNewFeature);
  };

  const handleCloseNewFeature = () => {
    setShowNewFeature(false);
  };

  const handleMarkAsRead = (id: string) => {
    // 更新未读数量
    setUnreadCount(prev => Math.max(0, prev - 1));
    console.log('标记为已读:', id);
  };

  const handleMarkAllAsRead = () => {
    // 清零未读数量
    setUnreadCount(0);
    console.log('全部标记为已读');
  };
  
  return (
    <>
      <div
        style={{
          zIndex: 1002,
          top: 0,
          left: 0,
          right: 0,
          position: "fixed",
        }}
        id="awsui-top-navigation"
      >
        <TopNavigation
          identity={{
            title: 'Data Governance Document Builder',
            href: "#",
            logo: {
              src: FDAIcon,
              alt: { CHATBOT_NAME } + " Logo",
            },
          }}

          utilities={[
            // {
            //   type: "button",
            //   text: "Build: " + dateTimeStamp,
            // },
            // {
            //   type: "button",
            //   text: theme === Mode.Dark ? "Light Mode" : "Dark Mode",
            //   onClick: onChangeThemeClick,
            // },
            // {
            //   type: "button",
            //   text: "GitHub",
            //   href: "https://github.com/aws-samples/aws-genai-llm-chatbot",
            //   external: true,
            //   externalIconAriaLabel: " (opens in a new tab)",
            // },
            {
              type: "menu-dropdown",
              text: "Help",
              iconSvg: <AnimatedIcon 
                size={16} 
                color="#ffffff" 
                animationType="heartbeat" 
                iconType="questionCircle"
              />,
              //description: "Help menu",
              // 动画类型: 'pulse', 'ring', 'rotate', 'bounce', 'heartbeat', 'glow', 'wiggle', 'float'
              // 图标类型: 'questionCircle', 'lightbulb', 'book', 'info', 'exclamation' 等
              items: [
                { id: "training materials", text: "Training Materials", href:'https://google.com', external:true },
                // { id: "profile", text: "Profile" },
                // { id: "preferences", text: "Preferences" },
                // { id: "security", text: "Security" },
                // {
                //   id: "support-group",
                //   text: "Support",
                //   items: [
                //     {
                //       id: "documentation",
                //       text: "Documentation",
                //       href: "#",
                //       external: true,
                //       externalIconAriaLabel:
                //         " (opens in new tab)"
                //     },
                //     { id: "support", text: "Support" },
                //     {
                //       id: "feedback",
                //       text: "Feedback",
                //       href: "#",
                //       external: true,
                //       externalIconAriaLabel:
                //         " (opens in new tab)"
                //     }
                //   ]
                // },
              ]
            },
            {
              type: "button",
              iconSvg: <BellWithBadge unreadCount={unreadCount} />,
              title: "Notifications",
              ariaLabel: unreadCount > 0 ? `Notifications (${unreadCount} unread)` : "Notifications",
              disableUtilityCollapse: false,
              onClick: handleBellClick
            },
            {
              type: "menu-dropdown",
              description: userLoginName ? `${userLoginName.firstName} ${userLoginName.lastName}` : "",
              // iconUrl: "/custom-icon.svg", 
              // ariaLabel: "User profile",
              iconSvg: <AnimatedIcon 
                size={19} 
                color="#ffffff" 
                animationType="pulse" 
                iconType="personCheck"
                className="user-icon-right"
              />,
              // 动画类型: 'float', 'pulse', 'rotate', 'bounce', 'heartbeat', 'glow', 'wiggle'
              // 图标类型: 'personCircle', 'personCheck', 'personGear', 'personHeart', 'personWorkspace' 等
              onItemClick: onUserProfileClick,
              items: [
                // {
                //   id: "signout",
                //   text: "Sign out",
                // },
              ],
              onItemFollow: onFollow,
            },
          ]}
        />
         {/* {((initialLoad && location.pathname === '/') || location.pathname.includes('/chatbot/playground')) && <div style={{backgroundColor:'#ffffff'}}>
          <p style={{padding:'10px 10px 0px 20px', color:'#c81f1f', fontWeight:'400', fontStyle:'italic', marginTop: '0px', fontSize:'12px'}}>Staff are responsible for the accuracy, quality, and integrity of their work, and accept accountability for any material they produce with AI.  Staff should expect their use of CDERGPT, including input prompts and generated outputs, to be logged and monitored as part of an effort of continuous improvement.</p>
          </div>} */}
      </div>

      {/* NewFeature 面板 */}
      <div ref={newFeatureRef}>
        <NewFeature
          isVisible={showNewFeature}
          onClose={handleCloseNewFeature}
          onMarkAsRead={handleMarkAsRead}
          onMarkAllAsRead={handleMarkAllAsRead}
        />
      </div>
    </>
  );
}
