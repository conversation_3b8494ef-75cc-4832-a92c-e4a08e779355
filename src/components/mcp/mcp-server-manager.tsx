import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Form,
  SpaceBetween,
  FormField,
  Input,
  Alert,
  Box,
  TextFilter,
  Pagination,
  Cards,
  ExpandableSection,
  Icon,
  Toggle,
  Tabs,
  Link,
  Popover,
  StatusIndicator,
  Spinner,
  ButtonDropdown
} from '@cloudscape-design/components';
import { useMCP } from '../../contexts/mcp-context';

interface MCPServer {
  id: string;
  name: string;
  url?: string;
  command?: string;
  args?: string;
  description?: string;
  isConnected: boolean;
  connectionType: 'url' | 'command';
}

const MCPServerManager: React.FC = () => {
  const { servers, addServer, removeServer, getClient, isConnecting, updateServerStatus } = useMCP();
  const [isAddServerModalVisible, setIsAddServerModalVisible] = useState(false);
  const [serverUrl, setServerUrl] = useState('');
  const [serverName, setServerName] = useState('');
  const [serverDescription, setServerDescription] = useState('');
  const [serverCommand, setServerCommand] = useState('');
  const [serverArgs, setServerArgs] = useState('');
  const [connectionType, setConnectionType] = useState<'url' | 'command'>('url');
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [filterText, setFilterText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [selectedServer, setSelectedServer] = useState<MCPServer | null>(null);
  const [activeTabId, setActiveTabId] = useState('url');
  const [testingServerId, setTestingServerId] = useState<string | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const isBrowserEnvironment = typeof window !== 'undefined' && typeof window.document !== 'undefined';

  const handleAddServer = async () => {
    setIsLoading(true);
    setError(null);

    if (!serverName) {
      setError('Server name is required');
      setIsLoading(false);
      return;
    }

    if (connectionType === 'url' && !serverUrl) {
      setError('Server URL is required for URL connection type');
      setIsLoading(false);
      return;
    }

    if (connectionType === 'command' && !serverCommand) {
      setError('Command is required for Command connection type');
      setIsLoading(false);
      return;
    }

    if (isBrowserEnvironment && connectionType === 'command') {
      setError('Command mode is not supported in browser environments. Please use URL mode instead or start an HTTP server.');
      setIsLoading(false);
      return;
    }

    try {
      const newServer: MCPServer = {
        id: Date.now().toString(),
        name: serverName,
        description: serverDescription,
        isConnected: false,
        connectionType: connectionType
      };

      if (connectionType === 'url') {
        newServer.url = serverUrl;

        try {
          const response = await fetch(serverUrl, {
            method: 'HEAD',
            signal: AbortSignal.timeout(3000)
          }).catch(() => null);

          if (!response) {
            setError('Server is unreachable, added but displayed as disconnected.');
            const updatedServers = [...servers, newServer];
            await addServer(newServer);
            setIsAddServerModalVisible(false);
            resetForm();
            return;
          }
        } catch (error) {
          console.error('Error testing server connection:', error);
          setError('Server connection test failed, added but displayed as disconnected.');
          await addServer(newServer);
          setIsAddServerModalVisible(false);
          resetForm();
          return;
        }

        newServer.isConnected = true;
      } else {
        newServer.command = serverCommand;
        newServer.args = serverArgs;
      }

      const success = await addServer(newServer);

      if (success) {
        setIsAddServerModalVisible(false);
        resetForm();
      } else {
        setError('Unable to connect to MCP server, added but displayed as disconnected.');
        updateServerStatus(newServer.id, false);
      }
    } catch (err) {
      setError(`Error: ${err instanceof Error ? err.message : String(err)}`);
    } finally {
      setIsLoading(false);
    }
  };

  const resetForm = () => {
    setServerName('');
    setServerUrl('');
    setServerCommand('');
    setServerArgs('');
    setServerDescription('');
    setConnectionType('url');
    setActiveTabId('url');
  };

  const handleRemoveServer = () => {
    if (selectedServer) {
      removeServer(selectedServer.id);
      setSelectedServer(null);
    }
  };

  const handleTestConnection = async () => {
    if (!selectedServer) return;

    try {
      setIsLoading(true);
      setTestingServerId(selectedServer.id);

      const statusMessage = document.createElement('div');
      statusMessage.style.position = 'fixed';
      statusMessage.style.bottom = '20px';
      statusMessage.style.right = '20px';
      statusMessage.style.padding = '10px 20px';
      statusMessage.style.backgroundColor = '#f0f0f0';
      statusMessage.style.borderLeft = '4px solid #0972d3';
      statusMessage.style.boxShadow = '0 2px 8px rgba(0,0,0,0.15)';
      statusMessage.style.zIndex = '5000';
      statusMessage.textContent = 'Testing connection...';
      document.body.appendChild(statusMessage);

      const testServer = { ...selectedServer, isConnected: true };

      let connectionMessage = 'Unknown error';
      let isServerReachable = false;

      if (testServer.connectionType === 'url' && testServer.url) {
        try {
          statusMessage.textContent = 'Checking server reachability...';
          const response = await fetch(testServer.url, {
            method: 'HEAD',
            signal: AbortSignal.timeout(5000)
          }).catch(e => {
            console.error('Server connectivity check failed:', e);
            connectionMessage = `Server unreachable: ${e.message}`;
            return null;
          });

          if (!response) {
            document.body.removeChild(statusMessage);
            alert(`Connection failed: ${connectionMessage}`);
            updateServerStatus(selectedServer.id, false);
            return;
          }

          isServerReachable = true;
        } catch (fetchError) {
          console.error('URL connection test failed:', fetchError);
          connectionMessage = `Cannot connect to server: ${fetchError.message}`;
          document.body.removeChild(statusMessage);
          alert(`Connection failed: ${connectionMessage}`);
          updateServerStatus(selectedServer.id, false);
          return;
        }
      }

      statusMessage.textContent = 'Testing MCP protocol...';

      const success = await addServer(testServer);

      document.body.removeChild(statusMessage);

      if (success) {
        const client = getClient(selectedServer.id);

        if (client) {
          try {
            await client.client.request('mcp.discover', {});
            alert('Connection successful! Server responded to MCP request.');

            updateServerStatus(selectedServer.id, true);
          } catch (requestError) {
            console.error('MCP request test failed:', requestError);
            alert(`Server is reachable, but MCP request failed: ${requestError.message}`);
            updateServerStatus(selectedServer.id, false);
          }
        } else {
          alert('Server is reachable, but MCP client creation failed.');
          updateServerStatus(selectedServer.id, false);
        }
      } else {
        alert(`Connection failed: Unable to establish MCP connection.`);
        updateServerStatus(selectedServer.id, false);
      }
    } catch (err) {
      alert(`Connection test error: ${err instanceof Error ? err.message : String(err)}`);
      if (selectedServer) {
        updateServerStatus(selectedServer.id, false);
      }
    } finally {
      setIsLoading(false);
      setTestingServerId(null);
    }
  };

  const handleRefreshConnections = async (refreshAll = false) => {
    setIsRefreshing(true);

    try {
      const serversToRefresh = refreshAll
        ? servers
        : servers.filter(server => !server.isConnected);

      if (serversToRefresh.length === 0) {
        alert('No servers to refresh.');
        setIsRefreshing(false);
        return;
      }

      const statusToast = document.createElement('div');
      statusToast.style.position = 'fixed';
      statusToast.style.bottom = '20px';
      statusToast.style.right = '20px';
      statusToast.style.padding = '12px 20px';
      statusToast.style.backgroundColor = '#f0f0f0';
      statusToast.style.borderLeft = '4px solid #0972d3';
      statusToast.style.boxShadow = '0 2px 8px rgba(0,0,0,0.15)';
      statusToast.style.zIndex = '5000';
      statusToast.textContent = `Refreshing ${serversToRefresh.length} server${serversToRefresh.length > 1 ? 's' : ''}...`;
      document.body.appendChild(statusToast);

      let successCount = 0;

      for (const server of serversToRefresh) {
        try {
          statusToast.textContent = `Checking connection for server "${server.name}"...`;

          let isReachable = false;

          if (server.connectionType === 'url' && server.url) {
            try {
              const response = await fetch(server.url, {
                method: 'HEAD',
                signal: AbortSignal.timeout(3000)
              }).catch(() => null);

              isReachable = !!response;
            } catch (error) {
              console.error(`Server ${server.name} unreachable:`, error);
            }
          }

          if (!isReachable) {
            console.log(`Server ${server.name} unreachable, skipping connection attempt.`);
            // Mark as disconnected if it was previously marked as connected
            if (server.isConnected) {
              updateServerStatus(server.id, false);
            }
            continue;
          }

          statusToast.textContent = `Connecting to server "${server.name}"...`;

          const serverToConnect = { ...server, isConnected: true };

          const success = await addServer(serverToConnect);

          if (success) {
            successCount++;
            updateServerStatus(server.id, true);
          } else {
            // Explicitly mark as disconnected if connection failed
            updateServerStatus(server.id, false);
          }
        } catch (error) {
          console.error(`Error connecting to server ${server.name}:`, error);
          // Ensure server is marked as disconnected on error
          updateServerStatus(server.id, false);
        }
      }

      statusToast.textContent = `Refresh complete: ${successCount} server${successCount !== 1 ? 's' : ''} connected, ${serversToRefresh.length - successCount} failed`;
      statusToast.style.borderLeft = successCount > 0 ? '4px solid #1d8102' : '4px solid #d13212';

      setTimeout(() => {
        if (document.body.contains(statusToast)) {
          document.body.removeChild(statusToast);
        }
      }, 5000);

    } catch (error) {
      console.error('Error refreshing connections:', error);
      alert(`Error refreshing connections: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setIsRefreshing(false);
    }
  };

  const filteredServers = servers.filter(server =>
    server.name.toLowerCase().includes(filterText.toLowerCase()) ||
    (server.url && server.url.toLowerCase().includes(filterText.toLowerCase())) ||
    (server.command && server.command.toLowerCase().includes(filterText.toLowerCase())) ||
    (server.description && server.description.toLowerCase().includes(filterText.toLowerCase()))
  );

  const itemsPerPage = 5;
  const startIndex = (currentPage - 1) * itemsPerPage;
  const visibleServers = filteredServers.slice(startIndex, startIndex + itemsPerPage);

  return (
    <Container
      variant="default"
      header={
        <Header
          variant="h2"
          actions={
            <SpaceBetween direction="horizontal" size="xs">
              {servers.length > 0 && (
                <ButtonDropdown
                  items={[
                    { id: "refresh-all", text: "Refresh All Servers" },
                    { id: "refresh-disconnected", text: "Refresh Disconnected" },
                  ]}
                  onItemClick={({ detail }) => {
                    if (detail.id === "refresh-all") {
                      handleRefreshConnections(true);
                    } else {
                      handleRefreshConnections(false);
                    }
                  }}
                  loading={isRefreshing}
                  disabled={isLoading || isConnecting}
                >
                  Refresh
                </ButtonDropdown>
              )}
              <Button
                variant="primary"
                onClick={() => setIsAddServerModalVisible(true)}
                // disabled
              >
                Add MCP Server
              </Button>
            </SpaceBetween>
          }
        >
          MCP Servers
        </Header>
      }
    >
      {servers.length === 0 ? (
        <div style={{ textAlign: 'center', padding: '70px 0 120px 0' }}>
          <div style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '8px' }}>No MCP Servers Added Yet</div>
          <div>Add MCP servers to enhance AI capabilities</div>
          <div>Disabled adding mcp server for normal users</div>
        </div>
      ) : (
        <>
          <div style={{ maxWidth: '274px' }}>
            <TextFilter
              filteringText={filterText}
              filteringPlaceholder="Find servers"
              filteringAriaLabel="Filter servers"
              onChange={({ detail }) => setFilterText(detail.filteringText)}
            />
          </div>

          <div style={{ marginBottom: '16px' }}></div>

          <Cards<MCPServer>
            cardDefinition={{
              header: item => item.name,
              sections: [
                {
                  id: "details",
                  content: item => (
                    <SpaceBetween size="l">
                      <div>
                        <Box variant="awsui-key-label">Connection Type</Box>
                        <div>{item.connectionType === 'url' ? 'URL' : 'Command Line'}</div>
                      </div>
                      {item.url && (
                        <div>
                          <Box variant="awsui-key-label">URL</Box>
                          <div>{item.url}</div>
                        </div>
                      )}
                      {item.command && (
                        <div>
                          <Box variant="awsui-key-label">Command</Box>
                          <div>{item.command} {item.args}</div>
                        </div>
                      )}
                      {item.description && (
                        <div>
                          <Box variant="awsui-key-label">Description</Box>
                          <div>{item.description}</div>
                        </div>
                      )}
                      <div>
                        <Box variant="awsui-key-label">Status</Box>
                        <div>
                          {testingServerId === item.id ? (
                            <span style={{ color: '#6d6d6d' }}>
                              <Spinner size="normal" /> Testing connection...
                            </span>
                          ) : item.isConnected ? (
                            <span style={{ color: '#1d8102' }}>
                              <Icon name="status-positive" /> Connected
                            </span>
                          ) : (
                            <span style={{ color: '#d13212' }}>
                              <Icon name="status-negative" /> Disconnected
                            </span>
                          )}
                        </div>
                      </div>
                    </SpaceBetween>
                  )
                }
              ]
            }}
            cardsPerRow={[
              { cards: 2 },
              { minWidth: 700, cards: 3 },
              { minWidth: 1000, cards: 4 }
            ]}
            items={visibleServers}
            loading={isLoading || isConnecting}
            empty={
              <Box textAlign="center" color="inherit">
                <b>No servers found</b>
                <Box padding={{ bottom: "s" }} variant="p" color="inherit">
                  No MCP servers match the current filter criteria.
                </Box>
              </Box>
            }
            variant="full-page"
            selectionType="single"
            selectedItems={selectedServer ? [selectedServer] : []}
            onSelectionChange={({ detail }) => {
              if (detail.selectedItems.length > 0) {
                setSelectedServer(detail.selectedItems[0]);
              } else {
                setSelectedServer(null);
              }
            }}
            stickyHeader={false}
            trackBy="id"
            visibleSections={["details"]}
            header={
              <div style={{ marginTop: '8px', marginBottom: '8px' }}>
                <SpaceBetween size="m" direction="horizontal">
                  <Button
                    onClick={handleTestConnection}
                    loading={isLoading && selectedServer && testingServerId === selectedServer.id}
                    disabled={!selectedServer || isLoading}>
                    Test Connection
                  </Button>
                  <Button
                    onClick={handleRemoveServer}
                    disabled={!selectedServer || isLoading}>
                    Delete
                  </Button>
                </SpaceBetween>
              </div>
            }
          />

          <div style={{ maxWidth: '300px' }}>
            <Pagination
              currentPageIndex={currentPage}
              pagesCount={Math.max(1, Math.ceil(filteredServers.length / itemsPerPage))}
              onChange={({ detail }) => setCurrentPage(detail.currentPageIndex)}
            />
          </div>
        </>
      )}

      <Modal
        visible={isAddServerModalVisible}
        header="Add MCP Server"
        footer={
          <Box float="right">
            <SpaceBetween direction="horizontal" size="xs">
              <Button variant="link" onClick={() => setIsAddServerModalVisible(false)}>
                Cancel
              </Button>
              <Button variant="primary" onClick={handleAddServer} loading={isLoading}>
                Add Server
              </Button>
            </SpaceBetween>
          </Box>
        }
        onDismiss={() => setIsAddServerModalVisible(false)}
      >
        <Form>
          <SpaceBetween direction="vertical" size="l">
            {error && (
              <Alert type="error">{error}</Alert>
            )}

            <FormField label="Server Name" description="Name for the MCP server">
              <Input
                value={serverName}
                onChange={({ detail }) => setServerName(detail.value)}
              />
            </FormField>

            <Tabs
              tabs={[
                {
                  id: "url",
                  label: "URL Connection",
                  content: (
                    <FormField
                      label="Server URL"
                      description="URL of the MCP server"
                    >
                      <Input
                        value={serverUrl}
                        onChange={({ detail }) => setServerUrl(detail.value)}
                      />
                    </FormField>
                  )
                },
                {
                  id: "command",
                  label: (
                    <span>
                      Command Line
                      {isBrowserEnvironment && (
                        <Popover
                          size="medium"
                          position="top"
                          dismissButton={false}
                          triggerType="custom"
                          content={
                            <StatusIndicator type="warning">
                              Command line mode is not supported in web browsers. This option is for reference only.
                            </StatusIndicator>
                          }
                        >
                          <span style={{ marginLeft: '5px', color: '#d13212' }}>
                            <Icon name="status-warning" />
                          </span>
                        </Popover>
                      )}
                    </span>
                  ),
                  content: (
                    <SpaceBetween direction="vertical" size="l">
                      {isBrowserEnvironment && (
                        <Alert type="warning">
                          <h4>Browser Limitations (Not working now)</h4>
                          {/*<p>Due to security restrictions, command line mode is not supported in web browsers.</p>*/}
                          {/*<p>To use MCP-Doc, you need to:</p>*/}
                          {/*<ol>*/}
                          {/*  <li>Start the server with HTTP support in a terminal: <code>python3 server.py --http</code></li>*/}
                          {/*  <li>Then connect using URL mode to the server address</li>*/}
                          {/*</ol>*/}
                          {/*<p>If the server doesn't support HTTP mode, consider using a desktop application or creating an HTTP proxy.</p>*/}
                        </Alert>
                      )}
                      <FormField
                        label="Command"
                        description="Command to start the MCP server"
                      >
                        <Input
                          value={serverCommand}
                          onChange={({ detail }) => setServerCommand(detail.value)}
                          disabled={isBrowserEnvironment}
                        />
                      </FormField>
                      <FormField
                        label="Arguments"
                        description="Optional arguments for the command"
                      >
                        <Input
                          value={serverArgs}
                          onChange={({ detail }) => setServerArgs(detail.value)}
                          disabled={isBrowserEnvironment}
                        />
                      </FormField>
                    </SpaceBetween>
                  )
                }
              ]}
              onChange={({ detail }) => {
                setActiveTabId(detail.activeTabId);
                setConnectionType(detail.activeTabId as 'url' | 'command');
              }}
              activeTabId={activeTabId}
            />

            <FormField label="Description" description="Optional description of the server">
              <Input
                value={serverDescription}
                onChange={({ detail }) => setServerDescription(detail.value)}
              />
            </FormField>

            <ExpandableSection headerText="What is MCP?">
              <p>
                {/*Model Context Protocol (MCP) enhances AI capabilities by connecting to external servers that provide additional tools, data sources, and prompt templates.*/}
              </p>
              <p>
                TBA
                {/*Adding MCP servers allows this application to access specialized functionality such as database queries, file system access, API interactions, and more.*/}
              </p>
              <h4>Using MCP-Doc in a Browser</h4>
              <p>To use MCP-Doc in this application, follow these steps:</p>
              <ol>
                {/*<li>Open a terminal/command prompt</li>*/}
                {/*<li>Install required dependencies: <code>pip install python-docx mcp</code></li>*/}
                {/*<li>Start the server with HTTP support: <code>python3 /path/to/server.py --http-port 3000</code></li>*/}
                <li>Start the server with HTTP support (Only support this method now)</li>
                <li>Add the server using URL mode</li>
              </ol>
              <p>If the serer doesn't directly support HTTP mode, you'll need to use an HTTP proxy or desktop application.</p>
            </ExpandableSection>
          </SpaceBetween>
        </Form>
      </Modal>
    </Container>
  );
};

export default MCPServerManager;

// 2025-5-2 只刷新 disconnected
// import React, { useState, useEffect } from 'react';
// import {
//   Container,
//   Header,
//   Button,
//   Modal,
//   Form,
//   SpaceBetween,
//   FormField,
//   Input,
//   Alert,
//   Box,
//   TextFilter,
//   Pagination,
//   Cards,
//   ExpandableSection,
//   Icon,
//   Toggle,
//   Tabs,
//   Link,
//   Popover,
//   StatusIndicator,
//   Spinner,
//   ButtonDropdown
// } from '@cloudscape-design/components';
// import { useMCP } from '../../contexts/mcp-context';
//
// interface MCPServer {
//   id: string;
//   name: string;
//   url?: string;
//   command?: string;
//   args?: string;
//   description?: string;
//   isConnected: boolean;
//   connectionType: 'url' | 'command';
// }
//
// const MCPServerManager: React.FC = () => {
//   const { servers, addServer, removeServer, getClient, isConnecting, updateServerStatus } = useMCP();
//   const [isAddServerModalVisible, setIsAddServerModalVisible] = useState(false);
//   const [serverUrl, setServerUrl] = useState('');
//   const [serverName, setServerName] = useState('');
//   const [serverDescription, setServerDescription] = useState('');
//   const [serverCommand, setServerCommand] = useState('');
//   const [serverArgs, setServerArgs] = useState('');
//   const [connectionType, setConnectionType] = useState<'url' | 'command'>('url');
//   const [error, setError] = useState<string | null>(null);
//   const [currentPage, setCurrentPage] = useState(1);
//   const [filterText, setFilterText] = useState('');
//   const [isLoading, setIsLoading] = useState(false);
//   const [selectedServer, setSelectedServer] = useState<MCPServer | null>(null);
//   const [activeTabId, setActiveTabId] = useState('url');
//   const [testingServerId, setTestingServerId] = useState<string | null>(null);
//   const [isRefreshing, setIsRefreshing] = useState(false);
//
//   const isBrowserEnvironment = typeof window !== 'undefined' && typeof window.document !== 'undefined';
//
//   const handleAddServer = async () => {
//     setIsLoading(true);
//     setError(null);
//
//     if (!serverName) {
//       setError('Server name is required');
//       setIsLoading(false);
//       return;
//     }
//
//     if (connectionType === 'url' && !serverUrl) {
//       setError('Server URL is required for URL connection type');
//       setIsLoading(false);
//       return;
//     }
//
//     if (connectionType === 'command' && !serverCommand) {
//       setError('Command is required for Command connection type');
//       setIsLoading(false);
//       return;
//     }
//
//     if (isBrowserEnvironment && connectionType === 'command') {
//       setError('Command mode is not supported in browser environments. Please use URL mode instead or start an HTTP server.');
//       setIsLoading(false);
//       return;
//     }
//
//     try {
//       const newServer: MCPServer = {
//         id: Date.now().toString(),
//         name: serverName,
//         description: serverDescription,
//         isConnected: false,
//         connectionType: connectionType
//       };
//
//       if (connectionType === 'url') {
//         newServer.url = serverUrl;
//
//         try {
//           const response = await fetch(serverUrl, {
//             method: 'HEAD',
//             signal: AbortSignal.timeout(3000)
//           }).catch(() => null);
//
//           if (!response) {
//             setError('Server is unreachable, added but displayed as disconnected.');
//             const updatedServers = [...servers, newServer];
//             await addServer(newServer);
//             setIsAddServerModalVisible(false);
//             resetForm();
//             return;
//           }
//         } catch (error) {
//           console.error('Error testing server connection:', error);
//           setError('Server connection test failed, added but displayed as disconnected.');
//           await addServer(newServer);
//           setIsAddServerModalVisible(false);
//           resetForm();
//           return;
//         }
//
//         newServer.isConnected = true;
//       } else {
//         newServer.command = serverCommand;
//         newServer.args = serverArgs;
//       }
//
//       const success = await addServer(newServer);
//
//       if (success) {
//         setIsAddServerModalVisible(false);
//         resetForm();
//       } else {
//         setError('Unable to connect to MCP server, added but displayed as disconnected.');
//         updateServerStatus(newServer.id, false);
//       }
//     } catch (err) {
//       setError(`Error: ${err instanceof Error ? err.message : String(err)}`);
//     } finally {
//       setIsLoading(false);
//     }
//   };
//
//   const resetForm = () => {
//     setServerName('');
//     setServerUrl('');
//     setServerCommand('');
//     setServerArgs('');
//     setServerDescription('');
//     setConnectionType('url');
//     setActiveTabId('url');
//   };
//
//   const handleRemoveServer = () => {
//     if (selectedServer) {
//       removeServer(selectedServer.id);
//       setSelectedServer(null);
//     }
//   };
//
//   const handleTestConnection = async () => {
//     if (!selectedServer) return;
//
//     try {
//       setIsLoading(true);
//       setTestingServerId(selectedServer.id);
//
//       const statusMessage = document.createElement('div');
//       statusMessage.style.position = 'fixed';
//       statusMessage.style.bottom = '20px';
//       statusMessage.style.right = '20px';
//       statusMessage.style.padding = '10px 20px';
//       statusMessage.style.backgroundColor = '#f0f0f0';
//       statusMessage.style.borderLeft = '4px solid #0972d3';
//       statusMessage.style.boxShadow = '0 2px 8px rgba(0,0,0,0.15)';
//       statusMessage.style.zIndex = '5000';
//       statusMessage.textContent = 'Testing connection...';
//       document.body.appendChild(statusMessage);
//
//       const testServer = { ...selectedServer, isConnected: true };
//
//       let connectionMessage = 'Unknown error';
//       let isServerReachable = false;
//
//       if (testServer.connectionType === 'url' && testServer.url) {
//         try {
//           statusMessage.textContent = 'Checking server reachability...';
//           const response = await fetch(testServer.url, {
//             method: 'HEAD',
//             signal: AbortSignal.timeout(5000)
//           }).catch(e => {
//             console.error('Server connectivity check failed:', e);
//             connectionMessage = `Server unreachable: ${e.message}`;
//             return null;
//           });
//
//           if (!response) {
//             document.body.removeChild(statusMessage);
//             alert(`Connection failed: ${connectionMessage}`);
//             updateServerStatus(selectedServer.id, false);
//             return;
//           }
//
//           isServerReachable = true;
//         } catch (fetchError) {
//           console.error('URL connection test failed:', fetchError);
//           connectionMessage = `Cannot connect to server: ${fetchError.message}`;
//           document.body.removeChild(statusMessage);
//           alert(`Connection failed: ${connectionMessage}`);
//           updateServerStatus(selectedServer.id, false);
//           return;
//         }
//       }
//
//       statusMessage.textContent = 'Testing MCP protocol...';
//
//       const success = await addServer(testServer);
//
//       document.body.removeChild(statusMessage);
//
//       if (success) {
//         const client = getClient(selectedServer.id);
//
//         if (client) {
//           try {
//             await client.client.request('mcp.discover', {});
//             alert('Connection successful! Server responded to MCP request.');
//
//             updateServerStatus(selectedServer.id, true);
//           } catch (requestError) {
//             console.error('MCP request test failed:', requestError);
//             alert(`Server is reachable, but MCP request failed: ${requestError.message}`);
//             updateServerStatus(selectedServer.id, false);
//           }
//         } else {
//           alert('Server is reachable, but MCP client creation failed.');
//           updateServerStatus(selectedServer.id, false);
//         }
//       } else {
//         alert(`Connection failed: Unable to establish MCP connection.`);
//         updateServerStatus(selectedServer.id, false);
//       }
//     } catch (err) {
//       alert(`Connection test error: ${err instanceof Error ? err.message : String(err)}`);
//       if (selectedServer) {
//         updateServerStatus(selectedServer.id, false);
//       }
//     } finally {
//       setIsLoading(false);
//       setTestingServerId(null);
//     }
//   };
//
//   const handleRefreshConnections = async () => {
//     setIsRefreshing(true);
//
//     try {
//       const disconnectedServers = servers.filter(server => !server.isConnected);
//
//       if (disconnectedServers.length === 0) {
//         alert('No disconnected servers to refresh.');
//         setIsRefreshing(false);
//         return;
//       }
//
//       const statusToast = document.createElement('div');
//       statusToast.style.position = 'fixed';
//       statusToast.style.bottom = '20px';
//       statusToast.style.right = '20px';
//       statusToast.style.padding = '12px 20px';
//       statusToast.style.backgroundColor = '#f0f0f0';
//       statusToast.style.borderLeft = '4px solid #0972d3';
//       statusToast.style.boxShadow = '0 2px 8px rgba(0,0,0,0.15)';
//       statusToast.style.zIndex = '5000';
//       statusToast.textContent = `Refreshing ${disconnectedServers.length} server connections...`;
//       document.body.appendChild(statusToast);
//
//       let successCount = 0;
//
//       for (const server of disconnectedServers) {
//         try {
//           statusToast.textContent = `Checking connection for server "${server.name}"...`;
//
//           let isReachable = false;
//
//           if (server.connectionType === 'url' && server.url) {
//             try {
//               const response = await fetch(server.url, {
//                 method: 'HEAD',
//                 signal: AbortSignal.timeout(3000)
//               }).catch(() => null);
//
//               isReachable = !!response;
//             } catch (error) {
//               console.error(`Server ${server.name} unreachable:`, error);
//             }
//           }
//
//           if (!isReachable) {
//             console.log(`Server ${server.name} unreachable, skipping connection attempt.`);
//             continue;
//           }
//
//           statusToast.textContent = `Connecting to server "${server.name}"...`;
//
//           const serverToConnect = { ...server, isConnected: true };
//
//           const success = await addServer(serverToConnect);
//
//           if (success) {
//             successCount++;
//             updateServerStatus(server.id, true);
//           }
//         } catch (error) {
//           console.error(`Error connecting to server ${server.name}:`, error);
//         }
//       }
//
//       statusToast.textContent = `Refresh complete: ${successCount} servers connected, ${disconnectedServers.length - successCount} failed`;
//       statusToast.style.borderLeft = successCount > 0 ? '4px solid #1d8102' : '4px solid #d13212';
//
//       setTimeout(() => {
//         if (document.body.contains(statusToast)) {
//           document.body.removeChild(statusToast);
//         }
//       }, 5000);
//
//     } catch (error) {
//       console.error('Error refreshing connections:', error);
//       alert(`Error refreshing connections: ${error instanceof Error ? error.message : String(error)}`);
//     } finally {
//       setIsRefreshing(false);
//     }
//   };
//
//   const filteredServers = servers.filter(server =>
//     server.name.toLowerCase().includes(filterText.toLowerCase()) ||
//     (server.url && server.url.toLowerCase().includes(filterText.toLowerCase())) ||
//     (server.command && server.command.toLowerCase().includes(filterText.toLowerCase())) ||
//     (server.description && server.description.toLowerCase().includes(filterText.toLowerCase()))
//   );
//
//   const itemsPerPage = 5;
//   const startIndex = (currentPage - 1) * itemsPerPage;
//   const visibleServers = filteredServers.slice(startIndex, startIndex + itemsPerPage);
//
//   return (
//     <Container
//       variant="default"
//       header={
//         <Header
//           variant="h2"
//           actions={
//             <SpaceBetween direction="horizontal" size="xs">
//               {servers.length > 0 && (
//                 <Button
//                   iconName="refresh"
//                   onClick={handleRefreshConnections}
//                   loading={isRefreshing}
//                   disabled={isLoading || isConnecting}
//                 >
//                   Refresh Connections
//                 </Button>
//               )}
//               <Button
//                 variant="primary"
//                 onClick={() => setIsAddServerModalVisible(true)}
//               >
//                 Add MCP Server
//               </Button>
//             </SpaceBetween>
//           }
//         >
//           MCP Servers
//         </Header>
//       }
//     >
//       {servers.length === 0 ? (
//         <div style={{ textAlign: 'center', padding: '70px 0 120px 0' }}>
//           <div style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '8px' }}>No MCP Servers Added Yet</div>
//           <div>Add MCP servers to enhance AI capabilities</div>
//         </div>
//       ) : (
//         <>
//           <div style={{ maxWidth: '300px' }}>
//             <TextFilter
//               filteringText={filterText}
//               filteringPlaceholder="Find servers"
//               filteringAriaLabel="Filter servers"
//               onChange={({ detail }) => setFilterText(detail.filteringText)}
//             />
//           </div>
//
//           <div style={{ marginBottom: '16px' }}></div>
//
//           <Cards<MCPServer>
//             cardDefinition={{
//               header: item => item.name,
//               sections: [
//                 {
//                   id: "details",
//                   content: item => (
//                     <SpaceBetween size="l">
//                       <div>
//                         <Box variant="awsui-key-label">Connection Type</Box>
//                         <div>{item.connectionType === 'url' ? 'URL' : 'Command Line'}</div>
//                       </div>
//                       {item.url && (
//                         <div>
//                           <Box variant="awsui-key-label">URL</Box>
//                           <div>{item.url}</div>
//                         </div>
//                       )}
//                       {item.command && (
//                         <div>
//                           <Box variant="awsui-key-label">Command</Box>
//                           <div>{item.command} {item.args}</div>
//                         </div>
//                       )}
//                       {item.description && (
//                         <div>
//                           <Box variant="awsui-key-label">Description</Box>
//                           <div>{item.description}</div>
//                         </div>
//                       )}
//                       <div>
//                         <Box variant="awsui-key-label">Status</Box>
//                         <div>
//                           {testingServerId === item.id ? (
//                             <span style={{ color: '#6d6d6d' }}>
//                               <Spinner size="normal" /> Testing connection...
//                             </span>
//                           ) : item.isConnected ? (
//                             <span style={{ color: '#1d8102' }}>
//                               <Icon name="status-positive" /> Connected
//                             </span>
//                           ) : (
//                             <span style={{ color: '#d13212' }}>
//                               <Icon name="status-negative" /> Disconnected
//                             </span>
//                           )}
//                         </div>
//                       </div>
//                     </SpaceBetween>
//                   )
//                 }
//               ]
//             }}
//             cardsPerRow={[
//               { cards: 2 },
//               { minWidth: 700, cards: 3 },
//               { minWidth: 1000, cards: 4 }
//             ]}
//             items={visibleServers}
//             loading={isLoading || isConnecting}
//             empty={
//               <Box textAlign="center" color="inherit">
//                 <b>No servers found</b>
//                 <Box padding={{ bottom: "s" }} variant="p" color="inherit">
//                   No MCP servers match the current filter criteria.
//                 </Box>
//               </Box>
//             }
//             variant="full-page"
//             selectionType="single"
//             selectedItems={selectedServer ? [selectedServer] : []}
//             onSelectionChange={({ detail }) => {
//               if (detail.selectedItems.length > 0) {
//                 setSelectedServer(detail.selectedItems[0]);
//               } else {
//                 setSelectedServer(null);
//               }
//             }}
//             stickyHeader={false}
//             trackBy="id"
//             visibleSections={["details"]}
//             header={
//               <div style={{ marginTop: '8px', marginBottom: '8px' }}>
//                 <SpaceBetween size="m" direction="horizontal">
//                   <Button
//                     onClick={handleTestConnection}
//                     loading={isLoading && selectedServer && testingServerId === selectedServer.id}
//                     disabled={!selectedServer || isLoading}>
//                     Test Connection
//                   </Button>
//                   <Button
//                     onClick={handleRemoveServer}
//                     disabled={!selectedServer || isLoading}>
//                     Delete
//                   </Button>
//                 </SpaceBetween>
//               </div>
//             }
//           />
//
//           <div style={{ maxWidth: '300px' }}>
//             <Pagination
//               currentPageIndex={currentPage}
//               pagesCount={Math.max(1, Math.ceil(filteredServers.length / itemsPerPage))}
//               onChange={({ detail }) => setCurrentPage(detail.currentPageIndex)}
//             />
//           </div>
//         </>
//       )}
//
//       <Modal
//         visible={isAddServerModalVisible}
//         header="Add MCP Server"
//         footer={
//           <Box float="right">
//             <SpaceBetween direction="horizontal" size="xs">
//               <Button variant="link" onClick={() => setIsAddServerModalVisible(false)}>
//                 Cancel
//               </Button>
//               <Button variant="primary" onClick={handleAddServer} loading={isLoading}>
//                 Add Server
//               </Button>
//             </SpaceBetween>
//           </Box>
//         }
//         onDismiss={() => setIsAddServerModalVisible(false)}
//       >
//         <Form>
//           <SpaceBetween direction="vertical" size="l">
//             {error && (
//               <Alert type="error">{error}</Alert>
//             )}
//
//             <FormField label="Server Name" description="Name for the MCP server">
//               <Input
//                 value={serverName}
//                 onChange={({ detail }) => setServerName(detail.value)}
//               />
//             </FormField>
//
//             <Tabs
//               tabs={[
//                 {
//                   id: "url",
//                   label: "URL Connection",
//                   content: (
//                     <FormField
//                       label="Server URL"
//                       description="URL of the MCP server"
//                     >
//                       <Input
//                         value={serverUrl}
//                         onChange={({ detail }) => setServerUrl(detail.value)}
//                       />
//                     </FormField>
//                   )
//                 },
//                 {
//                   id: "command",
//                   label: (
//                     <span>
//                       Command Line
//                       {isBrowserEnvironment && (
//                         <Popover
//                           size="medium"
//                           position="top"
//                           dismissButton={false}
//                           triggerType="custom"
//                           content={
//                             <StatusIndicator type="warning">
//                               Command line mode is not supported in web browsers. This option is for reference only.
//                             </StatusIndicator>
//                           }
//                         >
//                           <span style={{ marginLeft: '5px', color: '#d13212' }}>
//                             <Icon name="status-warning" />
//                           </span>
//                         </Popover>
//                       )}
//                     </span>
//                   ),
//                   content: (
//                     <SpaceBetween direction="vertical" size="l">
//                       {isBrowserEnvironment && (
//                         <Alert type="warning">
//                           <h4>Browser Limitations (Not working now)</h4>
//                           {/*<p>Due to security restrictions, command line mode is not supported in web browsers.</p>*/}
//                           {/*<p>To use MCP-Doc, you need to:</p>*/}
//                           {/*<ol>*/}
//                           {/*  <li>Start the server with HTTP support in a terminal: <code>python3 server.py --http</code></li>*/}
//                           {/*  <li>Then connect using URL mode to the server address</li>*/}
//                           {/*</ol>*/}
//                           {/*<p>If the server doesn't support HTTP mode, consider using a desktop application or creating an HTTP proxy.</p>*/}
//                         </Alert>
//                       )}
//                       <FormField
//                         label="Command"
//                         description="Command to start the MCP server"
//                       >
//                         <Input
//                           value={serverCommand}
//                           onChange={({ detail }) => setServerCommand(detail.value)}
//                           disabled={isBrowserEnvironment}
//                         />
//                       </FormField>
//                       <FormField
//                         label="Arguments"
//                         description="Optional arguments for the command"
//                       >
//                         <Input
//                           value={serverArgs}
//                           onChange={({ detail }) => setServerArgs(detail.value)}
//                           disabled={isBrowserEnvironment}
//                         />
//                       </FormField>
//                     </SpaceBetween>
//                   )
//                 }
//               ]}
//               onChange={({ detail }) => {
//                 setActiveTabId(detail.activeTabId);
//                 setConnectionType(detail.activeTabId as 'url' | 'command');
//               }}
//               activeTabId={activeTabId}
//             />
//
//             <FormField label="Description" description="Optional description of the server">
//               <Input
//                 value={serverDescription}
//                 onChange={({ detail }) => setServerDescription(detail.value)}
//               />
//             </FormField>
//
//             <ExpandableSection headerText="What is MCP?">
//               <p>
//                 {/*Model Context Protocol (MCP) enhances AI capabilities by connecting to external servers that provide additional tools, data sources, and prompt templates.*/}
//               </p>
//               <p>
//                 TBA
//                 {/*Adding MCP servers allows this application to access specialized functionality such as database queries, file system access, API interactions, and more.*/}
//               </p>
//               <h4>Using MCP-Doc in a Browser</h4>
//               <p>To use MCP-Doc in this application, follow these steps:</p>
//               <ol>
//                 {/*<li>Open a terminal/command prompt</li>*/}
//                 {/*<li>Install required dependencies: <code>pip install python-docx mcp</code></li>*/}
//                 {/*<li>Start the server with HTTP support: <code>python3 /path/to/server.py --http-port 3000</code></li>*/}
//                 <li>Start the server with HTTP support (Only support this method now)</li>
//                 <li>Add the server using URL mode</li>
//               </ol>
//               <p>If the serer doesn't directly support HTTP mode, you'll need to use an HTTP proxy or desktop application.</p>
//             </ExpandableSection>
//           </SpaceBetween>
//         </Form>
//       </Modal>
//     </Container>
//   );
// };
//
// export default MCPServerManager;

// 原版
// import React, { useState, useEffect } from 'react';
// import {
//   Container,
//   Header,
//   Button,
//   Modal,
//   Form,
//   SpaceBetween,
//   FormField,
//   Input,
//   Alert,
//   Box,
//   TextFilter,
//   Pagination,
//   Cards,
//   ExpandableSection,
//   Icon,
//   Toggle,
//   Tabs,
//   Link,
//   Popover,
//   StatusIndicator
// } from '@cloudscape-design/components';
// import { useMCP } from '../../contexts/mcp-context';
//
// interface MCPServer {
//   id: string;
//   name: string;
//   url?: string;
//   command?: string;
//   args?: string;
//   description?: string;
//   isConnected: boolean;
//   connectionType: 'url' | 'command';
// }
//
// const MCPServerManager: React.FC = () => {
//   const { servers, addServer, removeServer, getClient, isConnecting } = useMCP();
//   const [isAddServerModalVisible, setIsAddServerModalVisible] = useState(false);
//   const [serverUrl, setServerUrl] = useState('');
//   const [serverName, setServerName] = useState('');
//   const [serverDescription, setServerDescription] = useState('');
//   const [serverCommand, setServerCommand] = useState('');
//   const [serverArgs, setServerArgs] = useState('');
//   const [connectionType, setConnectionType] = useState<'url' | 'command'>('url');
//   const [error, setError] = useState<string | null>(null);
//   const [currentPage, setCurrentPage] = useState(1);
//   const [filterText, setFilterText] = useState('');
//   const [isLoading, setIsLoading] = useState(false);
//   const [selectedServer, setSelectedServer] = useState<MCPServer | null>(null);
//   const [activeTabId, setActiveTabId] = useState('url');
//
//   const isBrowserEnvironment = typeof window !== 'undefined' && typeof window.document !== 'undefined';
//
//   const handleAddServer = async () => {
//     setIsLoading(true);
//     setError(null);
//
//     if (!serverName) {
//       setError('Server name is required');
//       setIsLoading(false);
//       return;
//     }
//
//     if (connectionType === 'url' && !serverUrl) {
//       setError('Server URL is required for URL connection type');
//       setIsLoading(false);
//       return;
//     }
//
//     if (connectionType === 'command' && !serverCommand) {
//       setError('Command is required for Command connection type');
//       setIsLoading(false);
//       return;
//     }
//
//     // For browser environments, warn about command mode limitations
//     if (isBrowserEnvironment && connectionType === 'command') {
//       setError('Command mode is not supported in browser environments. Please use URL mode instead or start an HTTP server.');
//       setIsLoading(false);
//       return;
//     }
//
//     try {
//       // Create new server object
//       const newServer: MCPServer = {
//         id: Date.now().toString(),
//         name: serverName,
//         description: serverDescription,
//         isConnected: true,
//         connectionType: connectionType
//       };
//
//       // Add connection specific properties
//       if (connectionType === 'url') {
//         newServer.url = serverUrl;
//       } else {
//         newServer.command = serverCommand;
//         newServer.args = serverArgs;
//       }
//
//       // Try to add the server (this will attempt connection)
//       const success = await addServer(newServer);
//
//       if (success) {
//         // Close modal and reset form on success
//         setIsAddServerModalVisible(false);
//         resetForm();
//       } else {
//         setError('Failed to connect to MCP server');
//       }
//     } catch (err) {
//       setError(`Error: ${err instanceof Error ? err.message : String(err)}`);
//     } finally {
//       setIsLoading(false);
//     }
//   };
//
//   const resetForm = () => {
//     setServerName('');
//     setServerUrl('');
//     setServerCommand('');
//     setServerArgs('');
//     setServerDescription('');
//     setConnectionType('url');
//     setActiveTabId('url');
//   };
//
//   const handleRemoveServer = () => {
//     if (selectedServer) {
//       removeServer(selectedServer.id);
//       setSelectedServer(null);
//     }
//   };
//
//   const handleTestConnection = async () => {
//     if (!selectedServer) return;
//
//     try {
//       const client = getClient(selectedServer.id);
//       if (client) {
//         // If client exists, it's already connected
//         alert('Connection successful!');
//       } else {
//         // Try to connect by adding the server with connection flag
//         const updatedServer = { ...selectedServer, isConnected: true };
//         const success = await addServer(updatedServer);
//
//         if (success) {
//           alert('Connection successful!');
//         } else {
//           alert('Connection failed');
//         }
//       }
//     } catch (err) {
//       alert(`Connection failed: ${err instanceof Error ? err.message : String(err)}`);
//     }
//   };
//
//   const filteredServers = servers.filter(server =>
//     server.name.toLowerCase().includes(filterText.toLowerCase()) ||
//     (server.url && server.url.toLowerCase().includes(filterText.toLowerCase())) ||
//     (server.command && server.command.toLowerCase().includes(filterText.toLowerCase())) ||
//     (server.description && server.description.toLowerCase().includes(filterText.toLowerCase()))
//   );
//
//   const itemsPerPage = 5;
//   const startIndex = (currentPage - 1) * itemsPerPage;
//   const visibleServers = filteredServers.slice(startIndex, startIndex + itemsPerPage);
//
//   return (
//     <Container
//       variant="default"
//       header={
//         <Header
//           variant="h2"
//           actions={
//             <Button
//               variant="primary"
//               onClick={() => setIsAddServerModalVisible(true)}
//             >
//               Add MCP Server
//             </Button>
//           }
//         >
//           MCP Servers
//         </Header>
//       }
//     >
//       {servers.length === 0 ? (
//         <div style={{ textAlign: 'center', padding: '70px 0 120px 0' }}>
//           <div style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '8px' }}>No MCP servers added yet</div>
//           <div>Add an MCP server to enhance AI capabilities</div>
//         </div>
//       ) : (
//         <>
//           <div style={{ maxWidth: '272px' }}>
//             <TextFilter
//               filteringText={filterText}
//               filteringPlaceholder="Find servers"
//               filteringAriaLabel="Filter servers"
//               onChange={({ detail }) => setFilterText(detail.filteringText)}
//             />
//           </div>
//
//           <div style={{ marginBottom: '16px' }}></div>
//
//           <Cards<MCPServer>
//             cardDefinition={{
//               header: item => item.name,
//               sections: [
//                 {
//                   id: "details",
//                   content: item => (
//                     <SpaceBetween size="l">
//                       <div>
//                         <Box variant="awsui-key-label">Connection Type</Box>
//                         <div>{item.connectionType === 'url' ? 'URL' : 'Command'}</div>
//                       </div>
//                       {item.url && (
//                         <div>
//                           <Box variant="awsui-key-label">URL</Box>
//                           <div>{item.url}</div>
//                         </div>
//                       )}
//                       {item.command && (
//                         <div>
//                           <Box variant="awsui-key-label">Command</Box>
//                           <div>{item.command} {item.args}</div>
//                         </div>
//                       )}
//                       {item.description && (
//                         <div>
//                           <Box variant="awsui-key-label">Description</Box>
//                           <div>{item.description}</div>
//                         </div>
//                       )}
//                       <div>
//                         <Box variant="awsui-key-label">Status</Box>
//                         <div>
//                           {item.isConnected ? (
//                             <span style={{ color: '#1d8102' }}>
//                               <Icon name="status-positive" /> Connected
//                             </span>
//                           ) : (
//                             <span style={{ color: '#d13212' }}>
//                               <Icon name="status-negative" /> Disconnected
//                             </span>
//                           )}
//                         </div>
//                       </div>
//                     </SpaceBetween>
//                   )
//                 }
//               ]
//             }}
//             cardsPerRow={[
//               { cards: 2 },
//               { minWidth: 700, cards: 3 },
//               { minWidth: 1000, cards: 4 }
//             ]}
//             items={visibleServers}
//             loading={isLoading || isConnecting}
//             empty={
//               <Box textAlign="center" color="inherit">
//                 <b>No servers found</b>
//                 <Box padding={{ bottom: "s" }} variant="p" color="inherit">
//                   No MCP servers match the current filter.
//                 </Box>
//               </Box>
//             }
//             variant="full-page"
//             selectionType="single"
//             selectedItems={selectedServer ? [selectedServer] : []}
//             onSelectionChange={({ detail }) => {
//               if (detail.selectedItems.length > 0) {
//                 setSelectedServer(detail.selectedItems[0]);
//               } else {
//                 setSelectedServer(null);
//               }
//             }}
//             stickyHeader={false}
//             trackBy="id"
//             visibleSections={["details"]}
//             header={
//               <div style={{ marginTop: '2px', marginBottom: '2px' }}>
//                 <SpaceBetween size="m" direction="horizontal">
//                   <Button
//                     onClick={handleTestConnection}
//                     disabled={!selectedServer}>
//                     Test Connection
//                   </Button>
//                   <Button
//                     onClick={handleRemoveServer}
//                     disabled={!selectedServer}>
//                     Remove
//                   </Button>
//                 </SpaceBetween>
//               </div>
//             }
//           />
//
//           <div style={{ maxWidth: '272px' }}>
//             <Pagination
//               currentPageIndex={currentPage}
//               pagesCount={Math.max(1, Math.ceil(filteredServers.length / itemsPerPage))}
//               onChange={({ detail }) => setCurrentPage(detail.currentPageIndex)}
//             />
//           </div>
//         </>
//       )}
//
//       <Modal
//         visible={isAddServerModalVisible}
//         header="Add MCP Server"
//         footer={
//           <Box float="right">
//             <SpaceBetween direction="horizontal" size="xs">
//               <Button variant="link" onClick={() => setIsAddServerModalVisible(false)}>
//                 Cancel
//               </Button>
//               <Button variant="primary" onClick={handleAddServer} loading={isLoading}>
//                 Add Server
//               </Button>
//             </SpaceBetween>
//           </Box>
//         }
//         onDismiss={() => setIsAddServerModalVisible(false)}
//       >
//         <Form>
//           <SpaceBetween direction="vertical" size="l">
//             {error && (
//               <Alert type="error">{error}</Alert>
//             )}
//
//             <FormField label="Server Name" description="A friendly name for this MCP server">
//               <Input
//                 value={serverName}
//                 onChange={({ detail }) => setServerName(detail.value)}
//               />
//             </FormField>
//
//             <Tabs
//               tabs={[
//                 {
//                   id: "url",
//                   label: "URL Connection",
//                   content: (
//                     <FormField
//                       label="Server URL"
//                       description="The URL of the MCP server (e.g., http://localhost:3000/mcp)"
//                     >
//                       <Input
//                         value={serverUrl}
//                         onChange={({ detail }) => setServerUrl(detail.value)}
//                       />
//                     </FormField>
//                   )
//                 },
//                 {
//                   id: "command",
//                   label: (
//                     <span>
//                       Command Line
//                       {isBrowserEnvironment && (
//                         <Popover
//                           size="medium"
//                           position="top"
//                           dismissButton={false}
//                           triggerType="custom"
//                           content={
//                             <StatusIndicator type="warning">
//                               Command line mode is not supported in web browsers. This option is shown for reference only.
//                             </StatusIndicator>
//                           }
//                         >
//                           <span style={{ marginLeft: '5px', color: '#d13212' }}>
//                             <Icon name="status-warning" />
//                           </span>
//                         </Popover>
//                       )}
//                     </span>
//                   ),
//                   content: (
//                     <SpaceBetween direction="vertical" size="l">
//                       {isBrowserEnvironment && (
//                         <Alert type="warning">
//                           <h4>Browser Limitation</h4>
//                           <p>Command line mode is not supported in web browsers due to security restrictions.</p>
//                           <p>To use MCP-Doc, you need to:</p>
//                           <ol>
//                             <li>Start the server with HTTP support in a terminal: <code>python3 server.py --http</code></li>
//                             <li>Then connect using URL mode with the server's address</li>
//                           </ol>
//                           <p>If the server doesn't support HTTP mode, consider using a desktop application or creating an HTTP proxy.</p>
//                         </Alert>
//                       )}
//                       <FormField
//                         label="Command"
//                         description="The command to start the MCP server (e.g., python3)"
//                       >
//                         <Input
//                           value={serverCommand}
//                           onChange={({ detail }) => setServerCommand(detail.value)}
//                           disabled={isBrowserEnvironment}
//                         />
//                       </FormField>
//                       <FormField
//                         label="Arguments"
//                         description="Optional arguments for the command (e.g., /path/to/server.py)"
//                       >
//                         <Input
//                           value={serverArgs}
//                           onChange={({ detail }) => setServerArgs(detail.value)}
//                           disabled={isBrowserEnvironment}
//                         />
//                       </FormField>
//                     </SpaceBetween>
//                   )
//                 }
//               ]}
//               onChange={({ detail }) => {
//                 setActiveTabId(detail.activeTabId);
//                 setConnectionType(detail.activeTabId as 'url' | 'command');
//               }}
//               activeTabId={activeTabId}
//             />
//
//             <FormField label="Description" description="Optional description for this server">
//               <Input
//                 value={serverDescription}
//                 onChange={({ detail }) => setServerDescription(detail.value)}
//               />
//             </FormField>
//
//             <ExpandableSection headerText="What is MCP?">
//               <p>
//                 The Model Context Protocol (MCP) enhances AI capabilities by connecting to external servers that can provide additional tools, data sources, and prompt templates.
//               </p>
//               <p>
//                 Adding an MCP server allows this application to access specialized capabilities such as database queries, file system access, API interactions, and more.
//               </p>
//               <h4>Using MCP-Doc in a Browser</h4>
//               <p>To use MCP-Doc with this application, follow these steps:</p>
//               <ol>
//                 <li>Open a terminal/command prompt</li>
//                 <li>Install required dependencies: <code>pip install python-docx mcp</code></li>
//                 <li>Start the server with HTTP support: <code>python3 /path/to/server.py --http-port 3000</code></li>
//                 <li>Add the server using URL mode: <code>http://localhost:3000/mcp</code></li>
//               </ol>
//               <p>If MCP-Doc doesn't support HTTP mode directly, you'll need to use an HTTP proxy or desktop application.</p>
//             </ExpandableSection>
//           </SpaceBetween>
//         </Form>
//       </Modal>
//     </Container>
//   );
// };
//
// export default MCPServerManager;