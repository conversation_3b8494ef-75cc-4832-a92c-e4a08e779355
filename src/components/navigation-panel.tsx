import {
  Button,
  ExpandableSection,
  Icon,
  SideNavigation,
  SideNavigationProps,
} from "@cloudscape-design/components";
import useOnFollow from "../common/hooks/use-on-follow";
import { useNavigationPanelState } from "../common/hooks/use-navigation-panel-state";
import { AppContext } from "../common/app-context";
import { useContext, useEffect, useState, useRef } from "react";
import { CHATBOT_NAME } from "../common/constants";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/redux/store/configureStore";
import newChatIcon from "../assets/images/newchat-icon.png";
import preview from "../assets/images/view.png";
import mcp from "../assets/images/mcp.png";
import { Route, useNavigate, useLocation } from "react-router-dom";
import { chatActions } from "@/pages/chatbot/playground/chat.slice";
import Playground from "@/pages/chatbot/playground/playground";
import ChatHistory from "./chatbot/chat-history";
import { motion, useCycle } from "framer-motion";
import { useDimensions } from "../common/hooks/use-dimensions";
import { MenuToggle } from "./MenuToggle";
import { NavigationMenu } from "./NavigationMenu";
import { MenuSection } from "./MenuSection";

// Sidebar动画变体
const sidebar = {
  open: (height = 1000) => ({
    clipPath: `circle(${height * 2 + 200}px at calc(100% - 40px) 40px)`,
    transition: {
      type: "spring",
      stiffness: 20,
      restDelta: 2
    }
  }),
  closed: {
    clipPath: "circle(19px at calc(100% - 59px) 23px)",
    transition: {
      delay: 0.5,
      type: "spring",
      stiffness: 400,
      damping: 40
    }
  }
};

// 圆形进度条组件
const CircularProgress = ({ percentage = 50, size = 50 }: { percentage?: number; size?: number }) => {
  const radius = (size - 8) / 2;
  const circumference = 2 * Math.PI * radius;
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (percentage / 100) * circumference;

  return (
    <div style={{ position: 'relative', width: size, height: size }}>
      <svg width={size} height={size} style={{ transform: 'rotate(-90deg)' }}>
        {/* 背景圆环 */}
        <circle cx={size / 2} cy={size / 2} r={radius} stroke="#e0e6ed" strokeWidth="4" fill="transparent"/>
        {/* 进度圆环 */}
        <circle cx={size / 2} cy={size / 2} r={radius} stroke="url(#progressGradient)" strokeWidth="3"
          fill="transparent"
          strokeDasharray={strokeDasharray}
          strokeDashoffset={strokeDashoffset}
          strokeLinecap="round"
          style={{ transition: 'stroke-dashoffset 0.3s ease' }}
        />
        {/* 渐变定义 */}
        <defs>
          <linearGradient id="progressGradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="#007CBA" />
            <stop offset="100%" stopColor="#00cc88" />
          </linearGradient>
        </defs>
      </svg>
      {/* 百分比文字 */}
      <div
        style={{ position: 'absolute', top: '50%', left: '50%', transform: 'translate(-50%, -50%)', fontFamily: 'Roboto', fontWeight: '500', fontSize: '12px',
          color: '#000000',
          lineHeight: '1'
        }}
      >
        {percentage}%
      </div>
    </div>
  );
};

export default function NavigationPanel() {
  const appContext = useContext(AppContext);
  const [activeHref, setActiveHref] = useState("#/page2");
  const onFollow = useOnFollow();
  const [navigationPanelState, setNavigationPanelState] =
    useNavigationPanelState();
  const [resetBgColor, setResetBgColor] = useState(false);
  const dataState = useSelector((state: RootState) => {
    return state.rootReducer;
  });
  const navigate = useNavigate();
  const chatHistoryRef = useRef();
  const dispatch = useDispatch();
  const userRoles = dataState?.chatReducer?.userRoles;
  const location = useLocation();

  const [items, setItems] = useState<SideNavigationProps.Item[]>([]);
  // 检查是否通过Preview按钮或Navigation导航而来，如果是则默认打开
  const shouldStartOpen = location.state?.fromPreviewButton || location.state?.fromNavigation || false;
  const [isOpen, toggleOpen] = useCycle(shouldStartOpen, true);
  const containerRef = useRef<HTMLDivElement>(null);
  const { height } = useDimensions(containerRef);

  // 页面加载后自动展开导航面板
  useEffect(() => {
    // 如果通过Preview按钮或Navigation导航而来，导航面板已经是打开的，所以直接返回
    if (location.state?.fromPreviewButton || location.state?.fromNavigation) {
      return;
    }
    
    const timer = setTimeout(() => {
      if (!isOpen) {
        toggleOpen();
      }
    }, 1500); // 延迟1500毫秒后自动展开

    return () => clearTimeout(timer);
  }, []); // 只在组件挂载时执行

  // 清理location.state，避免在刷新页面时仍然保留标志
  useEffect(() => {
    if (location.state?.fromPreviewButton || location.state?.fromNavigation) {
      // 清理state，避免刷新页面时还保留这些标志
      window.history.replaceState({}, document.title);
    }
  }, []);

  /**
   * Render the navigation panel based on the user roles
   */
  useEffect(() => {
    const updatedItems: SideNavigationProps.Item[] = [
      // Add Section 1 - General Terms with all subsections
      {
        type: "section",
        text: "Section 1 - General Terms",
        items: [
          {
            type: "link",
            text: "1.1 Parties to Agreement",
            href: "/sections#parties-to-agreement",
          },
          {
            type: "link",
            text: "1.2 Period of Agreement",
            href: "/sections#period-of-agreement",
          },
          {
            type: "link",
            text: "1.3 Scope of Agreement",
            href: "/sections#scope-of-agreement",
          },
          {
            type: "link",
            text: "1.4 Authority to Share Data",
            href: "/sections#authority-to-share-data",
          },
          {
            type: "link",
            text: "1.5 Modification",
            href: "/sections#modification",
          },
          {
            type: "link",
            text: "1.6 Termination",
            href: "/sections#termination",
          },
          {
            type: "link",
            text: "1.7 Violation of Terms",
            href: "/sections#violation-of-terms",
          },
          {
            type: "link",
            text: "1.8 Indemnification",
            href: "/sections#indemnification",
          },
          {
            type: "link",
            text: "1.9 Acknowledgements",
            href: "/sections#acknowledgements",
          },
          {
            type: "link",
            text: "1.10 Roles and Responsibilities",
            href: "/sections#roles-and-responsibilities",
          },
          {
            type: "link",
            text: "1.11 Funding",
            href: "/sections#funding",
          },
          {
            type: "link",
            text: "1.12 Others",
            href: "/sections#others",
          },
        ],
      },
      {
        type: "section",
        text: "Section 2 - Purpose/Use case",
        items: [
          {
            type: "link",
            text: "2.0 Purpose/Use case",
            href: "/sections#purpose-use-case",
          },
        ],
      },
      {
        type: "section",
        text: "Section 3 - Scope of Data",
        items: [
          {
            type: "link",
            text: "3.1 Data Description",
            href: "/sections#data-description",
          },
          {
            type: "link",
            text: "3.2 Data Ownership",
            href: "/sections#data-ownership",
          },
          {
            type: "link",
            text: "3.3 Service Level",
            href: "/sections#service-level",
          },
          {
            type: "link",
            text: "3.3.1 Data Quality",
            href: "/sections#data-quality",
          },
        ],
      },
      {
        type: "section",
        text: "Section 4 - Data Controls",
        items: [
          {
            type: "link",
            text: "4.1 Disclosure and Use",
            href: "/sections#disclosure-and-use",
          },
          {
            type: "link",
            text: "4.2 Control of Identifiable Data",
            href: "/sections#control-of-identifiable-data",
          },
          {
            type: "link",
            text: "4.3 Data Deidentification",
            href: "/sections#data-deidentification",
          },
          {
            type: "link",
            text: "4.3.1 Person",
            href: "/sections#person",
          },
          {
            type: "link",
            text: "4.3.2 Organization",
            href: "/sections#organization",
          },
          {
            type: "link",
            text: "4.4 Data Sharing",
            href: "/sections#data-sharing",
          },
          {
            type: "link",
            text: "4.4.1 Data Linkage",
            href: "/sections#data-linkage",
          },
          {
            type: "link",
            text: "4.4.2 Data Reuse",
            href: "/sections#data-reuse",
          },
          {
            type: "link",
            text: "4.5 Data Redisclosure",
            href: "/sections#data-redisclosure",
          },
          {
            type: "link",
            text: "4.6 Publication",
            href: "/sections#publication",
          },
        ],
      },
      {
        type: "section",
        text: "Section 5 - Security",
        items: [
          {
            type: "link",
            text: "5.1 General",
            href: "/sections#general",
          },
          {
            type: "link",
            text: "5.2 Data Access Controls",
            href: "/sections#data-access-controls",
          },
          {
            type: "link",
            text: "5.3 Network Access",
            href: "/sections#network-access",
          },
          {
            type: "link",
            text: "5.4 Physical Access",
            href: "/sections#physical-access",
          },
          {
            type: "link",
            text: "5.5 Transmission and Storage",
            href: "/sections#transmission-and-storage",
          },
          {
            type: "link",
            text: "5.7 Cloud Computing (where applicable)",
            href: "/sections#cloud-computing",
          },
          {
            type: "link",
            text: "5.8 Incidents Including Reporting",
            href: "/sections#incidents-including-reporting",
          },
          {
            type: "link",
            text: "5.9 Audit",
            href: "/sections#audit",
          },
        ],
      },
      {
        type: "section",
        text: "Section 6 - Reporting Requirements",
        items: [
          {
            type: "link",
            text: "6.0 Reporting Requirements",
            href: "/sections#reporting-requirements",
          },
        ],
      },
      {
        type: "section",
        text: "Section 7 - Applicable Laws and Regulations",
        items: [
          {
            type: "link",
            text: "7.0 Applicable Laws and Regulations",
            href: "/sections#applicable-laws-and-regulations",
          },
        ],
      },
      {
        type: "section",
        text: "Section 8 - Signatures",
        items: [
          {
            type: "link",
            text: "8.0 Signatures",
            href: "/sections#signatures",
          },
        ],
      },
    ];

    // Add the following sections based on user roles
    if (userRoles?.includes("OWNER")) {
      // Show Chatbot Section
      // updatedItems.push({
      //   type: "section",
      //   text: "Prompts",
      //   items: [
      //     { type: "link", text: "Prompts Hub", href: "/chatbot/prompts" },
      //   ],
      // });

      // Show Workspaces section
      // updatedItems.push({
      //   type: "section",
      //   text: "Retrieval-Augmented Generation (RAG)",
      //   items: [{ type: "link", text: "Workspaces", href: "/rag/workspaces" }],
      // });

      // Show Reports section
      // updatedItems.push({
      //   type: "section",
      //   text: "Reports",
      //   items: [
      //     {
      //       type: "link",
      //       text: "Export csv",
      //       href: "/reports/export_csv",
      //     },
      //   ],
      // });
      //show semantic search
      // updatedItems.push({
      //   type: "section",
      //   text: "Semantic Search",
      //   items: [{ type: "link", text: "Semantic Search", href: "/rag/semantic-search" }],
      // });

      updatedItems
        .push
        // { type: "link", text: "Prompts Hub", href: "/chatbot/prompts" },
        // { type: "link", text: "Export csv", href: "/reports/export_csv" },
        // { type: "link", text: "Semantic Search", href: "/rag/semantic-search" },
        // { type: "link", text: "Document Libraries", href: "/rag/workspaces" },
        // {
        //   type: 'divider'
        // }
        ();
    }

    if (userRoles?.includes("EDIT")) {
      updatedItems.push(
        { type: "link", text: "Document Libraries", href: "/rag/workspaces" },
        {
          type: "divider",
        }
      );
    }

    setItems(updatedItems);
  }, [appContext, userRoles]);

  /**
   *
   * @param param0
   */
  const onChange = ({
    detail,
  }: {
    detail: SideNavigationProps.ChangeDetail;
  }) => {
    const sectionIndex = items.indexOf(detail.item);
    setNavigationPanelState({
      collapsedSections: {
        ...navigationPanelState.collapsedSections,
        [sectionIndex]: !detail.expanded,
      },
    });
  };

  const handleNewChatClick = () => {
    setResetBgColor(true);
    setTimeout(() => setResetBgColor(false), 0);
    navigate("/", { state: { resetChat: true } });
  };

  // ① 添加 handlePreviewClick 函数
  const handlePreviewClick = () => {
    setResetBgColor(true);
    setTimeout(() => setResetBgColor(false), 0);

    // 如果导航面板是关闭的，先打开它
    if (!isOpen) {
      toggleOpen();
    }

    // Toggle between sections and preview based on current location
    if (location.pathname === "/preview") {
      navigate("/", { state: { resetChat: true, fromPreviewButton: true } });
    } else {
      navigate("/preview", { state: { resetChat: true, fromPreviewButton: true } });
    }
  };

  // ① 添加 mcp 导航函数
  const handleMCPClick = () => {
    navigate("/mcp");
  };

  // ① 自定义样式
  return (
    <motion.nav
      initial={false}
      animate={isOpen ? "open" : "closed"}
      custom={height}
      ref={containerRef}
      style={{
        position: "absolute",
        top: 0,
        left: 0,
        bottom: 0,
        width: "380px",
        zIndex: 1000
      }}
    >
      <motion.div 
        className="background" 
        variants={sidebar}
        style={{
          position: "absolute",
          top: 0,
          left: 0,
          bottom: 0,
          width: "380px",
          // 渐变色调整
          background: "linear-gradient(135deg, #f8fafc 0%, #e1f2ff 50%, #cce7ff 100%)",
          border: "1px solid rgba(0, 124, 186, 0.2)",
          boxShadow: "0 20px 40px rgba(0, 124, 186, 0.15), 0 10px 20px rgba(34, 44, 103, 0.08)"
        }}
      />
      
      <div style={{ 
        position: "relative", 
        zIndex: 10, 
        height: "100%", 
        overflowY: "auto",
        /*减少 DUABUILDER 和顶部的距离 和左右的距离*/
        padding: "10px 50px 20px 5px",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        width: "100%"
      }}>
        <NavigationMenu>
          <MenuSection>
            <div style={{ cursor: "grab", width: "100%", display: "flex", justifyContent: "center" }} onClick={handleNewChatClick}>
              <div style={{ 
                display: "flex", 
                marginTop: "-10px", 
                width: "240px", 
                height: "50px", 
                padding: "10px 24px", 
                gap: "5px", 
                justifyContent: "center",
                alignItems: "center"
              }}>
                <span style={{ color: "#222C67", fontFamily: "Roboto Condensed", fontWeight: "700", fontSize: "29px", lineHeight: "24px" }}>
                  DUA<span style={{ color: "#007CBA" }}>BUILDER</span>
                </span>
              </div>
            </div>
          </MenuSection>

          <MenuSection>
            {/* Preview 按钮和圆形进度条容器 */}
            <div style={{ 
              marginTop: "-15px", 
              marginBottom: "-20px",
              display: "flex", 
              alignItems: "center", 
              width: "100%",
              justifyContent: "center"
            }}>
              <motion.div
                style={{ display: "flex", justifyContent: "space-between", alignItems: "center", 
                  width: "260px",
                  height: "50px", 
                  borderRadius: "15px", 
                  padding: "13px", 
                  gap: "8px", 
                  backgroundColor: "#CCE5F1", 
                  cursor: "cell", 
                  boxSizing: "border-box",
                  flexShrink: 0, 
                  position: "relative", 
                  border: "3px solid transparent",
                  backgroundImage:
                    "linear-gradient(#CCE5F1, #CCE5F1), conic-gradient(from 0deg, #007CBA, #222C67, #00cc88, #ff6b6b, #4ecdc4, #45b7d1, #007CBA)",
                  backgroundOrigin: "border-box",
                  backgroundClip: "padding-box, border-box",
                }}
                animate={{
                  backgroundImage: [
                    "linear-gradient(#CCE5F1, #CCE5F1), conic-gradient(from 0deg, #007CBA, #222C67, #00cc88, #ff6b6b, #4ecdc4, #45b7d1, #007CBA)",
                    "linear-gradient(#CCE5F1, #CCE5F1), conic-gradient(from 60deg, #007CBA, #222C67, #00cc88, #ff6b6b, #4ecdc4, #45b7d1, #007CBA)",
                    "linear-gradient(#CCE5F1, #CCE5F1), conic-gradient(from 120deg, #007CBA, #222C67, #00cc88, #ff6b6b, #4ecdc4, #45b7d1, #007CBA)",
                    "linear-gradient(#CCE5F1, #CCE5F1), conic-gradient(from 180deg, #007CBA, #222C67, #00cc88, #ff6b6b, #4ecdc4, #45b7d1, #007CBA)",
                    "linear-gradient(#CCE5F1, #CCE5F1), conic-gradient(from 240deg, #007CBA, #222C67, #00cc88, #ff6b6b, #4ecdc4, #45b7d1, #007CBA)",
                    "linear-gradient(#CCE5F1, #CCE5F1), conic-gradient(from 300deg, #007CBA, #222C67, #00cc88, #ff6b6b, #4ecdc4, #45b7d1, #007CBA)",
                    "linear-gradient(#CCE5F1, #CCE5F1), conic-gradient(from 360deg, #007CBA, #222C67, #00cc88, #ff6b6b, #4ecdc4, #45b7d1, #007CBA)",
                  ],
                }}
                whileHover={{ scale: 1.05, boxShadow: "0 8px 25px rgba(0, 124, 186, 0.3)" }}
                whileTap={{ scale: 0.98 }}
                transition={{ duration: 4, repeat: Infinity, ease: "linear", scale: { type: "spring", stiffness: 300, damping: 20 }, boxShadow: { duration: 0.3 } }}
                onClick={handlePreviewClick}
              >
                <div style={{ display: "flex", alignItems: "center", gap: "5px" }}>
                  <span style={{ fontFamily: "Roboto", fontWeight: "600", fontSize: "16px", lineHeight: "16px", whiteSpace: "nowrap" }}>
                    Preview
                  </span>
                  <img src={preview} style={{ width: "20px", height: "20px", flexShrink: 0 }} alt="new chat icon" />
                </div>
                {/* 圆形进度条 */}
                <CircularProgress percentage={59} size={39} />
              </motion.div>
            </div>
          </MenuSection>

          <MenuSection>
            {/* Section1 和 Preview 按钮的间距 */}
            <div style={{ 
              marginTop: "5px", 
              paddingLeft: "10px", 
              paddingRight: "10px", 
              width: "calc(100% - 20px)",
              display: "flex",
              justifyContent: "center"
            }}>
              <div style={{ width: "100%", maxWidth: "320px" }}>
                <SideNavigation
                  onFollow={(event) => {
                    if (!event.detail.external) {
                      event.preventDefault();
                      setActiveHref(event.detail.href);
                      
                      // 如果导航面板是关闭的，先打开它
                      if (!isOpen) {
                        toggleOpen();
                      }
                      
                      // 使用自定义的导航，添加 fromNavigation 标志
                      if (event.detail.href) {
                        navigate(event.detail.href, { state: { fromNavigation: true } });
                      }
                    }
                  }}
                  onChange={onChange}
                  items={items.map((value, idx) => {
                    if (value.type === "section") {
                      const collapsed =
                        navigationPanelState.collapsedSections?.[idx] === true;
                      value.defaultExpanded = !collapsed;
                    }
                    return value;
                  })}
                />
              </div>
            </div>
          </MenuSection>
        </NavigationMenu>
      </div>
      
      <MenuToggle toggle={() => toggleOpen()} />
    </motion.nav>
  );
}
