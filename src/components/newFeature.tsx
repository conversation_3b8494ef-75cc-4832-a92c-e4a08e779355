import React, { useState, useEffect } from 'react';
import '@/styles/newFeature.css';
import { UPDATES_CONFIG, processUpdatesConfig } from '../configs/newFeature/updateConfig';
import { 
  getReadStatus, 
  markAsRead as markAsReadInStorage, 
  markAllAsRead as markAllAsReadInStorage
} from '../stores/newFeature/updateStorage';

export interface UpdateItem {
  id: string;
  date: string;
  title: string;
  description: string;
  isRead?: boolean;
  type?: 'feature' | 'bugfix' | 'maintenance';
}

interface NewFeatureProps {
  isVisible: boolean;
  onClose: () => void;
  onMarkAsRead: (id: string) => void;
  onMarkAllAsRead: () => void;
}

const NewFeature: React.FC<NewFeatureProps> = ({ 
  isVisible, 
  onClose, 
  onMarkAsRead, 
  onMarkAllAsRead 
}) => {
  const [updates, setUpdates] = useState<UpdateItem[]>([]);

  // 初始化更新数据
  useEffect(() => {
    const processedUpdates = processUpdatesConfig(UPDATES_CONFIG);
    const readStatus = getReadStatus();
    
    // 设置已读状态
    const updatesWithReadStatus = processedUpdates.map(update => ({
      ...update,
      isRead: readStatus[update.id] || false
    }));

    setUpdates(updatesWithReadStatus);
  }, []);

  const handleMarkAsRead = (id: string) => {
    // 更新本地状态
    setUpdates(prev => 
      prev.map(update => 
        update.id === id ? { ...update, isRead: true } : update
      )
    );
    
    // 持久化到本地存储
    markAsReadInStorage(id);
    
    // 调用父组件回调
    onMarkAsRead(id);
  };

  const handleMarkAllAsRead = () => {
    const updateIds = updates.map(update => update.id);
    
    // 更新本地状态
    setUpdates(prev => 
      prev.map(update => ({ ...update, isRead: true }))
    );
    
    // 持久化到本地存储
    markAllAsReadInStorage(updateIds);
    
    // 调用父组件回调
    onMarkAllAsRead();
  };

  const unreadCount = updates.filter(update => !update.isRead).length;

  const getTypeIcon = (type: UpdateItem['type']) => {
    switch (type) {
      case 'feature': return '🚀';
      case 'bugfix': return '🔧';
      case 'maintenance': return '⚙️';
      default: return '📝';
    }
  };

  const getTypeLabel = (type: UpdateItem['type']) => {
    switch (type) {
      case 'feature': return 'Feature';
      case 'bugfix': return 'Bug Fix';
      case 'maintenance': return 'Maintenance';
      default: return 'Update';
    }
  };

  if (!isVisible) return null;

  return (
    <div className="new-feature-overlay">
      <div className="new-feature-panel">
        <div className="new-feature-header">
          <div className="header-left">
            <h3>Latest Updates</h3>
            {unreadCount > 0 && (
              <span className="unread-badge">{unreadCount} unread</span>
            )}
          </div>
          <div className="header-actions">
            {unreadCount > 0 && (
              <button 
                className="mark-all-read-btn"
                onClick={handleMarkAllAsRead}
              >
                Mark all as read
              </button>
            )}
            <button className="close-btn" onClick={onClose}>
              ✕
            </button>
          </div>
        </div>

        <div className="new-feature-content">
          {updates.length === 0 ? (
            <div className="no-updates">
              <p>No updates available</p>
            </div>
          ) : (
            updates.map((update) => (
              <div 
                key={update.id} 
                className={`update-item ${!update.isRead ? 'unread' : 'read'}`}
              >
                <div className="update-header">
                  <div className="update-meta">
                    <span className="update-type">
                      {getTypeIcon(update.type)} {getTypeLabel(update.type)}
                    </span>
                    <span className="update-date">{update.date}</span>
                  </div>
                  {!update.isRead && (
                    <button 
                      className="mark-read-btn"
                      onClick={() => handleMarkAsRead(update.id)}
                      title="Mark as read"
                    >
                      ✓
                    </button>
                  )}
                </div>
                
                <h4 className="update-title">{update.title}</h4>
                
                <div 
                  className="update-description"
                  dangerouslySetInnerHTML={{ __html: update.description }}
                />
                
                {!update.isRead && <div className="unread-indicator"></div>}
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default NewFeature; 