import React, { useRef, useState } from 'react';
import { <PERSON><PERSON>, Alert, SpaceBetween } from "@cloudscape-design/components";
import styles from '../styles/chat.module.scss'

const PDFUploadButton = ({ onFilesSelect, currentFiles, onClear, disabled }) => {
  const [error, setError] = useState('');
  const fileInputRef = useRef(null);

  const handleClick = () => {
    fileInputRef.current.click();
  };

  const handleFileChange = (event) => {
    const files = Array.from(event.target.files);

    if (files.length > 0) {
      setError('');
      onFilesSelect(files);
    }
    
    // Reset the input so the same files can be selected again
    event.target.value = '';
  };

  return (
    <>
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileChange}
        accept=".pdf,.docx,application/pdf,application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        style={{ display: 'none' }}
        multiple
      />
      <button
        onClick={handleClick}
        className={styles.send_input_btn}
        style={{marginRight: "5px"}}
        title="Attach .pdf or .docx documents (up to 640,000 characters total)"  
        formAction="none"
        disabled={disabled}
        >Upload Documents
        </button>
      {currentFiles && currentFiles.length > 0 && (
        <Button
          iconName="close"
          variant="icon"
          onClick={onClear}
        />
      )}
      
      {/* {error && (
        <Alert
          type="error"
          dismissible={true}
          onDismiss={() => setError('')}
        >
          {error}
        </Alert>
      )} */}
    </>
  );
};

export default PDFUploadButton;