import React, { useState } from 'react';
import {
  Container,
  FormField,
  Checkbox,
  SpaceBetween,
} from '@cloudscape-design/components';

interface AcknowledgementsProps {
  // Props can be added here for data management when logic is implemented
}

const Acknowledgements1_9: React.FC<AcknowledgementsProps> = () => {
  // State for the three checkboxes
  const [authorizationToSign, setAuthorizationToSign] = useState<boolean>(false);
  const [understandingAndAttest, setUnderstandingAndAttest] = useState<boolean>(false);
  const [dataPlansAccurate, setDataPlansAccurate] = useState<boolean>(false);

  return (
    <Container>
      <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
        <div>
          <span style={{ fontSize: '15px', fontWeight: '600', lineHeight: '1.1', margin: '0' }}>Section 1.9 - Acknowledgements</span>
        </div>
        
        <div style={{ marginTop: '16px' }}>
          <SpaceBetween direction="vertical" size="m">
            <FormField>
              <Checkbox
                onChange={({ detail }) => setAuthorizationToSign(detail.checked)}
                checked={authorizationToSign}
              >
                <span style={{fontWeight: '500', color: '#0073bb' }}>
                  I affirm that I have the authorization to sign this DUA.
                </span>
              </Checkbox>
            </FormField>

            <FormField>
              <Checkbox
                onChange={({ detail }) => setUnderstandingAndAttest(detail.checked)}
                checked={understandingAndAttest}
              >
                <span style={{fontWeight: '500', color: '#0073bb' }}>
                  I affirm understanding of this agreement and attest to abide by its terms.
                </span>
              </Checkbox>
            </FormField>

            <FormField>
              <Checkbox
                onChange={({ detail }) => setDataPlansAccurate(detail.checked)}
                checked={dataPlansAccurate}
              >
                <span style={{fontWeight: '500', color: '#0073bb' }}>
                  I confirm that the plans for the use of this data are accurate.
                </span>
              </Checkbox>
            </FormField>
          </SpaceBetween>
        </div>
      </div>
    </Container>
  );
};

export default Acknowledgements1_9; 