import React, { useState } from "react";
import {
  Container,
  Box,
  FormField,
  RadioGroup,
  Textarea,
} from "@cloudscape-design/components";

interface AuthorityToShareDataProps {
  // Props can be added here for data management when logic is implemented
}

const AuthorityToShareData1_4: React.FC<AuthorityToShareDataProps> = () => {
  // State for the radio button selections
  const [internalSharing, setInternalSharing] = useState<string>("");
  const [externalSharing, setExternalSharing] = useState<string>("");
  const [legislationReference, setLegislationReference] = useState<string>("");

  // Function to render conditional text based on internal sharing selection
  const renderInternalSharingMessage = () => {
    if (internalSharing === "yes") {
      return (
        <Box margin={{ top: "xs" }}>
          <span
            style={{ fontSize: "13px", color: "#0073bb", fontStyle: "italic" }}
          >
            Internal sharing must be in compliance with published FDA-Wide
            Internal Sharing Policy SMG
          </span>
        </Box>
      );
    } else if (internalSharing === "no") {
      return (
        <Box margin={{ top: "xs" }}>
          <span
            style={{ fontSize: "13px", color: "#5f6b7a", fontStyle: "italic" }}
          >
            Not Applicable
          </span>
        </Box>
      );
    }
    return null;
  };

  // Function to render conditional text based on external sharing selection
  const renderExternalSharingMessage = () => {
    if (externalSharing === "yes") {
      return (
        // <Box margin={{ top: 'xs' }}>
        //   <div style={{ marginBottom: '12px' }}>
        //     <span style={{ fontSize: '1px', fontWeight: '500', color: '#0073bb' }}>
        //       Please make reference to legislation and/or regulation that authorize the data provider to share data with the data recipient,
        //       as applicable. If your program does not have an explicit authority in statute to share data, please state that you your program has a
        //       presumed authority to share data subject to the broad privacy, confidentiality, and security requirements and standards common
        //       to all Federal data, unless further restricted in statute or regulation.
        //     </span>
        //   </div>
        //   <FormField>
        //     <div style={{ marginTop: '8px', width: '151%' }}>
        //     <Textarea
        //       value={legislationReference}
        //       onChange={(event) => setLegislationReference(event.detail.value)}
        //       placeholder="Start typing here..."
        //       rows={20}
        //     />
        //     </div>
        //   </FormField>
        // </Box>
        <div style={{ marginTop: "16px", marginBottom: "-30px", marginLeft: "24px" }}>
          <div style={{ marginBottom: "8px", width: "98%" }}>
            <span
              style={{ fontSize: "14px", fontWeight: "500", color: "#0073bb" }}
            >
              Please make reference to legislation and/or regulation that
              authorize the data provider to share data with the data recipient,
              as applicable. If your program does not have an explicit authority
              in statute to share data, please state that you your program has a
              presumed authority to share data subject to the broad privacy,
              confidentiality, and security requirements and standards common to
              all Federal data, unless further restricted in statute or
              regulation.
            </span>
          </div>
          <FormField>
            <div style={{ marginTop: "8px", width: "148%" }}>
              <Textarea
                value={legislationReference}
                onChange={(event) =>
                  setLegislationReference(event.detail.value)
                }
                placeholder="Start typing here"
                rows={20}
              />
            </div>
          </FormField>
        </div>
      );
    } else if (externalSharing === "no") {
      return (
        <Box margin={{ top: "xs" }}>
          <span
            style={{ fontSize: "13px", color: "#5f6b7a", fontStyle: "italic" }}
          >
            Not Applicable
          </span>
        </Box>
      );
    }
    return null;
  };

  return (
    <Container>
      <div style={{ display: "flex", flexDirection: "column", gap: "4px" }}>
        <div>
          <span
            style={{
              fontSize: "15px",
              fontWeight: "600",
              lineHeight: "1.1",
              margin: "0",
            }}
          >
            Section 1.4 - Authority to Operate (as applicable)
          </span>
        </div>

        <div style={{ marginTop: "16px" }}>
          {/* First Question */}
          <div style={{ marginBottom: "24px" }}>
            <Box>
              <span
                style={{
                  fontSize: "14px",
                  fontWeight: "500",
                  lineHeight: "1.1",
                  color: "#0073bb",
                }}
              >
                Is this data being shared only internally within the FDA?
              </span>
            </Box>

            <div style={{ marginTop: "12px" }}>
              <FormField>
                <RadioGroup
                  value={internalSharing}
                  onChange={({ detail }) => setInternalSharing(detail.value)}
                  items={[
                    {
                      value: "yes",
                      label: <span style={{ fontSize: "14px" }}>Yes</span>,
                    },
                    {
                      value: "no",
                      label: <span style={{ fontSize: "14px" }}>No</span>,
                    },
                  ]}
                />
              </FormField>

              {/* Conditional message based on selection */}
              {renderInternalSharingMessage()}
            </div>
          </div>

          {/* Second Question */}
          <div style={{ marginBottom: "24px" }}>
            <Box>
              <span
                style={{
                  fontSize: "14px",
                  fontWeight: "500",
                  lineHeight: "1.1",
                  color: "#0073bb",
                }}
              >
                Is this data also being shared with external partners?
              </span>
            </Box>

            <div style={{ marginTop: "12px", marginBottom: "-33px" }}>
              <FormField>
                <RadioGroup
                  value={externalSharing}
                  onChange={({ detail }) => setExternalSharing(detail.value)}
                  items={[
                    {
                      value: "yes",
                      label: <span style={{ fontSize: "14px" }}>Yes</span>,
                    },
                    {
                      value: "no",
                      label: <span style={{ fontSize: "14px" }}>No</span>,
                    },
                  ]}
                />
              </FormField>

              {/* Conditional message based on selection */}
              {renderExternalSharingMessage()}
            </div>
          </div>
        </div>
      </div>
    </Container>
  );
};

export default AuthorityToShareData1_4;
