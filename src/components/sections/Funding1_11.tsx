import React, { useState } from 'react';
import {
  Con<PERSON>er,
  FormField,
  Input,
  SpaceBetween,
} from '@cloudscape-design/components';

interface FundingProps {
  // Props can be added here for data management when logic is implemented
}

const Funding1_11: React.FC<FundingProps> = () => {
  // State for labor hours
  const [laborHours, setLaborHours] = useState<string>('');
  const [laborHoursError, setLaborHoursError] = useState<string>('');

  // State for funding amount
  const [fundingAmount, setFundingAmount] = useState<string>('');
  const [fundingAmountError, setFundingAmountError] = useState<string>('');

  // Function to handle labor hours input
  const handleLaborHoursChange = (value: string) => {
    // Allow empty string
    if (value === '') {
      setLaborHours('');
      setLaborHoursError('');
      return;
    }

    // Check if input is a valid positive number
    const numericValue = parseFloat(value);
    
    // Check if it's a valid number
    if (isNaN(numericValue)) {
      setLaborHoursError('Please enter a valid number');
      setLaborHours(value);
      return;
    }

    // Check if it's negative
    if (numericValue < 0) {
      setLaborHoursError('Please enter a positive number');
      setLaborHours(value);
      return;
    }

    // Valid input
    setLaborHours(value);
    setLaborHoursError('');
  };

  // Function to handle funding amount input
  const handleFundingAmountChange = (value: string) => {
    // Allow empty string
    if (value === '') {
      setFundingAmount('');
      setFundingAmountError('');
      return;
    }

    // Check if input is a valid positive number
    const numericValue = parseFloat(value);
    
    // Check if it's a valid number
    if (isNaN(numericValue)) {
      setFundingAmountError('Please enter a valid number');
      setFundingAmount(value);
      return;
    }

    // Check if it's negative
    if (numericValue < 0) {
      setFundingAmountError('Please enter a positive number');
      setFundingAmount(value);
      return;
    }

    // Check decimal places (max 3)
    const decimalPart = value.split('.')[1];
    if (decimalPart && decimalPart.length > 3) {
      setFundingAmountError('Maximum 3 decimal places allowed');
      setFundingAmount(value);
      return;
    }

    // Valid input
    setFundingAmount(value);
    setFundingAmountError('');
  };

  return (
    <Container>
      <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
        <div>
          <span style={{ fontSize: '15px', fontWeight: '600', lineHeight: '1.1', margin: '0' }}>Section 1.11 - Funding</span>
        </div>
        
        <div style={{ marginTop: '16px' }}>
          <div style={{ marginBottom: '16px' }}>
            <span style={{ fontSize: '14px', color: '#0073bb', fontWeight: '500' }}>
              Please choose at least one of the below to fill in:
            </span>
          </div>
          
          <SpaceBetween direction="vertical" size="l">
            <FormField
              label={
                <span style={{ fontSize: '14px', color: '#000000', fontWeight: '400' }}>
                  Enter the number of labor hours involved:
                </span>
              }
              errorText={laborHoursError}
            >
              <div style={{ maxWidth: '300px' }}>
                <Input
                  value={laborHours}
                  onChange={(event) => handleLaborHoursChange(event.detail.value)}
                  placeholder=""
                  invalid={!!laborHoursError}
                  type="text"
                />
              </div>
            </FormField>

            <FormField
              label={
                <span style={{ fontSize: '14px', color: '#000000', fontWeight: '400' }}>
                  Enter the amount of funding involved:
                </span>
              }
              errorText={fundingAmountError}
            >
              <div style={{ maxWidth: '300px' }}>
                <Input
                  value={fundingAmount}
                  onChange={(event) => handleFundingAmountChange(event.detail.value)}
                  placeholder=""
                  invalid={!!fundingAmountError}
                  type="text"
                />
              </div>
            </FormField>
          </SpaceBetween>
        </div>
      </div>
    </Container>
  );
};

export default Funding1_11; 