import React, { useState } from 'react';
import {
  Container,
  Box,
  FormField,
  RadioGroup,
  SpaceBetween,
  Textarea,
} from '@cloudscape-design/components';

interface ModificationProps {
  // Props can be added here for data management when logic is implemented
}

const Modification1_5: React.FC<ModificationProps> = () => {
  // State for the first question
  const [requiresModification, setRequiresModification] = useState<string>('');
  
  // State for the second question
  const [dataModified, setDataModified] = useState<string>('');

  // State for the additional text when "Yes" is selected
  const [modificationDetails, setModificationDetails] = useState<string>('');

  // State for the additional text when second question "Yes" is selected
  const [dataUsageDetails, setDataUsageDetails] = useState<string>('');

  return (
    <Container>
      <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
        <div>
          <span style={{ fontSize: '15px', fontWeight: '600', lineHeight: '1.1', margin: '0' }}>Section 1.5 - Modification</span>
        </div>
        
        <div style={{ marginTop: '16px' }}>
          <SpaceBetween direction="vertical" size="l">
            {/* First Question */}
            <FormField>
              <div style={{ marginBottom: '8px' }}>
                <span style={{ fontSize: '14px', color: '#0073bb', fontWeight: '500' }}>
                  Is there any type of changes that require modification to the existing agreement?
                </span>
              </div>
              <RadioGroup
                onChange={({ detail }) => setRequiresModification(detail.value)}
                value={requiresModification}
                items={[
                  { value: "yes", label: "Yes" },
                  { value: "no", label: "No" }
                ]}
              />

              {/* Conditional content based on first question */}
              {requiresModification === 'yes' && (
                <div style={{ marginTop: '16px', marginLeft: '24px' }}>
                  <div style={{ marginBottom: '8px', width: '227%' }}>
                    <span style={{ fontSize: '14px', color: '#0073bb', fontWeight: '500' }}>
                      What are the types of changes that require modification of the agreement (e.g., changes to the regulatory authorities, research plans or computing environments)?
                    </span>
                  </div>
                  <FormField>
                    <div style={{ marginTop: '8px', width: '227%' }}>
                    <Textarea
                      value={modificationDetails}
                      onChange={(event) => setModificationDetails(event.detail.value)}
                      placeholder="Start typing here"
                      rows={20}
                    />
                    </div>
                  </FormField>
                </div>
              )}

              {requiresModification === 'no' && (
                <div style={{ marginTop: '12px', marginLeft: '24px' }}>
                  <span style={{ fontSize: '14px', color: '#5f6b7a', fontStyle: 'italic' }}>
                    Not Applicable
                  </span>
                </div>
              )}
            </FormField>

            {/* Second Question */}
            <FormField>
              <div style={{ marginBottom: '8px' }}>
                <span style={{ fontSize: '14px', color: '#0073bb', fontWeight: '500' }}>
                  Will the data be modified?
                </span>
              </div>
              <RadioGroup
                onChange={({ detail }) => setDataModified(detail.value)}
                value={dataModified}
                items={[
                  { value: "yes", label: "Yes" },
                  { value: "no", label: "No" }
                ]}
              />

              {/* Conditional content based on second question */}
              {dataModified === 'yes' && (
                <div style={{ marginTop: '16px', marginLeft: '24px' }}>
                  <div style={{ marginBottom: '8px' }}>
                    <span style={{ fontSize: '14px', color: '#0073bb', fontWeight: '500' }}>
                      What will the modified data be used for?
                    </span>
                  </div>
                  <FormField>
                    <div style={{ marginTop: '8px', width: '227%' }}>
                    <Textarea
                      value={dataUsageDetails}
                      onChange={(event) => setDataUsageDetails(event.detail.value)}
                      placeholder="Start typing here"
                      rows={20}
                    />
                    </div>
                  </FormField>
                </div>
              )}

              {dataModified === 'no' && (
                <div style={{ marginTop: '12px', marginLeft: '24px' }}>
                  <span style={{ fontSize: '14px', color: '#5f6b7a', fontStyle: 'italic' }}>
                    Not Applicable
                  </span>
                </div>
              )}
            </FormField>
          </SpaceBetween>
        </div>
      </div>
    </Container>
  );
};

export default Modification1_5; 