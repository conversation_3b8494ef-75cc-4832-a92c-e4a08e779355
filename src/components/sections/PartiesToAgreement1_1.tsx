import React, { useState } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  SpaceBetween,
  Table,
  Button,
  Input,
  Box,
  ColumnLayout,
  Icon,
} from '@cloudscape-design/components';
import { Utils } from '../../common/utils';
import { DeleteIcon } from './utils';

interface ContactInfo {
  id: string;
  name: string;
  organization: string;
  roleTitle: string;
  email: string;
}

interface PartiesToAgreementProps {
  // Props can be added here for data management when logic is implemented
}

const PartiesToAgreement1_1: React.FC<PartiesToAgreementProps> = () => {
  // State for managing the data
  const [dataProviders, setDataProviders] = useState<ContactInfo[]>(() => [
    { id: Utils.generateId('dp'), name: '', organization: '', roleTitle: '', email: '' }
  ]);

  const [dataRecipients, setDataRecipients] = useState<ContactInfo[]>(() => [
    { id: Utils.generateId('dr'), name: '', organization: '', roleTitle: '', email: '' }
  ]);

  const [duaContacts, setDuaContacts] = useState<ContactInfo[]>(() => [
    { id: Utils.generateId('dua'), name: '', organization: '', roleTitle: '', email: '' }
  ]);

  // Function to add new row
  const addNewRow = (type: 'dataProvider' | 'dataRecipient' | 'duaContact') => {
    const template = { name: '', organization: '', roleTitle: '', email: '' };
    const idPrefix = type === 'dataProvider' ? 'dp' : type === 'dataRecipient' ? 'dr' : 'dua';

    if (type === 'dataProvider') {
      Utils.addNewItem(dataProviders, setDataProviders, template, idPrefix);
    } else if (type === 'dataRecipient') {
      Utils.addNewItem(dataRecipients, setDataRecipients, template, idPrefix);
    } else {
      Utils.addNewItem(duaContacts, setDuaContacts, template, idPrefix);
    }
  };

  // Function to delete row
  const deleteRow = (type: 'dataProvider' | 'dataRecipient' | 'duaContact', itemId: string) => {
    if (type === 'dataProvider') {
      Utils.deleteItem(dataProviders, setDataProviders, itemId);
    } else if (type === 'dataRecipient') {
      Utils.deleteItem(dataRecipients, setDataRecipients, itemId);
    } else {
      Utils.deleteItem(duaContacts, setDuaContacts, itemId);
    }
  };

  // Function to update item data
  const updateItem = (items: ContactInfo[], setItems: React.Dispatch<React.SetStateAction<ContactInfo[]>>, itemId: string, field: keyof ContactInfo, value: string) => {
    Utils.updateItem(items, setItems, itemId, field, value);
  };

  // Create column definitions for Data Providers
  const dataProvidersColumnDefinitions = [
    {
      id: 'name',
      header: (
        <div style={{ textAlign: 'center', fontSize: '14px', fontWeight: '600' }}>
          Name
        </div>
      ),
      cell: (item: ContactInfo) => (
        <Input
          value={item.name}
          placeholder=""
          onChange={(event) => updateItem(dataProviders, setDataProviders, item.id, 'name', event.detail.value)}
        />
      ),
      width: 200,
    },
    {
      id: 'organization',
      header: (
        <div style={{ textAlign: 'center', fontSize: '14px', fontWeight: '600' }}>
          Organizations
        </div>
      ),
      cell: (item: ContactInfo) => (
        <Input
          value={item.organization}
          placeholder=""
          onChange={(event) => updateItem(dataProviders, setDataProviders, item.id, 'organization', event.detail.value)}
        />
      ),
      width: 200,
    },
    {
      id: 'roleTitle',
      header: (
        <div style={{ textAlign: 'center', fontSize: '14px', fontWeight: '600' }}>
          Role / Title
        </div>
      ),
      cell: (item: ContactInfo) => (
        <Input
          value={item.roleTitle}
          placeholder=""
          onChange={(event) => updateItem(dataProviders, setDataProviders, item.id, 'roleTitle', event.detail.value)}
        />
      ),
      width: 200,
    },
    {
      id: 'email',
      header: (
        <div style={{ textAlign: 'center', fontSize: '14px', fontWeight: '600' }}>
          Email
        </div>
      ),
      cell: (item: ContactInfo) => (
        <Input
          value={item.email}
          placeholder=""
          onChange={(event) => updateItem(dataProviders, setDataProviders, item.id, 'email', event.detail.value)}
        />
      ),
      width: 200,
    },
    {
      id: 'actions',
      header: (
        <div style={{ textAlign: 'center', fontSize: '14px', fontWeight: '600' }}>
          Actions
        </div>
      ),
      cell: (item: ContactInfo) => (
        <div style={{ display: 'flex', justifyContent: 'center' }}>
          {dataProviders.length > 1 && (
            <Button
              variant="icon"
              // iconName="remove"
              iconSvg={DeleteIcon}
              onClick={() => deleteRow('dataProvider', item.id)}
              ariaLabel="Delete row"
            />
          )}
        </div>
      ),
      width: 100,
    },
  ];

  // Create column definitions for Data Recipients
  const dataRecipientsColumnDefinitions = [
    {
      id: 'name',
      header: (
        <div style={{ textAlign: 'center', fontSize: '14px', fontWeight: '600' }}>
          Name
        </div>
      ),
      cell: (item: ContactInfo) => (
        <Input
          value={item.name}
          placeholder=""
          onChange={(event) => updateItem(dataRecipients, setDataRecipients, item.id, 'name', event.detail.value)}
        />
      ),
      width: 200,
    },
    {
      id: 'organization',
      header: (
        <div style={{ textAlign: 'center', fontSize: '14px', fontWeight: '600' }}>
          Organizations
        </div>
      ),
      cell: (item: ContactInfo) => (
        <Input
          value={item.organization}
          placeholder=""
          onChange={(event) => updateItem(dataRecipients, setDataRecipients, item.id, 'organization', event.detail.value)}
        />
      ),
      width: 200,
    },
    {
      id: 'roleTitle',
      header: (
        <div style={{ textAlign: 'center', fontSize: '14px', fontWeight: '600' }}>
          Role / Title
        </div>
      ),
      cell: (item: ContactInfo) => (
        <Input
          value={item.roleTitle}
          placeholder=""
          onChange={(event) => updateItem(dataRecipients, setDataRecipients, item.id, 'roleTitle', event.detail.value)}
        />
      ),
      width: 200,
    },
    {
      id: 'email',
      header: (
        <div style={{ textAlign: 'center', fontSize: '14px', fontWeight: '600' }}>
          Email
        </div>
      ),
      cell: (item: ContactInfo) => (
        <Input
          value={item.email}
          placeholder=""
          onChange={(event) => updateItem(dataRecipients, setDataRecipients, item.id, 'email', event.detail.value)}
        />
      ),
      width: 200,
    },
    {
      id: 'actions',
      header: (
        <div style={{ textAlign: 'center', fontSize: '14px', fontWeight: '600' }}>
          Actions
        </div>
      ),
      cell: (item: ContactInfo) => (
        <div style={{ display: 'flex', justifyContent: 'center' }}>
          {dataRecipients.length > 1 && (
            <Button
              variant="icon"
              // iconName="remove"
              iconSvg={DeleteIcon}
              onClick={() => deleteRow('dataRecipient', item.id)}
              ariaLabel="Delete row"
            />
          )}
        </div>
      ),
      width: 100,
    },
  ];

  // Create column definitions for DUA Contacts
  const duaContactsColumnDefinitions = [
    {
      id: 'name',
      header: (
        <div style={{ textAlign: 'center', fontSize: '14px', fontWeight: '600' }}>
          Name
        </div>
      ),
      cell: (item: ContactInfo) => (
        <Input
          value={item.name}
          placeholder=""
          onChange={(event) => updateItem(duaContacts, setDuaContacts, item.id, 'name', event.detail.value)}
        />
      ),
      width: 200,
    },
    {
      id: 'organization',
      header: (
        <div style={{ textAlign: 'center', fontSize: '14px', fontWeight: '600' }}>
          Organizations
        </div>
      ),
      cell: (item: ContactInfo) => (
        <Input
          value={item.organization}
          placeholder=""
          onChange={(event) => updateItem(duaContacts, setDuaContacts, item.id, 'organization', event.detail.value)}
        />
      ),
      width: 200,
    },
    {
      id: 'roleTitle',
      header: (
        <div style={{ textAlign: 'center', fontSize: '14px', fontWeight: '600' }}>
          Role / Title
        </div>
      ),
      cell: (item: ContactInfo) => (
        <Input
          value={item.roleTitle}
          placeholder=""
          onChange={(event) => updateItem(duaContacts, setDuaContacts, item.id, 'roleTitle', event.detail.value)}
        />
      ),
      width: 200,
    },
    {
      id: 'email',
      header: (
        <div style={{ textAlign: 'center', fontSize: '14px', fontWeight: '600' }}>
          Email
        </div>
      ),
      cell: (item: ContactInfo) => (
        <Input
          value={item.email}
          placeholder=""
          onChange={(event) => updateItem(duaContacts, setDuaContacts, item.id, 'email', event.detail.value)}
        />
      ),
      width: 200,
    },
    {
      id: 'actions',
      header: (
        <div style={{ textAlign: 'center', fontSize: '14px', fontWeight: '600' }}>
          Actions
        </div>
      ),
      cell: (item: ContactInfo) => (
        <div style={{ display: 'flex', justifyContent: 'center' }}>
          {duaContacts.length > 1 && (
            <Button
              variant="icon"
              // iconName="remove"
              iconSvg={DeleteIcon}
              onClick={() => deleteRow('duaContact', item.id)}
              ariaLabel="Delete row"
            />
          )}
        </div>
      ),
      width: 100,
    },
  ];

  return (
    <Container>
      <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
        <div>
          <span style={{ fontSize: '15px', fontWeight: '600', lineHeight: '1.1', margin: '0' }}>Section 1 - General Terms</span>
        </div>
        
        <div>
          <span style={{ fontSize: '15px', fontWeight: '600', lineHeight: '1.1', margin: '0' }}>Section 1.1 - Parties to Agreement</span>
        </div>
        
        <div style={{ marginTop: '4px' }}>
          <Box>
            <Box color="text-status-info" fontSize="body-s">
              <span style={{ fontSize: '13px', fontWeight: '500', lineHeight: '1.1', color: '#0073bb' }}>
                Please provide details for data providers, data recipients, and contacts information to complete Section 1.1
              </span>
            </Box>
          </Box>
        </div>

        {/* Data Providers Section */}
        <div style={{ display: 'flex', flexDirection: 'column', gap: '4px', marginTop: '6px' }}>
          <Header
            variant="awsui-h1-sticky"
            actions={
              <Button 
                variant="primary"
                onClick={() => addNewRow('dataProvider')}
              >
                <span style={{ fontSize: '12px' }}>Add Data Provider</span>
              </Button>
            }
          >
            <span style={{ fontSize: '16px', fontWeight: '600', lineHeight: '1.1' }}>Data Providers</span>
          </Header>
          <Table
            columnDefinitions={dataProvidersColumnDefinitions}
            items={dataProviders}
            loadingText="Loading data providers"
            variant="borderless"
            stripedRows={false}
            empty={
              <Box textAlign="center" color="inherit">
                <b style={{ fontSize: '14px' }}>No data providers</b>
                <Box
                  padding={{ bottom: "xxs" }}
                  variant="p"
                  color="inherit"
                >
                  <span style={{ fontSize: '13px' }}>No data providers to display.</span>
                </Box>
              </Box>
            }
            header={<></>}
          />
        </div>

        {/* Data Recipients Section */}
        <div style={{ display: 'flex', flexDirection: 'column', gap: '4px', marginTop: '6px' }}>
          <Header
            variant="awsui-h1-sticky"
            actions={
              <Button 
                variant="primary"
                onClick={() => addNewRow('dataRecipient')}
              >
                <span style={{ fontSize: '12px' }}>Add Data Recipient</span>
              </Button>
            }
          >
            <span style={{ fontSize: '16px', fontWeight: '600', lineHeight: '1.1' }}>Data Recipients</span>
          </Header>
          <Table
            columnDefinitions={dataRecipientsColumnDefinitions}
            items={dataRecipients}
            loadingText="Loading data recipients"
            variant="borderless"
            stripedRows={false}
            empty={
              <Box textAlign="center" color="inherit">
                <b style={{ fontSize: '14px' }}>No data recipients</b>
                <Box
                  padding={{ bottom: "xxs" }}
                  variant="p"
                  color="inherit"
                >
                  <span style={{ fontSize: '13px' }}>No data recipients to display.</span>
                </Box>
              </Box>
            }
            header={<></>}
          />
        </div>

        {/* Additional DUA Contact Details Section */}
        <div style={{ display: 'flex', flexDirection: 'column', gap: '4px', marginTop: '6px' }}>
          <Header
            variant="awsui-h1-sticky"
            actions={
              <Button 
                variant="primary"
                onClick={() => addNewRow('duaContact')}
              >
                <span style={{ fontSize: '12px' }}>Add DUA Contacts</span>
              </Button>
            }
          >
            <span style={{ fontSize: '16px', fontWeight: '600', lineHeight: '1.1' }}>Additional DUA Contact Details (as applicable)</span>
          </Header>
          <Table
            columnDefinitions={duaContactsColumnDefinitions}
            items={duaContacts}
            loadingText="Loading DUA contacts"
            variant="borderless"
            stripedRows={false}
            empty={
              <Box textAlign="center" color="inherit">
                <b style={{ fontSize: '14px' }}>No DUA contacts</b>
                <Box
                  padding={{ bottom: "xxs" }}
                  variant="p"
                  color="inherit"
                >
                  <span style={{ fontSize: '13px' }}>No DUA contacts to display.</span>
                </Box>
              </Box>
            }
            header={<></>}
          />
        </div>
      </div>
    </Container>
  );
};

export default PartiesToAgreement1_1; 