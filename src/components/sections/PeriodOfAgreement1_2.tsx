import React, { useState } from 'react';
import {
  Container,
  Header,
  Box,
  DateRangePicker,
  FormField,
  SpaceBetween,
} from '@cloudscape-design/components';

interface PeriodOfAgreementProps {
  // Props can be added here for data management when logic is implemented
}

const PeriodOfAgreement1_2: React.FC<PeriodOfAgreementProps> = () => {
  const [dateRange, setDateRange] = useState<{
    startDate: string | null;
    endDate: string | null;
  }>({
    startDate: null,
    endDate: null,
  });

  const handleDateRangeChange = (detail: any) => {
    setDateRange({
      startDate: detail.value?.startDate || null,
      endDate: detail.value?.endDate || null,
    });
  };

  return (
    <Container>
      <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
        <div>
          <span style={{ fontSize: '15px', fontWeight: '600', lineHeight: '1.1', margin: '0' }}>Section 1.2 - Period of Agreement</span>
        </div>
        
        <div style={{ marginTop: '4px' }}>
          <Box>
            <Box color="text-status-info" fontSize="body-s">
              <span style={{ fontSize: '13px', fontWeight: '500', lineHeight: '1.1', color: '#0073bb' }}>
                Please enter the start date and end date of the agreement
              </span>
            </Box>
          </Box>
        </div>

        <div style={{ marginTop: '12px' }}>
          <FormField
            label={
              <span style={{ fontSize: '14px', fontWeight: '600' }}>
                Agreement Period
              </span>
            }
            description={
              <span style={{ fontSize: '12px',fontWeight: '500', color: '#0073bb' }}>
                Select the start and end dates for this agreement
              </span>
            }
          >
            <DateRangePicker
              value={{
                startDate: dateRange.startDate || '',
                endDate: dateRange.endDate || '',
                type: 'absolute'
              }}
              onChange={({ detail }) => handleDateRangeChange(detail)}
              relativeOptions={[]}
              isValidRange={(range) => {
                // fix can;t build
                if (range?.type === 'absolute' && range?.startDate && range?.endDate) {
                  return {
                    valid: new Date(range.startDate) <= new Date(range.endDate),
                    errorMessage: range.startDate > range.endDate ? 'Start date must be before end date' : undefined
                  };
                }
                // fix can't build
                return { valid: true };
              }}
              i18nStrings={{
                todayAriaLabel: "Today",
                nextMonthAriaLabel: "Next month",
                previousMonthAriaLabel: "Previous month",
                customRelativeRangeDurationLabel: "Duration",
                customRelativeRangeDurationPlaceholder: "Enter duration",
                customRelativeRangeOptionLabel: "Custom range",
                customRelativeRangeOptionDescription: "Set a custom range in the past",
                customRelativeRangeUnitLabel: "Unit of time",
                formatRelativeRange: (e) => {
                  const t = 1 === e.amount ? e.unit : `${e.unit}s`;
                  return `Last ${e.amount} ${t}`;
                },
                formatUnit: (e, t) => (1 === t ? e : `${e}s`),
                dateTimeConstraintText: "Range is 6 to 30 days",
                modeSelectionLabel: "Date range mode",
                relativeModeTitle: "Relative range",
                absoluteModeTitle: "Absolute range",
                relativeRangeSelectionHeading: "Choose a range",
                startDateLabel: "Start date",
                endDateLabel: "End date",
                startTimeLabel: "Start time",
                endTimeLabel: "End time",
                clearButtonLabel: "Clear and dismiss",
                cancelButtonLabel: "Cancel",
                applyButtonLabel: "Apply"
              }}
              dateOnly
              placeholder="Select date range"
              rangeSelectorMode="absolute-only"
            />
          </FormField>
        </div>

        {/* Display selected dates */}
        {(dateRange.startDate || dateRange.endDate) && (
          <div style={{ marginTop: '12px' }}>
            <Box variant="awsui-key-label">
              <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
                <span style={{ fontSize: '14px', fontWeight: '600' }}>Selected Period:</span>
                <div style={{ fontSize: '13px', color: '#5f6b7a' }}>
                  {dateRange.startDate && (
                    <span>Start Date: {new Date(dateRange.startDate).toLocaleDateString()}</span>
                  )}
                  {dateRange.startDate && dateRange.endDate && <span> | </span>}
                  {dateRange.endDate && (
                    <span>End Date: {new Date(dateRange.endDate).toLocaleDateString()}</span>
                  )}
                </div>
              </div>
            </Box>
          </div>
        )}
      </div>
    </Container>
  );
};

export default PeriodOfAgreement1_2; 