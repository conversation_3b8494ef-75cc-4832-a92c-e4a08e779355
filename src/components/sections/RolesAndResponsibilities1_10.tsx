import React, { useState } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  Button,
  Input,
  FormField,
  Textarea,
} from '@cloudscape-design/components';
import { Utils } from '../../common/utils';

interface RoleContactInfo {
  id: string;
  name: string;
  organization: string;
  roleTitle: string;
  email: string;
}

interface RolesAndResponsibilitiesProps {
  // Props can be added here for data management when logic is implemented
}

const RolesAndResponsibilities1_10: React.FC<RolesAndResponsibilitiesProps> = () => {
  // State for managing the different role types
  const [projectOfficers, setProjectOfficers] = useState<RoleContactInfo[]>(() => [
    { id: Utils.generateId('po'), name: '', organization: '', roleTitle: '', email: '' }
  ]);

  const [dataCustodians, setDataCustodians] = useState<RoleContactInfo[]>(() => [
    { id: Utils.generateId('dc'), name: '', organization: '', roleTitle: '', email: '' }
  ]);

  const [systemManagers, setSystemManagers] = useState<RoleContactInfo[]>(() => [
    { id: Utils.generateId('sm'), name: '', organization: '', roleTitle: '', email: '' }
  ]);

  const [dataStewards, setDataStewards] = useState<RoleContactInfo[]>(() => [
    { id: Utils.generateId('ds'), name: '', organization: '', roleTitle: '', email: '' }
  ]);

  // State for the description text area
  const [description, setDescription] = useState<string>('');

  // Function to add new row
  const addNewRow = (type: 'projectOfficer' | 'dataCustodian' | 'systemManager' | 'dataSteward') => {
    const template = { name: '', organization: '', roleTitle: '', email: '' };
    
    switch (type) {
      case 'projectOfficer':
        Utils.addNewItem(projectOfficers, setProjectOfficers, template, 'po');
        break;
      case 'dataCustodian':
        Utils.addNewItem(dataCustodians, setDataCustodians, template, 'dc');
        break;
      case 'systemManager':
        Utils.addNewItem(systemManagers, setSystemManagers, template, 'sm');
        break;
      case 'dataSteward':
        Utils.addNewItem(dataStewards, setDataStewards, template, 'ds');
        break;
    }
  };

  // Function to delete row
  const deleteRow = (type: 'projectOfficer' | 'dataCustodian' | 'systemManager' | 'dataSteward', itemId: string) => {
    switch (type) {
      case 'projectOfficer':
        Utils.deleteItem(projectOfficers, setProjectOfficers, itemId);
        break;
      case 'dataCustodian':
        Utils.deleteItem(dataCustodians, setDataCustodians, itemId);
        break;
      case 'systemManager':
        Utils.deleteItem(systemManagers, setSystemManagers, itemId);
        break;
      case 'dataSteward':
        Utils.deleteItem(dataStewards, setDataStewards, itemId);
        break;
    }
  };

  // Function to update item data
  const updateItem = (items: RoleContactInfo[], setItems: React.Dispatch<React.SetStateAction<RoleContactInfo[]>>, itemId: string, field: keyof RoleContactInfo, value: string) => {
    Utils.updateItem(items, setItems, itemId, field, value);
  };

  // Create column definitions for each role type
  const createColumnDefinitions = (
    items: RoleContactInfo[], 
    setItems: React.Dispatch<React.SetStateAction<RoleContactInfo[]>>,
    type: 'projectOfficer' | 'dataCustodian' | 'systemManager' | 'dataSteward'
  ) => [
    {
      id: 'name',
      header: (
        <div style={{ textAlign: 'center', fontSize: '14px', fontWeight: '600' }}>
          Name
        </div>
      ),
      cell: (item: RoleContactInfo) => (
        <Input
          value={item.name}
          placeholder=""
          onChange={(event) => updateItem(items, setItems, item.id, 'name', event.detail.value)}
        />
      ),
      width: 200,
    },
    {
      id: 'organization',
      header: (
        <div style={{ textAlign: 'center', fontSize: '14px', fontWeight: '600' }}>
          Organizations
        </div>
      ),
      cell: (item: RoleContactInfo) => (
        <Input
          value={item.organization}
          placeholder=""
          onChange={(event) => updateItem(items, setItems, item.id, 'organization', event.detail.value)}
        />
      ),
      width: 200,
    },
    {
      id: 'roleTitle',
      header: (
        <div style={{ textAlign: 'center', fontSize: '14px', fontWeight: '600' }}>
          Role / Title
        </div>
      ),
      cell: (item: RoleContactInfo) => (
        <Input
          value={item.roleTitle}
          placeholder=""
          onChange={(event) => updateItem(items, setItems, item.id, 'roleTitle', event.detail.value)}
        />
      ),
      width: 200,
    },
    {
      id: 'email',
      header: (
        <div style={{ textAlign: 'center', fontSize: '14px', fontWeight: '600' }}>
          Email
        </div>
      ),
      cell: (item: RoleContactInfo) => (
        <Input
          value={item.email}
          placeholder=""
          onChange={(event) => updateItem(items, setItems, item.id, 'email', event.detail.value)}
        />
      ),
      width: 200,
    },
    {
      id: 'actions',
      header: (
        <div style={{ textAlign: 'center', fontSize: '14px', fontWeight: '600' }}>
          Actions
        </div>
      ),
      cell: (item: RoleContactInfo) => (
        <div style={{ display: 'flex', justifyContent: 'center' }}>
          {items.length > 1 && (
            <Button
              variant="icon"
              iconName="remove"
              onClick={() => deleteRow(type, item.id)}
              ariaLabel="Delete row"
            />
          )}
        </div>
      ),
      width: 100,
    },
  ];

  return (
    <Container>
      <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
        <div>
          <span style={{ fontSize: '15px', fontWeight: '600', lineHeight: '1.1', margin: '0' }}>Section 1.10 - Roles and Responsibilities</span>
        </div>
        
        <div style={{ marginTop: '4px' }}>
          <Box>
            <Box color="text-status-info" fontSize="body-s">
              {/* <span style={{ fontSize: '13px', lineHeight: '1.1', color: '#5f6b7a' }}> */}
              <span style={{ fontSize: '13px',fontWeight: '500', lineHeight: '1.1', color: '#0073bb' }}>
                Identify the project officer, data custodian, system manager, and data stewards. Fill in their contact details
              </span>
            </Box>
          </Box>
        </div>

        {/* Project Officer Section */}
        <div style={{ display: 'flex', flexDirection: 'column', gap: '4px', marginTop: '12px' }}>
          <Header
            variant="awsui-h1-sticky"
            actions={
              <Button 
                variant="primary"
                onClick={() => addNewRow('projectOfficer')}
              >
                <span style={{ fontSize: '12px' }}>Add Another Project Officer</span>
              </Button>
            }
          >
            <span style={{ fontSize: '16px', fontWeight: '600', lineHeight: '1.1' }}>Project Officer</span>
          </Header>
          <Table
            columnDefinitions={createColumnDefinitions(projectOfficers, setProjectOfficers, 'projectOfficer')}
            items={projectOfficers}
            loadingText="Loading project officers"
            variant="borderless"
            stripedRows={false}
            empty={
              <Box textAlign="center" color="inherit">
                <b style={{ fontSize: '14px' }}>No project officers</b>
                <Box
                  padding={{ bottom: "xxs" }}
                  variant="p"
                  color="inherit"
                >
                  <span style={{ fontSize: '13px' }}>No project officers to display.</span>
                </Box>
              </Box>
            }
            header={<></>}
          />
        </div>

        {/* Data Custodian Section */}
        <div style={{ display: 'flex', flexDirection: 'column', gap: '4px', marginTop: '12px' }}>
          <Header
            variant="awsui-h1-sticky"
            actions={
              <Button 
                variant="primary"
                onClick={() => addNewRow('dataCustodian')}
              >
                <span style={{ fontSize: '12px' }}>Add Another Data Custodian</span>
              </Button>
            }
          >
            <span style={{ fontSize: '16px', fontWeight: '600', lineHeight: '1.1' }}>Data Custodian</span>
          </Header>
          <Table
            columnDefinitions={createColumnDefinitions(dataCustodians, setDataCustodians, 'dataCustodian')}
            items={dataCustodians}
            loadingText="Loading data custodians"
            variant="borderless"
            stripedRows={false}
            empty={
              <Box textAlign="center" color="inherit">
                <b style={{ fontSize: '14px' }}>No data custodians</b>
                <Box
                  padding={{ bottom: "xxs" }}
                  variant="p"
                  color="inherit"
                >
                  <span style={{ fontSize: '13px' }}>No data custodians to display.</span>
                </Box>
              </Box>
            }
            header={<></>}
          />
        </div>

        {/* System Manager Section */}
        <div style={{ display: 'flex', flexDirection: 'column', gap: '4px', marginTop: '12px' }}>
          <Header
            variant="awsui-h1-sticky"
            actions={
              <Button 
                variant="primary"
                onClick={() => addNewRow('systemManager')}
              >
                <span style={{ fontSize: '12px' }}>Add Another System Manager</span>
              </Button>
            }
          >
            <span style={{ fontSize: '16px', fontWeight: '600', lineHeight: '1.1' }}>System Manager</span>
          </Header>
          <Table
            columnDefinitions={createColumnDefinitions(systemManagers, setSystemManagers, 'systemManager')}
            items={systemManagers}
            loadingText="Loading system managers"
            variant="borderless"
            stripedRows={false}
            empty={
              <Box textAlign="center" color="inherit">
                <b style={{ fontSize: '14px' }}>No system managers</b>
                <Box
                  padding={{ bottom: "xxs" }}
                  variant="p"
                  color="inherit"
                >
                  <span style={{ fontSize: '13px' }}>No system managers to display.</span>
                </Box>
              </Box>
            }
            header={<></>}
          />
        </div>

        {/* Data Stewards Section */}
        <div style={{ display: 'flex', flexDirection: 'column', gap: '4px', marginTop: '12px' }}>
          <Header
            variant="awsui-h1-sticky"
            actions={
              <Button 
                variant="primary"
                onClick={() => addNewRow('dataSteward')}
              >
                <span style={{ fontSize: '12px' }}>Add Another Data Steward</span>
              </Button>
            }
          >
            <span style={{ fontSize: '16px', fontWeight: '600', lineHeight: '1.1' }}>Data Stewards</span>
          </Header>
          <Table
            columnDefinitions={createColumnDefinitions(dataStewards, setDataStewards, 'dataSteward')}
            items={dataStewards}
            loadingText="Loading data stewards"
            variant="borderless"
            stripedRows={false}
            empty={
              <Box textAlign="center" color="inherit">
                <b style={{ fontSize: '14px' }}>No data stewards</b>
                <Box
                  padding={{ bottom: "xxs" }}
                  variant="p"
                  color="inherit"
                >
                  <span style={{ fontSize: '13px' }}>No data stewards to display.</span>
                </Box>
              </Box>
            }
            header={<></>}
          />
        </div>

        {/* Description Section */}
        <div style={{ marginTop: '20px' }}>
          <Box>
            <Box color="text-status-info" fontSize="body-s">
              <span style={{ fontSize: '13px', fontWeight: '500', lineHeight: '1.1', color: '#0073bb' }}>
                Provide a summative statement describing the data users or data consumers of this data:
              </span>
            </Box>
          </Box>
          
          <div style={{ marginTop: '8px', width: '151%' }}>
            <FormField>
              <Textarea
                value={description}
                onChange={(event) => setDescription(event.detail.value)}
                placeholder="Start typing here"
                rows={20}
              />
            </FormField>
          </div>
        </div>
      </div>
    </Container>
  );
};

export default RolesAndResponsibilities1_10; 