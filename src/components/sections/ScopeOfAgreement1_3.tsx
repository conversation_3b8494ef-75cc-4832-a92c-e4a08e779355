import React, { useState } from 'react';
import {
  Con<PERSON><PERSON>,
  Header,
  Box,
  Table,
  Button,
  Input,
  FormField,
  Textarea,
} from '@cloudscape-design/components';
import { Utils } from '../../common/utils';
import { DeleteIcon } from './utils';

interface DatasetSource {
  id: string;
  datasetName: string;
  sourceSystemName: string;
}

interface ScopeOfAgreementProps {
  // Props can be added here for data management when logic is implemented
}

const ScopeOfAgreement1_3: React.FC<ScopeOfAgreementProps> = () => {
  // State for managing dataset and source system data
  const [datasetSources, setDatasetSources] = useState<DatasetSource[]>(() => [
    { id: Utils.generateId('ds'), datasetName: '', sourceSystemName: '' }
  ]);

  // State for the purpose description
  const [purposeDescription, setPurposeDescription] = useState<string>('');

  // Function to add new row
  const addNewRow = () => {
    Utils.addNewItem(
      datasetSources,
      setDatasetSources,
      { datasetName: '', sourceSystemName: '' },
      'ds'
    );
  };

  // Function to delete row
  const deleteRow = (itemId: string) => {
    Utils.deleteItem(datasetSources, setDatasetSources, itemId);
  };

  // Function to update item data
  const updateItem = (itemId: string, field: keyof DatasetSource, value: string) => {
    Utils.updateItem(datasetSources, setDatasetSources, itemId, field, value);
  };

  const columnDefinitions = [
    {
      id: 'datasetName',
      header: (
        <div style={{ textAlign: 'center', fontSize: '14px', fontWeight: '600' }}>
          Dataset Names
        </div>
      ),
      cell: (item: DatasetSource) => (
        <Input
          value={item.datasetName}
          placeholder=""
          onChange={(event) => updateItem(item.id, 'datasetName', event.detail.value)}
        />
      ),
      width: 300,
    },
    {
      id: 'sourceSystemName',
      header: (
        <div style={{ textAlign: 'center', fontSize: '14px', fontWeight: '600' }}>
          Source System Names
        </div>
      ),
      cell: (item: DatasetSource) => (
        <Input
          value={item.sourceSystemName}
          placeholder=""
          onChange={(event) => updateItem(item.id, 'sourceSystemName', event.detail.value)}
        />
      ),
      width: 300,
    },
    {
      id: 'actions',
      header: (
        <div style={{ textAlign: 'center', fontSize: '14px', fontWeight: '600' }}>
          Actions
        </div>
      ),
      cell: (item: DatasetSource) => (
        <div style={{ display: 'flex', justifyContent: 'center' }}>
          {datasetSources.length > 1 && (
            <Button
              variant="icon"
              // iconName="remove"
              iconSvg={DeleteIcon}
              onClick={() => deleteRow(item.id)}
              ariaLabel="Delete row"
            />
          )}
        </div>
      ),
      width: 100,
    },
  ];

  return (
    <Container>
      <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
        <div>
          <span style={{ fontSize: '15px', fontWeight: '600', lineHeight: '1.1', margin: '0' }}>Section 1.3 - Scope of Agreement</span>
        </div>
        
        <div style={{ marginTop: '4px' }}>
          <Box>
            <Box color="text-status-info" fontSize="body-s">
              <span style={{ fontSize: '13px', fontWeight: '500', lineHeight: '1.1', color: '#0073bb' }}>
                Please fill in the names of the data sets and the corresponding source systems
              </span>
            </Box>
          </Box>
        </div>

        {/* Dataset and Source System Table */}
        <div style={{ display: 'flex', flexDirection: 'column', gap: '4px', marginTop: '12px' }}>
          <Header
            variant="awsui-h1-sticky"
            actions={
              <Button 
                variant="primary"
                onClick={addNewRow}
              >
                <span style={{ fontSize: '12px' }}>Add Dataset and Source System Names</span>
              </Button>
            }
          >
            <span style={{ fontSize: '16px', fontWeight: '600', lineHeight: '1.1' }}>Dataset and Source System Information</span>
          </Header>
          <Table
            columnDefinitions={columnDefinitions}
            items={datasetSources}
            loadingText="Loading datasets"
            variant="borderless"
            stripedRows={false}
            empty={
              <Box textAlign="center" color="inherit">
                <b style={{ fontSize: '14px' }}>No datasets</b>
                <Box
                  padding={{ bottom: "xxs" }}
                  variant="p"
                  color="inherit"
                >
                  <span style={{ fontSize: '13px' }}>No datasets to display.</span>
                </Box>
              </Box>
            }
            header={<></>}
          />
        </div>

        {/* Purpose Description Section */}
        <div style={{ marginTop: '20px' }}>
          <Box>
            <Box color="text-status-info" fontSize="body-s">
              <span style={{ fontSize: '13px',fontWeight: '500', lineHeight: '1.1', color: '#0073bb' }}>
                Please insert a high-level description of the purpose of the agreement and data to be shared:
              </span>
            </Box>
          </Box>
          
          <div style={{ marginTop: '8px', width: '151%' }}>
            <FormField>
              <Textarea
                value={purposeDescription}
                onChange={(event) => setPurposeDescription(event.detail.value)}
                placeholder="Start typing here"
                rows={20}
                // deleted resize='vertical'
              />
            </FormField>
          </div>
        </div>
      </div>
    </Container>
  );
};

export default ScopeOfAgreement1_3; 