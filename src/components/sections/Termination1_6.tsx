import React, { useState } from 'react';
import {
  Container,
  Box,
  FormField,
  RadioGroup,
  SpaceBetween,
  Textarea,
  Input,
} from '@cloudscape-design/components';

interface TerminationProps {
  // Props can be added here for data management when logic is implemented
}

const Termination1_6: React.FC<TerminationProps> = () => {
  // State for the termination question
  const [terminationRequired, setTerminationRequired] = useState<string>('');
  
  // State for the timeframe description when "Yes" is selected
  const [terminationTimeframe, setTerminationTimeframe] = useState<string>('');

  // State for calendar days when "No" is selected
  const [calendarDays, setCalendarDays] = useState<string>('');
  const [calendarDaysError, setCalendarDaysError] = useState<string>('');

  // Function to handle calendar days input
  const handleCalendarDaysChange = (value: string) => {
    // Allow empty string
    if (value === '') {
      setCalendarDays('');
      setCalendarDaysError('');
      return;
    }

    // Check if input is a valid positive number
    const numericValue = parseFloat(value);
    
    // Check if it's a valid number
    if (isNaN(numericValue)) {
      setCalendarDaysError('Please enter a valid number');
      setCalendarDays(value); // Keep the invalid input to show user what they typed
      return;
    }

    // Check if it's negative
    if (numericValue < 0) {
      setCalendarDaysError('Please enter a positive number');
      setCalendarDays(value);
      return;
    }

    // Check if it's an integer (no decimal places for calendar days)
    if (!Number.isInteger(numericValue)) {
      setCalendarDaysError('Please enter a whole number');
      setCalendarDays(value);
      return;
    }

    // Valid input
    setCalendarDays(value);
    setCalendarDaysError('');
  };

  return (
    <Container>
      <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
        <div>
          <span style={{ fontSize: '15px', fontWeight: '600', lineHeight: '1.1', margin: '0' }}>Section 1.6 - Termination</span>
        </div>
        
        <div style={{ marginTop: '16px' }}>
          <FormField>
            <div style={{ marginBottom: '8px' }}>
              <span style={{ fontSize: '14px', color: '#0073bb', fontWeight: '500' }}>
                Is a termination required based on a violation?
              </span>
            </div>
            <RadioGroup
              onChange={({ detail }) => setTerminationRequired(detail.value)}
              value={terminationRequired}
              items={[
                { value: "yes", label: "Yes" },
                { value: "no", label: "No" }
              ]}
            />

            {/* Conditional content when "Yes" is selected */}
            {terminationRequired === 'yes' && (
              <div style={{ marginTop: '16px', marginLeft: '24px' }}>
                <div style={{ marginBottom: '8px' }}>
                  <span style={{ fontSize: '14px', color: '#0073bb', fontWeight: '500' }}>
                    Please describe the timeframe for termination notice
                  </span>
                </div>
                <FormField>
                  <div style={{ marginTop: '8px', width: '227%' }}>
                  <Textarea
                    value={terminationTimeframe}
                    onChange={(event) => setTerminationTimeframe(event.detail.value)}
                    placeholder="Start typing here"
                    rows={20}
                  />
                  </div>
                </FormField>
              </div>
            )}

            {/* Conditional content when "No" is selected */}
            {terminationRequired === 'no' && (
              <div style={{ marginTop: '16px', marginLeft: '24px' }}>
                <FormField
                  label={
                    <span style={{ fontSize: '14px', color: '#0073bb', fontWeight: '500' }}>
                      Enter the number of calendar days for data disposition:
                    </span>
                  }
                  errorText={calendarDaysError}
                >
                  <div style={{ maxWidth: '200px' }}>
                    <Input
                      value={calendarDays}
                      onChange={(event) => handleCalendarDaysChange(event.detail.value)}
                      placeholder=""
                      invalid={!!calendarDaysError}
                      type="text"
                    />
                  </div>
                </FormField>
              </div>
            )}
          </FormField>
        </div>
      </div>
    </Container>
  );
};

export default Termination1_6; 