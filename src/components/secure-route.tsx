import React, { useEffect, useState } from "react";
import { Outlet, useLocation, Navigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { runPixel } from "@semoss/sdk";
import { getUserRole,getConfig } from "@/configs/api";
import { chatActions } from "@/pages/chatbot/playground/chat.slice";
import { useInsight } from "@semoss/sdk-react";
import { setInsightId } from "@/common/apply_insight_id";
import forge from "node-forge";
import axios, { AxiosInstance } from "axios";
import { allApis } from "@/configs/api";
import { preProdPublicKeyPem, prodPublicKeyPem } from "../keys.jsx";
import { userName } from "@/components/chatbot/types";

const RequiredAuth: React.FC = () => {
  const location = useLocation();
  const dispatch = useDispatch();
  const { isAuthorized, insightId } = useInsight();

  // Select user details from Redux
  const userEmail = useSelector((state: any) => state.chat?.userEmail ?? "");
  const roles = useSelector((state: any) => state.chat?.userRoles ?? []);

  // State to track authentication status
  const [authChecked, setAuthChecked] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // Determine public key based on environment
  const isProd = process.env.NODE_ENV === "production";
  const key = isProd ? prodPublicKeyPem : preProdPublicKeyPem;

  // Create an Axios instance
  const axiosInstance = axios.create();

  // Function to encrypt user data
  const encryptData = (data: any) => {
    try {
      const publicKey = forge.pki.publicKeyFromPem(key);
      const encrypted = publicKey.encrypt(JSON.stringify(data), "RSA-OAEP");
      return forge.util.encode64(encrypted);
    } catch (error) {
      console.error("Encryption error:", error);
      return null;
    }
  };

  // Apply x-auth-token interceptor
  const applyxAuthTokenInterceptor = (instance: AxiosInstance, userName: string, roles: string[]) => {
    if (!userName || !roles) return;
    const userData = {
      userName,
      roles,
      date: new Date().toISOString(),
    };

    instance.interceptors.request.use(
      (config) => {
        const encryptedData = encryptData(userData);
        if (encryptedData) {
          config.headers["x-auth-token"] = encryptedData;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );
  };

  useEffect(() => {
    const fetchUserRoles = async (): Promise<string[]> => {
      try {
        const response = await getUserRole.get<{ permission: string }>(""); 
        return response?.data?.permission ? [response.data.permission] : [];
      } catch (error) {
        console.error("Error fetching user roles:", error);
        return [];
      }
    };

    const authenticateUser = async () => {
      let email = "";
      let roles: string[] = [];

      // 🚀 **Development Mode: Skip Auth**
      if (process.env.NODE_ENV === "development") {
        console.log("Development mode: Skipping authentication...");
        email = "<EMAIL>";
        roles = ["OWNER"];
        setInsightId("04b53dfe-8190-4a0e-8b42-4bfc9998b197");
        allApis.forEach((api) => applyxAuthTokenInterceptor(api, email, roles));
        dispatch(chatActions.handleUserEmail(email));
        dispatch(chatActions.handleUserRoles(roles));
        setIsAuthenticated(true);
        setAuthChecked(true);
        return;
      }

      try {
        await new Promise((resolve) => setTimeout(resolve, 200))
        try{
          const configtwo = await getConfig.get("");
          console.log("configTwo", configtwo)
          console.log('configTwoHeaders',configtwo.headers)
        }catch(retryError){
          console.log(retryError, 'something went wrong')
          let redirectLink = retryError.response.headers.redirect
          retryError.response.status === 302 && window.location.assign(redirectLink)
        }
        const { errors, pixelReturn }: { errors?: string[]; pixelReturn: any } = await runPixel(`GetUserInfo();`, insightId);
        if (errors?.length || !pixelReturn?.[0]?.output?.SAML) {
          console.error("Error fetching user info:", errors);
          setAuthChecked(true);
          setIsAuthenticated(false);
          return;
        }

        const samlData = pixelReturn[0].output.SAML;
        email = samlData?.email || "";
        roles = await fetchUserRoles();

        const parseUserName = (samlData: any): userName => {
          // Handle empty or invalid input
          if (!samlData.username) {
            return { firstName: '', lastName: '' };
          }
          // Split by dot (.) to separate first and last name
          const nameParts = samlData.username.split('.');
          // If there's no dot, return empty last name
          if (nameParts.length === 1) {
            return { 
              firstName: nameParts[0],
              lastName: ''
            };  // No need for type assertion since return type is already declared
          }
          return {
            firstName: nameParts[0],
            lastName: nameParts[1]
          };
        };        
        // Extract UserName
        const userName: userName = parseUserName(samlData);
        dispatch(chatActions.handleUserName(userName));
    
        // Apply token & update Redux
        allApis.forEach((api) => applyxAuthTokenInterceptor(api, email, roles));
        dispatch(chatActions.handleUserEmail(email));
        dispatch(chatActions.handleUserRoles(roles));

        setInsightId(insightId);
        setIsAuthenticated(true);
      } catch (error) {
        if(error instanceof SyntaxError){
          console.error('Invalid JSON response', error)
        }else{
          console.error('Error during authentication', error)
        }
        setIsAuthenticated(false)
      } finally {
        setAuthChecked(true);
      }
    };

    authenticateUser();
  }, [dispatch, insightId, isAuthorized]);

  // Apply interceptor after user data is available
  useEffect(() => {
    if (userEmail && roles.length > 0) {
      applyxAuthTokenInterceptor(axiosInstance, userEmail, roles);
    }
  }, [userEmail, roles]);

  // 🚀 **Prevent Rendering Until Auth is Checked**
  if (!authChecked) {
    return <div>Loading...</div>; // Show a loading indicator until auth is complete
  }

  // 🚀 **Redirect Unauthorized Users**
  // if (!isAuthenticated) {
  //   return <Navigate to="/login" state={{ from: location }} replace />;
  // }

  return <Outlet />;
};

export default RequiredAuth;
