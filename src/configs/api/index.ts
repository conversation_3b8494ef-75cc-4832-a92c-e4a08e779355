import axios from 'axios';
import {API_URL, SERVICE_NAME, CFG_API_URL, VITE_PROJECT_ID} from './../../../config';
import {applyInsightIdInterceptor} from "@/common/apply_insight_id";

/**
 * API for the Ping service
 */
export const pingChatAPI = axios.create({
    baseURL: `${API_URL}/api/${SERVICE_NAME}/ping`,
    withCredentials: false,
    headers: {
        'Content-Type': 'application/json',
    },
});

/**
 * API for the config service
 */
export const getConfig = axios.create({
    baseURL: `${CFG_API_URL}/config`,
    withCredentials: false,
    headers: {
        'Content-Type': 'application/json',
    },
});

/**
 * API for the Ping service
 */
export const getUserRole = axios.create({
    baseURL: `${CFG_API_URL}/auth/project/getUserProjectPermission?projectId=${VITE_PROJECT_ID}`,
    withCredentials: false,
    headers: {
        'Content-Type': 'application/json',
    },
});

/**
 * API for the Models service
 */
export const coreModelsAPI = axios.create({
    baseURL: `${API_URL}/api/${SERVICE_NAME}/models`,
    withCredentials: false,
    headers: {
        'Content-Type': 'application/json',
    },
});

/**
 * API for the Prompts service
 */
export const corePromptsAPI = axios.create({
    baseURL: `${API_URL}/api/${SERVICE_NAME}/prompt`,
    withCredentials: false,
    headers: {
        'Content-Type': 'application/json',
    },
});

/**
 * API for the Sessions service
 */
export const coreSessionsAPI = axios.create({
    baseURL: `${API_URL}/api/${SERVICE_NAME}/sessions`,
    withCredentials: false,
    headers: {
        'Content-Type': 'application/json',
    },
});

/**
 * API for the Chat service
 */
export const chatAPI = axios.create({
    baseURL: `${API_URL}/api/${SERVICE_NAME}/chat`,
    withCredentials: false,
    headers: {
        'Content-Type': 'application/json',
    },
});

/**
 * API for the RAG Engines service
 */
export const ragEnginesAPI = axios.create({
    baseURL: `${API_URL}/api/${SERVICE_NAME}/ragEngines`,
    withCredentials: false,
    headers: {
        'Content-Type': 'application/json',
    },
});

/**
 * API for the Embedding service
 */
export const embeddingsAPI = axios.create({
    baseURL: `${API_URL}/api/${SERVICE_NAME}/embeddings`,
    withCredentials: false,
    headers: {
        'Content-Type': 'application/json',
    },
});

/**
 * API for the Workspace service
 */
export const workspaceAPI = axios.create({
    baseURL: `${API_URL}/api/${SERVICE_NAME}/workspaces`,
    withCredentials: false,
    headers: {
        'Content-Type': 'application/json',
    },
});

/**
 * API for the Documents service
 */
export const documentsAPI = axios.create({
    baseURL: `${API_URL}/api/${SERVICE_NAME}/documents`,
    withCredentials: false,
    headers: {
        'Content-Type': 'application/json',
    },
});

/**
 * API for the Feedback service
 */
export const feedbackAPI = axios.create({
    baseURL: `${API_URL}/api/${SERVICE_NAME}/feedback`,
    withCredentials: false,
    headers: {
        'Content-Type': 'application/json',
    },
});

export const crossEncodersAPI = axios.create({
    baseURL: `${API_URL}/api/${SERVICE_NAME}/crossEncoders`,
    withCredentials: false,
    headers: {
        'Content-Type': 'application/json',
    },
});


export const semanticAPI = axios.create({
    baseURL: `${API_URL}/api/${SERVICE_NAME}/semantic`,
    withCredentials: false,
    headers: {
        'Content-Type': 'application/json'
    }
})


export const semanticBedrockAPI = axios.create({
    baseURL: `${API_URL}/sql_demo`,
    withCredentials: false,
    headers: {
        'Content-Type': 'application/json'
    }
})

// ①新增
export const exportMSWordDocAPI = axios.create({
    baseURL: `${API_URL}/api/genai/downloadResponse`,
    withCredentials: false,
    headers: {
        'Content-Type': 'application/json'
    }
})

export const allApis = [
    pingChatAPI,
    getUserRole,
    coreModelsAPI,
    corePromptsAPI,
    coreSessionsAPI,
    chatAPI,
    ragEnginesAPI,
    embeddingsAPI,
    workspaceAPI,
    documentsAPI,
    feedbackAPI,
    crossEncodersAPI,
    semanticAPI,
    semanticBedrockAPI,
    exportMSWordDocAPI
];
  
