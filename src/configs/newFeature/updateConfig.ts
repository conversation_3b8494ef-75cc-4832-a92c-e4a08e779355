export interface UpdateConfig {
  id: string;
  title: string;
  description: string;
  date?: string; // 可选，如果不提供则自动生成当前日期
  enabled?: boolean; // 是否启用此更新，默认为 true
  version?: number; // 可选，版本号，用于区分同一更新的不同版本
}

export interface UpdatesConfig {
  feature: UpdateConfig[];
  bugfix: UpdateConfig[];
  maintenance: UpdateConfig[];
}

// 获取当前日期的格式化字符串
const getCurrentDate = (): string => {
  const now = new Date();
  return now.toISOString().split('T')[0]; // 格式: YYYY-MM-DD
};

// 生成带版本号的唯一ID
const generateVersionedId = (baseId: string, version?: number): string => {
  if (version && version > 1) {
    return `${baseId}-v${version}`;
  }
  return baseId;
};

// 配置更新内容 - 您只需要在这里修改内容
export const UPDATES_CONFIG: UpdatesConfig = {
  // 新功能更新
  feature: [
    {
      id: 'dua-feature',
      title: 'New DUA Feature',
      description: `
        <p>We're excited to announce our new DUA feature:</p>
        <ul>
          <li><strong>Section anchor</strong>: Automatically scroll to the section you want to edit</li>
          <li><strong>Circle Progress</strong>: Added a circle progress bar to the top of the page</li>
        </ul>
        <p>This feature will significantly improve your workflow efficiency!</p>
      `,
      version: 3, // 当您更新内容时，将此数字增加为 2, 3, 4...
      enabled: true
    },
    {
      id: 'dua-feature-2',
      title: 'Coming Soon Feature',
      description: `
        <p>Preview capabilities:</p>
        <ul>
          <li> Preview the completed section</li>
        </ul>
      `,
      enabled: true
    }
  ],

  // Bug 修复更新
  bugfix: [
    {
      id: 'performance-optimization1', 
      title: 'System Performance Optimization',
      description: `
        <p>This update includes the following performance improvements:</p>
        <ul>
          <li>Page loading speed improved by <strong>40%</strong></li>
        </ul>
      `,
      version: 1,
      enabled: true
    }
  ],

  // 系统维护通知
  maintenance: [
    {
      id: 'system-maintenance',
      title: 'Scheduled System Maintenance',
      description: `
        <p><strong>Maintenance Time:</strong> May 30, 2025 05:00-04:00 (UTC+8)</p>
        <p>The system will be temporarily unavailable during maintenance. Please save your work in advance.</p>
        <ul>
          <li>Performance improvements</li>
        </ul>
      `,
      version: 1,
      enabled: true
    }
  ]
};

// 处理配置的辅助函数
export const processUpdatesConfig = (config: UpdatesConfig) => {
  const processedUpdates: any[] = [];

  Object.entries(config).forEach(([type, updates]) => {
    updates
      .filter(update => update.enabled !== false) // 只处理启用的更新
      .forEach(update => {
        processedUpdates.push({
          id: generateVersionedId(update.id, update.version), // 生成带版本号的ID
          title: update.title,
          description: update.description,
          date: update.date || getCurrentDate(), // 如果没有日期，使用当前日期
          type: type as 'feature' | 'bugfix' | 'maintenance',
          isRead: false, // 默认未读状态
          version: update.version || 1
        });
      });
  });

  // 按日期排序，最新的在前
  return processedUpdates.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
};



// ============ 使用说明 ============
// 更新内容后如何重新激活未读状态：
// 
// 🎯 方法1（最推荐）：增加版本号
// 在配置中将 version: 1 改为 version: 2
// 系统会自动生成新的ID，如：'dua-feature-v2'
//
// 🔄 方法2：在浏览器控制台执行：resetUpdateStatus()
// 这会清除所有已读状态
//
// 🆔 方法3：直接更改 id 字段
// 例如：'dua-feature' → 'dua-feature-enhanced'
//
// 🛠️ 方法4：在开发者工具中删除 localStorage 中的 'feature_updates_read_status' 项

// ============ 快速更新指南 ============
// 1. 要更新 DUA 功能的内容：
//    - 修改 description 内容
//    - 将 version: 1 改为 version: 2
//    - 保存文件即可
//
// 2. 要添加新的更新：
//    - 复制现有的配置对象
//    - 更改 id 和 title
//    - 设置 version: 1
//    - 添加到对应的数组中 