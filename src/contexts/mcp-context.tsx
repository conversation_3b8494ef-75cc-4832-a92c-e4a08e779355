import React, { createContext, useState, useEffect, useContext, ReactNode } from 'react';

// 条件导入MCP SDK
let Client: any;
let StreamableHTTPClientTransport: any;
let StdioClientTransport: any;

// 检测是否为浏览器环境
const isBrowser = typeof window !== 'undefined';

// 定义模拟客户端的接口
interface MockClientInterface {
  isConnected: boolean;
  transport: any;
  connect(transport: any): Promise<void>;
  request(method: string, params: any): Promise<any>;
}

// 定义模拟传输的接口
interface MockTransportInterface {
  url?: URL;
  close(): Promise<void>;
}

// 包装导入以捕获可能的错误
try {
  // 移除.js扩展名以提高兼容性
  const mcpSdk = require('@modelcontextprotocol/sdk/client/index');
  Client = mcpSdk.Client;

  const streamableHttp = require('@modelcontextprotocol/sdk/client/streamableHttp');
  StreamableHTTPClientTransport = streamableHttp.StreamableHTTPClientTransport;

  // 只在非浏览器环境中导入StdioClientTransport
  if (!isBrowser) {
    const stdio = require('@modelcontextprotocol/sdk/client/stdio');
    StdioClientTransport = stdio.StdioClientTransport;
  }
} catch (error) {
  // 优雅地处理导入错误，便于调试
  console.warn('Failed to import MCP SDK:', error);
  // 创建模拟客户端，但实际测试连接状态
  Client = class MockClient implements MockClientInterface {
    isConnected: boolean;
    transport: any;

    constructor() {
      this.isConnected = false;
      this.transport = null;
    }

    async connect(transport: any): Promise<void> {
      // 对于模拟客户端，实际尝试验证连接
      try {
        this.transport = transport;
        if (transport && transport.url) {
          // 尝试向服务器发送测试请求
          const response = await fetch(transport.url.toString(), {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              jsonrpc: '2.0',
              id: 'connection-test',
              method: 'mcp.ping',
              params: {}
            })
          });

          this.isConnected = response.ok;
          if (!response.ok) {
            throw new Error(`Server responded with status: ${response.status}`);
          }
          return Promise.resolve();
        }
        throw new Error('Invalid transport configuration');
      } catch (error) {
        console.error('Connection failed:', error);
        this.isConnected = false;
        return Promise.reject(error);
      }
    }

    async request(method: string, params: any): Promise<any> {
      if (!this.isConnected) {
        return Promise.reject(new Error('Not connected to server'));
      }
      // 模拟返回基本响应
      return Promise.resolve({ result: {}, id: 'mock-response' });
    }
  };

  StreamableHTTPClientTransport = class MockTransport implements MockTransportInterface {
    url?: URL;

    constructor(url?: URL) {
      this.url = url;
    }

    close(): Promise<void> {
      return Promise.resolve();
    }
  };

  StdioClientTransport = class MockStdioTransport implements MockTransportInterface {
    constructor() {}

    close(): Promise<void> {
      return Promise.resolve();
    }
  };
}

interface MCPServer {
  id: string;
  name: string;
  url?: string;
  command?: string;
  args?: string;
  description?: string;
  isConnected: boolean;
  connectionType: 'url' | 'command';
}

interface MCPClient {
  server: MCPServer;
  client: any;
  transport: any;
}

interface MCPContextType {
  servers: MCPServer[];
  clients: Record<string, MCPClient>;
  addServer: (server: MCPServer) => Promise<boolean>;
  removeServer: (id: string) => void;
  updateServerStatus: (id: string, isConnected: boolean) => void;
  getClient: (serverId: string) => MCPClient | null;
  isConnecting: boolean;
}

const MCPContext = createContext<MCPContextType | undefined>(undefined);

export const MCPProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [servers, setServers] = useState<MCPServer[]>([]);
  const [clients, setClients] = useState<Record<string, MCPClient>>({});
  const [isConnecting, setIsConnecting] = useState(false);
  const [lastReconnectAttempt, setLastReconnectAttempt] = useState<number>(0);

  // Load servers from localStorage on initial load
  useEffect(() => {
    try {
      // 安全地获取localStorage数据
      const savedServers = typeof localStorage !== 'undefined' ? localStorage.getItem('mcpServers') : null;
      if (savedServers) {
        const parsedServers = JSON.parse(savedServers) as MCPServer[];
        setServers(parsedServers);

        // 只在非浏览器环境或URL类型的服务器尝试自动连接
        const serversToConnect = parsedServers.filter(server =>
          server.isConnected && (!isBrowser || server.connectionType === 'url')
        );

        if (serversToConnect.length > 0) {
          console.log(`Attempting to auto-connect to ${serversToConnect.length} server(s)`);

          // 使用Promise.allSettled来处理所有连接尝试，不阻塞任何一个失败的连接
          Promise.allSettled(serversToConnect.map(server =>
            connectToServer(server)
              .catch(err => {
                console.error(`Failed to reconnect to server ${server.name}:`, err);

                // 更新服务器状态为断开连接
                const updatedServers = parsedServers.map(s =>
                  s.id === server.id ? { ...s, isConnected: false } : s
                );

                // 安全地更新localStorage
                try {
                  if (typeof localStorage !== 'undefined') {
                    localStorage.setItem('mcpServers', JSON.stringify(updatedServers));
                  }
                  setServers(updatedServers);
                } catch (storageError) {
                  console.error('Failed to update localStorage:', storageError);
                }

                throw err; // 重新抛出错误以便Promise.allSettled可以正确捕获
              })
          )).then(results => {
            console.log('Reconnection attempts completed:',
              results.filter(r => r.status === 'fulfilled').length, 'successful,',
              results.filter(r => r.status === 'rejected').length, 'failed'
            );
          });
        }
      }
    } catch (error) {
      console.error('Error loading MCP servers from localStorage:', error);
      // 静默失败，使用空数组继续
      setServers([]);
    }
  }, []);

  // 定期检查服务器连接状态
  useEffect(() => {
    // 没有服务器时不需要检查
    if (servers.length === 0) return;

    // 定义检查服务器的函数
    const checkServers = async () => {
      // 避免频繁检查，至少间隔20秒
      const now = Date.now();
      if (now - lastReconnectAttempt < 20000) return;
      setLastReconnectAttempt(now);

      // console.log('定期检查服务器连接状态...');

      // 找出需要重连的服务器 - 当前未连接但为URL类型的服务器
      const serversToCheck = servers.filter(server =>
        !server.isConnected && server.connectionType === 'url' && server.url
      );

      if (serversToCheck.length === 0) return;

      // 检查每个断开连接的服务器是否可达
      for (const server of serversToCheck) {
        if (!server.url) continue;

        // 避免在检查时设置全局加载状态
        try {
          // 先做一个简单的可达性测试
          const isReachable = await fetch(server.url, {
            method: 'HEAD',
            signal: AbortSignal.timeout(3000)
          })
            .then(() => true)
            .catch(() => false);

          if (isReachable) {
            // console.log(`服务器 ${server.name} 现在可达，尝试重连...`);
            // 尝试建立连接，标记为已连接
            const serverToConnect = { ...server, isConnected: true };
            const success = await connectToServer(serverToConnect)
              .catch(err => {
                // console.log(`自动重连到 ${server.name} 失败:`, err);
                return false;
              });

            if (success) {
              // console.log(`成功重连到服务器 ${server.name}`);
              // 更新服务器状态
              updateServerStatus(server.id, true);
            }
          }
        } catch (error) {
          console.error(`Error occurred while checking the status of server ${server.name}:`, error);
        }
      }
    };

    // 设置定期检查服务器的间隔
    const intervalId = setInterval(checkServers, 30000); // 每30秒检查一次

    // 初次加载也检查一次
    checkServers();

    // 清理函数
    return () => clearInterval(intervalId);
  }, [servers, lastReconnectAttempt]);

  const saveServers = (updatedServers: MCPServer[]) => {
    try {
      // 安全地更新localStorage
      if (typeof localStorage !== 'undefined') {
        localStorage.setItem('mcpServers', JSON.stringify(updatedServers));
      }
      setServers(updatedServers);
    } catch (error) {
      console.error('Failed to save servers to localStorage:', error);
      // 至少更新内存中的状态
      setServers(updatedServers);
    }
  };

  const connectToServer = async (server: MCPServer) => {
    setIsConnecting(true);

    try {
      // 检查是否成功加载了MCP SDK
      if (!Client || typeof Client !== 'function') {
        throw new Error('MCP SDK not available');
      }

      const client = new Client({
        name: 'mcp-client',
        version: '1.0.0'
      });

      let transport;

      if (server.connectionType === 'url' && server.url) {
        // 检查是否成功加载了StreamableHTTPClientTransport
        if (!StreamableHTTPClientTransport || typeof StreamableHTTPClientTransport !== 'function') {
          throw new Error('StreamableHTTPClientTransport not available');
        }

        try {
          // 在创建传输前验证URL有效性
          const testUrl = new URL(server.url);

          // 在连接前进行简单的可用性检查
          try {
            const connectionCheck = await fetch(testUrl.toString(), {
              method: 'HEAD',
              // 设置小超时以快速检测服务器是否可达
              signal: AbortSignal.timeout(3000)
            }).catch(e => {
              console.error('Server connectivity test failed:', e);
              return null;
            });

            if (!connectionCheck) {
              throw new Error('Server is unreachable');
            }
          } catch (fetchError) {
            console.error('Server connectivity check failed:', fetchError);
            throw new Error(`Server is unreachable: ${fetchError.message}`);
          }

          // Connect via URL
          transport = new StreamableHTTPClientTransport(testUrl);
        } catch (urlError) {
          console.error('URL transport creation error:', urlError);
          throw new Error(`Failed to create URL transport: ${urlError.message}`);
        }
      } else if (server.connectionType === 'command' && server.command) {
        // 在浏览器环境中不支持命令行模式
        if (isBrowser) {
          throw new Error('Command mode is not supported in browser environments');
        }

        // 检查是否成功加载了StdioClientTransport
        if (!StdioClientTransport || typeof StdioClientTransport !== 'function') {
          throw new Error('StdioClientTransport not available');
        }

        // Improved command line parsing for arguments
        let args: string[] = [];
        if (server.args) {
          // Handle quoted arguments properly
          const argsString = server.args.trim();
          const regex = /([^\s"']+)|"([^"]*)"|'([^']*)'/g;
          let match;

          while ((match = regex.exec(argsString)) !== null) {
            // The first element in match is the full match, then captured groups
            // Get the first non-undefined group (either unquoted, double-quoted, or single-quoted)
            const arg = match[1] || match[2] || match[3];
            args.push(arg);
          }

          // If regex didn't match anything but args isn't empty,
          // just use it as a single argument
          if (args.length === 0 && argsString) {
            args = [argsString];
          }
        }

        console.log('Starting command:', server.command, 'with args:', args);

        try {
          // Connect via command
          transport = new StdioClientTransport({
            command: server.command,
            args: args
          });
        } catch (stdioError) {
          console.error('Command transport creation error:', stdioError);
          throw new Error(`Failed to create command transport: ${stdioError.message}`);
        }
      } else {
        throw new Error('Invalid server configuration');
      }

      // Add debug info
      console.log('Connecting to MCP server:', server.name);

      // Connect with timeout
      const connectPromise = client.connect(transport);
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Connection timeout')), 10000);
      });

      await Promise.race([connectPromise, timeoutPromise]);

      // 验证连接成功后，尝试发送一个简单的 ping 请求作为健康检查
      try {
        await client.request('mcp.ping', {}).catch(e => {
          console.error('Ping test failed:', e);
          throw new Error('Server responded but failed ping test');
        });
      } catch (pingError) {
        console.error('Ping verification failed:', pingError);
        throw new Error(`Connected but server verification failed: ${pingError.message}`);
      }

      console.log('Connected successfully to:', server.name);

      setClients(prev => ({
        ...prev,
        [server.id]: {
          server,
          client,
          transport
        }
      }));

      return true;
    } catch (error) {
      console.error(`Failed to connect to MCP server ${server.name}:`, error);
      throw error;
    } finally {
      setIsConnecting(false);
    }
  };

  const addServer = async (server: MCPServer) => {
    try {
      // 修改：不要在连接前预设连接状态
      const serverToConnect = { ...server, isConnected: false };

      if (server.isConnected) {
        const connected = await connectToServer(serverToConnect);
        // 仅当实际连接成功后才更新连接状态
        if (connected) {
          serverToConnect.isConnected = true;
        } else {
          // 如果连接失败，确保状态为false
          serverToConnect.isConnected = false;
        }
      }

      const updatedServers = [...servers, serverToConnect];
      saveServers(updatedServers);

      return serverToConnect.isConnected;
    } catch (error) {
      console.error('Failed to add MCP server:', error);
      return false;
    }
  };

  const removeServer = (id: string) => {
    try {
      // Disconnect client if it exists
      if (clients[id]) {
        try {
          // 安全地关闭传输连接
          if (clients[id].transport && typeof clients[id].transport.close === 'function') {
            clients[id].transport.close().catch(err => {
              console.error(`Error disconnecting from server ${id}:`, err);
            });
          }
        } catch (closeError) {
          console.error(`Error during transport close for server ${id}:`, closeError);
        }

        // Remove client
        const updatedClients = { ...clients };
        delete updatedClients[id];
        setClients(updatedClients);
      }

      // Remove server from list
      const updatedServers = servers.filter(server => server.id !== id);
      saveServers(updatedServers);
    } catch (error) {
      console.error(`Failed to properly remove server ${id}:`, error);
      // 尝试最基本的更新
      try {
        setServers(prev => prev.filter(server => server.id !== id));
        setClients(prev => {
          const updated = { ...prev };
          delete updated[id];
          return updated;
        });
      } catch (finalError) {
        console.error('Final error in removeServer:', finalError);
      }
    }
  };

  const updateServerStatus = (id: string, isConnected: boolean) => {
    try {
      // 更新服务器列表中的状态
      const updatedServers = servers.map(server =>
        server.id === id ? { ...server, isConnected } : server
      );

      setServers(updatedServers);

      // 更新 localStorage
      try {
        if (typeof localStorage !== 'undefined') {
          localStorage.setItem('mcpServers', JSON.stringify(updatedServers));
        }
      } catch (storageError) {
        console.error('Failed to update server status in localStorage:', storageError);
      }

      // 如果服务器标记为断开连接，尝试关闭现有客户端连接
      if (!isConnected && clients[id]) {
        try {
          if (clients[id].transport && typeof clients[id].transport.close === 'function') {
            clients[id].transport.close().catch(err => {
              console.error(`Error disconnecting from server ${id}:`, err);
            });
          }

          // 移除客户端实例
          const updatedClients = { ...clients };
          delete updatedClients[id];
          setClients(updatedClients);
        } catch (closeError) {
          console.error(`Error closing transport for server ${id}:`, closeError);
        }
      }
    } catch (error) {
      console.error(`Failed to update status for server ${id}:`, error);
    }
  };

  const getClient = (serverId: string): MCPClient | null => {
    const client = clients[serverId];
    // 添加实际客户端存在性检查
    if (!client || !client.client) return null;

    // 验证客户端是否仍然连接
    if (typeof client.client.isConnected === 'boolean' && !client.client.isConnected) {
      return null;
    }

    return client;
  };

  return (
    <MCPContext.Provider
      value={{
        servers,
        clients,
        addServer,
        removeServer,
        updateServerStatus,
        getClient,
        isConnecting
      }}
    >
      {children}
    </MCPContext.Provider>
  );
};

export const useMCP = () => {
  const context = useContext(MCPContext);
  if (context === undefined) {
    throw new Error('useMCP must be used within an MCPProvider');
  }
  return context;
};

export default MCPContext;
// 修改后
// import React, { createContext, useState, useEffect, useContext, ReactNode } from 'react';
//
// // 条件导入MCP SDK
// let Client: any;
// let StreamableHTTPClientTransport: any;
// let StdioClientTransport: any;
//
// // 检测是否为浏览器环境
// const isBrowser = typeof window !== 'undefined';
//
// // 定义模拟客户端的接口
// interface MockClientInterface {
//   isConnected: boolean;
//   transport: any;
//   connect(transport: any): Promise<void>;
//   request(method: string, params: any): Promise<any>;
// }
//
// // 定义模拟传输的接口
// interface MockTransportInterface {
//   url?: URL;
//   close(): Promise<void>;
// }
//
// // 包装导入以捕获可能的错误
// try {
//   // 移除.js扩展名以提高兼容性
//   const mcpSdk = require('@modelcontextprotocol/sdk/client/index');
//   Client = mcpSdk.Client;
//
//   const streamableHttp = require('@modelcontextprotocol/sdk/client/streamableHttp');
//   StreamableHTTPClientTransport = streamableHttp.StreamableHTTPClientTransport;
//
//   // 只在非浏览器环境中导入StdioClientTransport
//   if (!isBrowser) {
//     const stdio = require('@modelcontextprotocol/sdk/client/stdio');
//     StdioClientTransport = stdio.StdioClientTransport;
//   }
// } catch (error) {
//   // 优雅地处理导入错误，便于调试
//   console.warn('MCP SDK导入失败:', error);
//   // 创建模拟客户端，但实际测试连接状态
//   Client = class MockClient implements MockClientInterface {
//     isConnected: boolean;
//     transport: any;
//
//     constructor() {
//       this.isConnected = false;
//       this.transport = null;
//     }
//
//     async connect(transport: any): Promise<void> {
//       // 对于模拟客户端，实际尝试验证连接
//       try {
//         this.transport = transport;
//         if (transport && transport.url) {
//           // 尝试向服务器发送测试请求
//           const response = await fetch(transport.url.toString(), {
//             method: 'POST',
//             headers: {
//               'Content-Type': 'application/json'
//             },
//             body: JSON.stringify({
//               jsonrpc: '2.0',
//               id: 'connection-test',
//               method: 'mcp.ping',
//               params: {}
//             })
//           });
//
//           this.isConnected = response.ok;
//           if (!response.ok) {
//             throw new Error(`Server responded with status: ${response.status}`);
//           }
//           return Promise.resolve();
//         }
//         throw new Error('Invalid transport configuration');
//       } catch (error) {
//         console.error('Connection failed:', error);
//         this.isConnected = false;
//         return Promise.reject(error);
//       }
//     }
//
//     async request(method: string, params: any): Promise<any> {
//       if (!this.isConnected) {
//         return Promise.reject(new Error('Not connected to server'));
//       }
//       // 模拟返回基本响应
//       return Promise.resolve({ result: {}, id: 'mock-response' });
//     }
//   };
//
//   StreamableHTTPClientTransport = class MockTransport implements MockTransportInterface {
//     url?: URL;
//
//     constructor(url?: URL) {
//       this.url = url;
//     }
//
//     close(): Promise<void> {
//       return Promise.resolve();
//     }
//   };
//
//   StdioClientTransport = class MockStdioTransport implements MockTransportInterface {
//     constructor() {}
//
//     close(): Promise<void> {
//       return Promise.resolve();
//     }
//   };
// }
//
// interface MCPServer {
//   id: string;
//   name: string;
//   url?: string;
//   command?: string;
//   args?: string;
//   description?: string;
//   isConnected: boolean;
//   connectionType: 'url' | 'command';
// }
//
// interface MCPClient {
//   server: MCPServer;
//   client: any;
//   transport: any;
// }
//
// interface MCPContextType {
//   servers: MCPServer[];
//   clients: Record<string, MCPClient>;
//   addServer: (server: MCPServer) => Promise<boolean>;
//   removeServer: (id: string) => void;
//   updateServerStatus: (id: string, isConnected: boolean) => void;
//   getClient: (serverId: string) => MCPClient | null;
//   isConnecting: boolean;
// }
//
// const MCPContext = createContext<MCPContextType | undefined>(undefined);
//
// export const MCPProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
//   const [servers, setServers] = useState<MCPServer[]>([]);
//   const [clients, setClients] = useState<Record<string, MCPClient>>({});
//   const [isConnecting, setIsConnecting] = useState(false);
//
//   // Load servers from localStorage on initial load
//   useEffect(() => {
//     try {
//       // 安全地获取localStorage数据
//       const savedServers = typeof localStorage !== 'undefined' ? localStorage.getItem('mcpServers') : null;
//       if (savedServers) {
//         const parsedServers = JSON.parse(savedServers) as MCPServer[];
//         setServers(parsedServers);
//
//         // 只在非浏览器环境或URL类型的服务器尝试自动连接
//         const serversToConnect = parsedServers.filter(server =>
//           server.isConnected && (!isBrowser || server.connectionType === 'url')
//         );
//
//         if (serversToConnect.length > 0) {
//           console.log(`Attempting to auto-connect to ${serversToConnect.length} server(s)`);
//
//           // 使用Promise.allSettled来处理所有连接尝试，不阻塞任何一个失败的连接
//           Promise.allSettled(serversToConnect.map(server =>
//             connectToServer(server)
//               .catch(err => {
//                 console.error(`Failed to reconnect to server ${server.name}:`, err);
//
//                 // 更新服务器状态为断开连接
//                 const updatedServers = parsedServers.map(s =>
//                   s.id === server.id ? { ...s, isConnected: false } : s
//                 );
//
//                 // 安全地更新localStorage
//                 try {
//                   if (typeof localStorage !== 'undefined') {
//                     localStorage.setItem('mcpServers', JSON.stringify(updatedServers));
//                   }
//                   setServers(updatedServers);
//                 } catch (storageError) {
//                   console.error('Failed to update localStorage:', storageError);
//                 }
//
//                 throw err; // 重新抛出错误以便Promise.allSettled可以正确捕获
//               })
//           )).then(results => {
//             console.log('Reconnection attempts completed:',
//               results.filter(r => r.status === 'fulfilled').length, 'successful,',
//               results.filter(r => r.status === 'rejected').length, 'failed'
//             );
//           });
//         }
//       }
//     } catch (error) {
//       console.error('Error loading MCP servers from localStorage:', error);
//       // 静默失败，使用空数组继续
//       setServers([]);
//     }
//   }, []);
//
//   const saveServers = (updatedServers: MCPServer[]) => {
//     try {
//       // 安全地更新localStorage
//       if (typeof localStorage !== 'undefined') {
//         localStorage.setItem('mcpServers', JSON.stringify(updatedServers));
//       }
//       setServers(updatedServers);
//     } catch (error) {
//       console.error('Failed to save servers to localStorage:', error);
//       // 至少更新内存中的状态
//       setServers(updatedServers);
//     }
//   };
//
//   const connectToServer = async (server: MCPServer) => {
//     setIsConnecting(true);
//
//     try {
//       // 检查是否成功加载了MCP SDK
//       if (!Client || typeof Client !== 'function') {
//         throw new Error('MCP SDK not available');
//       }
//
//       const client = new Client({
//         name: 'mcp-client',
//         version: '1.0.0'
//       });
//
//       let transport;
//
//       if (server.connectionType === 'url' && server.url) {
//         // 检查是否成功加载了StreamableHTTPClientTransport
//         if (!StreamableHTTPClientTransport || typeof StreamableHTTPClientTransport !== 'function') {
//           throw new Error('StreamableHTTPClientTransport not available');
//         }
//
//         try {
//           // 在创建传输前验证URL有效性
//           const testUrl = new URL(server.url);
//
//           // 在连接前进行简单的可用性检查
//           try {
//             const connectionCheck = await fetch(testUrl.toString(), {
//               method: 'HEAD',
//               // 设置小超时以快速检测服务器是否可达
//               signal: AbortSignal.timeout(3000)
//             }).catch(e => {
//               console.error('Server connectivity test failed:', e);
//               return null;
//             });
//
//             if (!connectionCheck) {
//               throw new Error('Server is unreachable');
//             }
//           } catch (fetchError) {
//             console.error('Server connectivity check failed:', fetchError);
//             throw new Error(`Server is unreachable: ${fetchError.message}`);
//           }
//
//           // Connect via URL
//           transport = new StreamableHTTPClientTransport(testUrl);
//         } catch (urlError) {
//           console.error('URL transport creation error:', urlError);
//           throw new Error(`Failed to create URL transport: ${urlError.message}`);
//         }
//       } else if (server.connectionType === 'command' && server.command) {
//         // 在浏览器环境中不支持命令行模式
//         if (isBrowser) {
//           throw new Error('Command mode is not supported in browser environments');
//         }
//
//         // 检查是否成功加载了StdioClientTransport
//         if (!StdioClientTransport || typeof StdioClientTransport !== 'function') {
//           throw new Error('StdioClientTransport not available');
//         }
//
//         // Improved command line parsing for arguments
//         let args: string[] = [];
//         if (server.args) {
//           // Handle quoted arguments properly
//           const argsString = server.args.trim();
//           const regex = /([^\s"']+)|"([^"]*)"|'([^']*)'/g;
//           let match;
//
//           while ((match = regex.exec(argsString)) !== null) {
//             // The first element in match is the full match, then captured groups
//             // Get the first non-undefined group (either unquoted, double-quoted, or single-quoted)
//             const arg = match[1] || match[2] || match[3];
//             args.push(arg);
//           }
//
//           // If regex didn't match anything but args isn't empty,
//           // just use it as a single argument
//           if (args.length === 0 && argsString) {
//             args = [argsString];
//           }
//         }
//
//         console.log('Starting command:', server.command, 'with args:', args);
//
//         try {
//           // Connect via command
//           transport = new StdioClientTransport({
//             command: server.command,
//             args: args
//           });
//         } catch (stdioError) {
//           console.error('Command transport creation error:', stdioError);
//           throw new Error(`Failed to create command transport: ${stdioError.message}`);
//         }
//       } else {
//         throw new Error('Invalid server configuration');
//       }
//
//       // Add debug info
//       console.log('Connecting to MCP server:', server.name);
//
//       // Connect with timeout
//       const connectPromise = client.connect(transport);
//       const timeoutPromise = new Promise((_, reject) => {
//         setTimeout(() => reject(new Error('Connection timeout')), 10000);
//       });
//
//       await Promise.race([connectPromise, timeoutPromise]);
//
//       // 验证连接成功后，尝试发送一个简单的 ping 请求作为健康检查
//       try {
//         await client.request('mcp.ping', {}).catch(e => {
//           console.error('Ping test failed:', e);
//           throw new Error('Server responded but failed ping test');
//         });
//       } catch (pingError) {
//         console.error('Ping verification failed:', pingError);
//         throw new Error(`Connected but server verification failed: ${pingError.message}`);
//       }
//
//       console.log('Connected successfully to:', server.name);
//
//       setClients(prev => ({
//         ...prev,
//         [server.id]: {
//           server,
//           client,
//           transport
//         }
//       }));
//
//       return true;
//     } catch (error) {
//       console.error(`Failed to connect to MCP server ${server.name}:`, error);
//       throw error;
//     } finally {
//       setIsConnecting(false);
//     }
//   };
//
//   const addServer = async (server: MCPServer) => {
//     try {
//       // 修改：不要在连接前预设连接状态
//       const serverToConnect = { ...server, isConnected: false };
//
//       if (server.isConnected) {
//         const connected = await connectToServer(serverToConnect);
//         // 仅当实际连接成功后才更新连接状态
//         if (connected) {
//           serverToConnect.isConnected = true;
//         } else {
//           // 如果连接失败，确保状态为false
//           serverToConnect.isConnected = false;
//         }
//       }
//
//       const updatedServers = [...servers, serverToConnect];
//       saveServers(updatedServers);
//
//       return serverToConnect.isConnected;
//     } catch (error) {
//       console.error('Failed to add MCP server:', error);
//       return false;
//     }
//   };
//
//   const removeServer = (id: string) => {
//     try {
//       // Disconnect client if it exists
//       if (clients[id]) {
//         try {
//           // 安全地关闭传输连接
//           if (clients[id].transport && typeof clients[id].transport.close === 'function') {
//             clients[id].transport.close().catch(err => {
//               console.error(`Error disconnecting from server ${id}:`, err);
//             });
//           }
//         } catch (closeError) {
//           console.error(`Error during transport close for server ${id}:`, closeError);
//         }
//
//         // Remove client
//         const updatedClients = { ...clients };
//         delete updatedClients[id];
//         setClients(updatedClients);
//       }
//
//       // Remove server from list
//       const updatedServers = servers.filter(server => server.id !== id);
//       saveServers(updatedServers);
//     } catch (error) {
//       console.error(`Failed to properly remove server ${id}:`, error);
//       // 尝试最基本的更新
//       try {
//         setServers(prev => prev.filter(server => server.id !== id));
//         setClients(prev => {
//           const updated = { ...prev };
//           delete updated[id];
//           return updated;
//         });
//       } catch (finalError) {
//         console.error('Final error in removeServer:', finalError);
//       }
//     }
//   };
//
//   const updateServerStatus = (id: string, isConnected: boolean) => {
//     try {
//       // 更新服务器列表中的状态
//       const updatedServers = servers.map(server =>
//         server.id === id ? { ...server, isConnected } : server
//       );
//
//       setServers(updatedServers);
//
//       // 更新 localStorage
//       try {
//         if (typeof localStorage !== 'undefined') {
//           localStorage.setItem('mcpServers', JSON.stringify(updatedServers));
//         }
//       } catch (storageError) {
//         console.error('Failed to update server status in localStorage:', storageError);
//       }
//
//       // 如果服务器标记为断开连接，尝试关闭现有客户端连接
//       if (!isConnected && clients[id]) {
//         try {
//           if (clients[id].transport && typeof clients[id].transport.close === 'function') {
//             clients[id].transport.close().catch(err => {
//               console.error(`Error disconnecting from server ${id}:`, err);
//             });
//           }
//
//           // 移除客户端实例
//           const updatedClients = { ...clients };
//           delete updatedClients[id];
//           setClients(updatedClients);
//         } catch (closeError) {
//           console.error(`Error closing transport for server ${id}:`, closeError);
//         }
//       }
//     } catch (error) {
//       console.error(`Failed to update status for server ${id}:`, error);
//     }
//   };
//
//   const getClient = (serverId: string): MCPClient | null => {
//     const client = clients[serverId];
//     // 添加实际客户端存在性检查
//     if (!client || !client.client) return null;
//
//     // 验证客户端是否仍然连接
//     if (typeof client.client.isConnected === 'boolean' && !client.client.isConnected) {
//       return null;
//     }
//
//     return client;
//   };
//
//   return (
//     <MCPContext.Provider
//       value={{
//         servers,
//         clients,
//         addServer,
//         removeServer,
//         updateServerStatus,
//         getClient,
//         isConnecting
//       }}
//     >
//       {children}
//     </MCPContext.Provider>
//   );
// };
//
// export const useMCP = () => {
//   const context = useContext(MCPContext);
//   if (context === undefined) {
//     throw new Error('useMCP must be used within an MCPProvider');
//   }
//   return context;
// };
//
// export default MCPContext;


// 原版
// import React, { createContext, useState, useEffect, useContext, ReactNode } from 'react';
//
// // 条件导入MCP SDK
// let Client: any;
// let StreamableHTTPClientTransport: any;
// let StdioClientTransport: any;
//
// // 检测是否为浏览器环境
// const isBrowser = typeof window !== 'undefined';
//
// // 包装导入以捕获可能的错误
// try {
//   // 移除.js扩展名以提高兼容性
//   const mcpSdk = require('@modelcontextprotocol/sdk/client/index');
//   Client = mcpSdk.Client;
//
//   const streamableHttp = require('@modelcontextprotocol/sdk/client/streamableHttp');
//   StreamableHTTPClientTransport = streamableHttp.StreamableHTTPClientTransport;
//
//   // 只在非浏览器环境中导入StdioClientTransport
//   if (!isBrowser) {
//     const stdio = require('@modelcontextprotocol/sdk/client/stdio');
//     StdioClientTransport = stdio.StdioClientTransport;
//   }
// } catch (error) {
//   // 优雅地处理导入错误，便于调试
//   console.warn('MCP SDK导入失败:', error);
//   // 创建空的模拟客户端以防止应用崩溃
//   Client = class MockClient {
//     constructor() {}
//     connect() { return Promise.resolve(); }
//     request() { return Promise.resolve({}); }
//   };
//   StreamableHTTPClientTransport = class MockTransport {
//     constructor() {}
//     close() { return Promise.resolve(); }
//   };
//   StdioClientTransport = class MockStdioTransport {
//     constructor() {}
//     close() { return Promise.resolve(); }
//   };
// }
//
// interface MCPServer {
//   id: string;
//   name: string;
//   url?: string;
//   command?: string;
//   args?: string;
//   description?: string;
//   isConnected: boolean;
//   connectionType: 'url' | 'command';
// }
//
// interface MCPClient {
//   server: MCPServer;
//   client: any;
//   transport: any;
// }
//
// interface MCPContextType {
//   servers: MCPServer[];
//   clients: Record<string, MCPClient>;
//   addServer: (server: MCPServer) => Promise<boolean>;
//   removeServer: (id: string) => void;
//   getClient: (serverId: string) => MCPClient | null;
//   isConnecting: boolean;
// }
//
// const MCPContext = createContext<MCPContextType | undefined>(undefined);
//
// export const MCPProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
//   const [servers, setServers] = useState<MCPServer[]>([]);
//   const [clients, setClients] = useState<Record<string, MCPClient>>({});
//   const [isConnecting, setIsConnecting] = useState(false);
//
//   // Load servers from localStorage on initial load
//   useEffect(() => {
//     try {
//       // 安全地获取localStorage数据
//       const savedServers = typeof localStorage !== 'undefined' ? localStorage.getItem('mcpServers') : null;
//       if (savedServers) {
//         const parsedServers = JSON.parse(savedServers) as MCPServer[];
//         setServers(parsedServers);
//
//         // 只在非浏览器环境或URL类型的服务器尝试自动连接
//         const serversToConnect = parsedServers.filter(server =>
//           server.isConnected && (!isBrowser || server.connectionType === 'url')
//         );
//
//         if (serversToConnect.length > 0) {
//           console.log(`Attempting to auto-connect to ${serversToConnect.length} server(s)`);
//
//           // 使用Promise.allSettled来处理所有连接尝试，不阻塞任何一个失败的连接
//           Promise.allSettled(serversToConnect.map(server =>
//             connectToServer(server)
//               .catch(err => {
//                 console.error(`Failed to reconnect to server ${server.name}:`, err);
//
//                 // 更新服务器状态为断开连接
//                 const updatedServers = parsedServers.map(s =>
//                   s.id === server.id ? { ...s, isConnected: false } : s
//                 );
//
//                 // 安全地更新localStorage
//                 try {
//                   if (typeof localStorage !== 'undefined') {
//                     localStorage.setItem('mcpServers', JSON.stringify(updatedServers));
//                   }
//                   setServers(updatedServers);
//                 } catch (storageError) {
//                   console.error('Failed to update localStorage:', storageError);
//                 }
//
//                 throw err; // 重新抛出错误以便Promise.allSettled可以正确捕获
//               })
//           )).then(results => {
//             console.log('Reconnection attempts completed:',
//               results.filter(r => r.status === 'fulfilled').length, 'successful,',
//               results.filter(r => r.status === 'rejected').length, 'failed'
//             );
//           });
//         }
//       }
//     } catch (error) {
//       console.error('Error loading MCP servers from localStorage:', error);
//       // 静默失败，使用空数组继续
//       setServers([]);
//     }
//   }, []);
//
//   const saveServers = (updatedServers: MCPServer[]) => {
//     try {
//       // 安全地更新localStorage
//       if (typeof localStorage !== 'undefined') {
//         localStorage.setItem('mcpServers', JSON.stringify(updatedServers));
//       }
//       setServers(updatedServers);
//     } catch (error) {
//       console.error('Failed to save servers to localStorage:', error);
//       // 至少更新内存中的状态
//       setServers(updatedServers);
//     }
//   };
//
//   const connectToServer = async (server: MCPServer) => {
//     setIsConnecting(true);
//
//     try {
//       // 检查是否成功加载了MCP SDK
//       if (!Client || typeof Client !== 'function') {
//         throw new Error('MCP SDK not available');
//       }
//
//       const client = new Client({
//         name: 'mcp-client',
//         version: '1.0.0'
//       });
//
//       let transport;
//
//       if (server.connectionType === 'url' && server.url) {
//         // 检查是否成功加载了StreamableHTTPClientTransport
//         if (!StreamableHTTPClientTransport || typeof StreamableHTTPClientTransport !== 'function') {
//           throw new Error('StreamableHTTPClientTransport not available');
//         }
//
//         try {
//           // Connect via URL
//           transport = new StreamableHTTPClientTransport(
//             new URL(server.url)
//           );
//         } catch (urlError) {
//           console.error('URL transport creation error:', urlError);
//           throw new Error(`Failed to create URL transport: ${urlError.message}`);
//         }
//       } else if (server.connectionType === 'command' && server.command) {
//         // 在浏览器环境中不支持命令行模式
//         if (isBrowser) {
//           throw new Error('Command mode is not supported in browser environments');
//         }
//
//         // 检查是否成功加载了StdioClientTransport
//         if (!StdioClientTransport || typeof StdioClientTransport !== 'function') {
//           throw new Error('StdioClientTransport not available');
//         }
//
//         // Improved command line parsing for arguments
//         let args: string[] = [];
//         if (server.args) {
//           // Handle quoted arguments properly
//           const argsString = server.args.trim();
//           const regex = /([^\s"']+)|"([^"]*)"|'([^']*)'/g;
//           let match;
//
//           while ((match = regex.exec(argsString)) !== null) {
//             // The first element in match is the full match, then captured groups
//             // Get the first non-undefined group (either unquoted, double-quoted, or single-quoted)
//             const arg = match[1] || match[2] || match[3];
//             args.push(arg);
//           }
//
//           // If regex didn't match anything but args isn't empty,
//           // just use it as a single argument
//           if (args.length === 0 && argsString) {
//             args = [argsString];
//           }
//         }
//
//         console.log('Starting command:', server.command, 'with args:', args);
//
//         try {
//           // Connect via command
//           transport = new StdioClientTransport({
//             command: server.command,
//             args: args
//           });
//         } catch (stdioError) {
//           console.error('Command transport creation error:', stdioError);
//           throw new Error(`Failed to create command transport: ${stdioError.message}`);
//         }
//       } else {
//         throw new Error('Invalid server configuration');
//       }
//
//       // Add debug info
//       console.log('Connecting to MCP server:', server.name);
//
//       // Connect with timeout
//       const connectPromise = client.connect(transport);
//       const timeoutPromise = new Promise((_, reject) => {
//         setTimeout(() => reject(new Error('Connection timeout')), 10000);
//       });
//
//       await Promise.race([connectPromise, timeoutPromise]);
//       console.log('Connected successfully to:', server.name);
//
//       setClients(prev => ({
//         ...prev,
//         [server.id]: {
//           server,
//           client,
//           transport
//         }
//       }));
//
//       return true;
//     } catch (error) {
//       console.error(`Failed to connect to MCP server ${server.name}:`, error);
//       throw error;
//     } finally {
//       setIsConnecting(false);
//     }
//   };
//
//   const addServer = async (server: MCPServer) => {
//     try {
//       if (server.isConnected) {
//         await connectToServer(server);
//       }
//
//       const updatedServers = [...servers, server];
//       saveServers(updatedServers);
//
//       return true;
//     } catch (error) {
//       console.error('Failed to add MCP server:', error);
//       return false;
//     }
//   };
//
//   const removeServer = (id: string) => {
//     try {
//       // Disconnect client if it exists
//       if (clients[id]) {
//         try {
//           // 安全地关闭传输连接
//           if (clients[id].transport && typeof clients[id].transport.close === 'function') {
//             clients[id].transport.close().catch(err => {
//               console.error(`Error disconnecting from server ${id}:`, err);
//             });
//           }
//         } catch (closeError) {
//           console.error(`Error during transport close for server ${id}:`, closeError);
//         }
//
//         // Remove client
//         const updatedClients = { ...clients };
//         delete updatedClients[id];
//         setClients(updatedClients);
//       }
//
//       // Remove server from list
//       const updatedServers = servers.filter(server => server.id !== id);
//       saveServers(updatedServers);
//     } catch (error) {
//       console.error(`Failed to properly remove server ${id}:`, error);
//       // 尝试最基本的更新
//       try {
//         setServers(prev => prev.filter(server => server.id !== id));
//         setClients(prev => {
//           const updated = { ...prev };
//           delete updated[id];
//           return updated;
//         });
//       } catch (finalError) {
//         console.error('Final error in removeServer:', finalError);
//       }
//     }
//   };
//
//   const getClient = (serverId: string): MCPClient | null => {
//     return clients[serverId] || null;
//   };
//
//   return (
//     <MCPContext.Provider
//       value={{
//         servers,
//         clients,
//         addServer,
//         removeServer,
//         getClient,
//         isConnecting
//       }}
//     >
//       {children}
//     </MCPContext.Provider>
//   );
// };
//
// export const useMCP = () => {
//   const context = useContext(MCPContext);
//   if (context === undefined) {
//     throw new Error('useMCP must be used within an MCPProvider');
//   }
//   return context;
// };
//
// export default MCPContext;