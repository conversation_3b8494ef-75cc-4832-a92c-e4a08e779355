import "regenerator-runtime/runtime";
import React from "react";
import ReactDOM from "react-dom/client";
import "@cloudscape-design/global-styles/index.css";
import AppConfigured from "./components/app-configured";
import { ErrorBoundary } from "react-error-boundary";
import ErrorFallBack from "./components/ErrorFallBack";

import { StorageHelper } from "./common/helpers/storage-helper";
import { Security } from "@okta/okta-react";
import { OktaAuth, toRelativeUrl } from "@okta/okta-auth-js";
import { Provider } from "react-redux";
import store from "./redux/store/configureStore";
import {MCPProvider} from '@/contexts/mcp-context'
import { clearAllReadStatus } from "./stores/newFeature/updateStorage";

import oktaConfig from "./oktaConfig";

// const oktaAuth = new OktaAuth(oktaConfig);

function restoreOriginalUri(_oktaAuth: OktaAuth, originalUri: string) {
  window.location.replace(
    toRelativeUrl(originalUri || "/", window.location.origin)
  );
}

// Make clearAllReadStatus available in global scope for debugging
if (typeof window !== 'undefined') {
  (window as any).clearAllReadStatus = clearAllReadStatus;
}

const root = ReactDOM.createRoot(
  document.getElementById("root") as HTMLElement
);

const theme = StorageHelper.getTheme();
StorageHelper.applyTheme(theme);

root.render(
  <React.StrictMode>
    {/* <Security oktaAuth={oktaAuth} restoreOriginalUri={restoreOriginalUri}> */}
    <Provider store={store}>
      <ErrorBoundary
        FallbackComponent={ErrorFallBack}
        onReset={() => (location.href = "/")}
      >
        {/*①*/}
        <MCPProvider>
          <AppConfigured />
        </MCPProvider>
      </ErrorBoundary>
    </Provider>
    {/* </Security> */}
  </React.StrictMode>
);
