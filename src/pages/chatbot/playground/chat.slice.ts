import { ChatBotHistoryItem, userName } from "@/components/chatbot/types";
import { createSlice } from "@reduxjs/toolkit";

export interface intialChatStateProps {
  thumbsUpClicked: boolean;
  comment?: string;
  isFeedbackOpened: boolean;
  chatId: string;
  isFeedbackSubmitted?: boolean;
  messageContent?: string;
  showFeedbackMessage?: boolean;
  messageHistory?: ChatBotHistoryItem[];
  feedbackReasons?: string[];
  userRoles?: string[];
  chatDocuments: Object[];
  userEmail?: string;
  userName?: userName;
  tempWorkspaceId?: string;
} 

const initialState: intialChatStateProps = {
  thumbsUpClicked: false,
  comment: "",
  isFeedbackOpened: false,
  chatId: "",
  isFeedbackSubmitted: false,
  messageContent: "",
  showFeedbackMessage: false,
  messageHistory: [],
  feedbackReasons: [],
  userRoles: [],
  chatDocuments: [],
  userEmail: "",
  userName: {firstName:"John",lastName:""},
  tempWorkspaceId: "",
};
const chatSlice = createSlice({
  name: "chat",
  initialState,
  reducers: {
    setThumbsUpClicked(state, action) {
      state.thumbsUpClicked = action.payload;
    },
    setComments(state, action) {
      state.comment = action.payload;
    },
    feedbackOpened(state, action) {
      state.isFeedbackOpened = action.payload;
    },
    setChatId(state, action) {
      state.chatId = action.payload;
    },
    setFeedbackSubmitted(state, action) {
      state.isFeedbackSubmitted = action.payload;
    },
    handleMessageContent(state, action) {
      state.messageContent = action.payload;
    },
    showFeedbackMessage(state, action) {
      state.showFeedbackMessage = action.payload;
    },
    setMessageHistory(state, action) {
      state.messageHistory = action.payload;
    },
    setFeedbackReasons(state, action) {
      state.feedbackReasons = action.payload;
    },
    handleUserRoles(state, action) {
      state.userRoles = action.payload;
    },
    handleChatDocuments(state, action) {
      state.chatDocuments = action.payload;
    },
    handleUserEmail(state, action) {
      state.userEmail = action.payload;
    },
    handleUserName(state, action) {
      state.userName = action.payload;
    },
    handleTempWorkspaceId(state, action) {
      state.tempWorkspaceId = action.payload;
    }
  },
});

export const chatActions = chatSlice.actions;
export default chatSlice.reducer;
