import { useState } from 'react';
import BaseAppLayout from '../../../components/base-app-layout';
import Chat from '../../../components/chatbot/chat';
import Sessions from '../../../components/chatbot/sessions';
import { useParams } from 'react-router-dom';
import { useSelector } from 'react-redux';

export default function Playground() {
  const { sessionId } = useParams();
  const [toolsOpen, setToolsOpen] = useState(false);
  const dataState = useSelector((state: any) => state?.rootReducer);
  const userRoles = dataState?.chatReducer?.userRoles;

  return (
    <BaseAppLayout
      toolsHide={true}
      toolsOpen={toolsOpen}
      onToolsChange={({ detail }) => setToolsOpen(detail.open)}
      tools={<Sessions toolsOpen={toolsOpen} />}
      toolsWidth={500}
      content={<Chat sessionId={sessionId} />}
    />
  );
}
