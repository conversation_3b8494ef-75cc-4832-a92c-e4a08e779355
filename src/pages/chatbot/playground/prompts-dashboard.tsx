import { useState } from 'react';
import BaseAppLayout from '../../../components/base-app-layout';
import { Prompts } from '../../../components/chatbot/prompts';
import Sessions from '../../../components/chatbot/sessions';

export const PromptsDashboard = () => {
    const [toolsOpen, setToolsOpen] = useState(false);

    return (
        <BaseAppLayout
            toolsHide={false}
            toolsOpen={toolsOpen}
            onToolsChange={({ detail }) => setToolsOpen(detail.open)}
            tools={<Sessions toolsOpen={toolsOpen} />}
            toolsWidth={500}
            content={<Prompts />}
        />
    );
};
