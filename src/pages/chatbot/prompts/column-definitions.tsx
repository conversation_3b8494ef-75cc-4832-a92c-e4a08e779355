import { TableProps } from '@cloudscape-design/components';
import {
    PropertyFilterProperty,
    PropertyFilterOperator,
} from '@cloudscape-design/collection-hooks';

export const PromptsColumnDefinitions: TableProps.ColumnDefinition<any>[] = [
    {
        id: 'name',
        header: 'Name',
        sortingField: 'name',
        cell: (item: any) => item.name,
    },
    {
        id: 'description',
        header: 'Description',
        sortingField: 'description',
        cell: (item: any) => item.description,
    },
    {
        id: 'prompt',
        header: 'Prompt',
        sortingField: 'prompt',
        cell: (item: any) => item.prompt,
    },
    {
        id: 'prompt_type',
        header: 'Prompt Type',
        sortingField: 'prompt_type',
        cell: (item: any) => item.prompt_type,
    },
    {
        id: 'tags',
        header: 'Tags',
        sortingField: 'tags',
        cell: (item: any) => item.tags,
    },
];

export const PromptsColumnFilteringProperties: PropertyFilterProperty[] = [
    {
        propertyLabel: 'Name',
        key: 'name',
        groupValuesLabel: 'Name values',
        operators: [':', '!:', '=', '!='] as PropertyFilterOperator[],
    },
    {
        propertyLabel: 'Description',
        key: 'description',
        groupValuesLabel: 'Description values',
        operators: [':', '!:', '=', '!='] as PropertyFilterOperator[],
    },
    {
        propertyLabel: 'Prompt',
        key: 'prompt',
        groupValuesLabel: 'Prompt values',
        operators: [':', '!:', '=', '!='] as PropertyFilterOperator[],
    },
    {
        propertyLabel: 'Prompt Type',
        key: 'prompt_type',
        groupValuesLabel: 'Prompt Type values',
        operators: [':', '!:', '=', '!='] as PropertyFilterOperator[],
    },
    {
        propertyLabel: 'Tags',
        key: 'tags',
        groupValuesLabel: 'Tag values',
        operators: [':', '!:', '=', '!='] as PropertyFilterOperator[],
    },
].sort((a, b) => a.propertyLabel.localeCompare(b.propertyLabel));
