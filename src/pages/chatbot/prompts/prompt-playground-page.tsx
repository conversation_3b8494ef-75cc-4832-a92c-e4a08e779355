import { useState } from 'react';
import BaseAppLayout from '../../../components/base-app-layout';
import Sessions from '../../../components/chatbot/sessions';
import { useParams } from 'react-router-dom';
import PromptPlayground from '../../../components/chatbot/prompt-playground';

export default function PromptPlaygroundPage() {
    const { promptId } = useParams();
    const [toolsOpen, setToolsOpen] = useState(false);

    return (
        <BaseAppLayout
            toolsHide={false}
            toolsOpen={toolsOpen}
            onToolsChange={({ detail }) => setToolsOpen(detail.open)}
            tools={<Sessions toolsOpen={toolsOpen} />}
            toolsWidth={500}
            content={<PromptPlayground promptId={promptId} />}
        />
    );
}
