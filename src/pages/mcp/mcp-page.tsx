import React from 'react';
import { useNavigate } from 'react-router-dom';
import MCPServerManager from '@/components/mcp/mcp-server-manager';

const MCPPage: React.FC = () => {
  const navigate = useNavigate();

  const handleBackToHome = () => {
    navigate('/');
  };

  return (
    <div style={{ width: '100%', position: 'relative' }}>
      <div style={{
        position: 'absolute',
        top: '20px',
        left: '20px',
        zIndex: 100
      }}>
        <button
          onClick={handleBackToHome}
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '5px',
            padding: '8px 12px',
            border: 'none',
            background: '#f0f0f0',
            borderRadius: '12px',
            cursor: 'pointer',
            fontWeight: 'bold',
            boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
          }}
        >
          <span style={{ fontSize: '20px' }}>←</span>
          <span>Back</span>
        </button>
      </div>

      <div style={{
        width: '100%',
        textAlign: 'center',
        padding: '30px 0 40px 0'
      }}>
        <h1 style={{
          margin: '0 0 10px 0',
          fontSize: '26px',
          fontWeight: 'bold'
        }}>
          Model Context Protocol
        </h1>
        <p style={{
          margin: 0,
          color: '#666',
          fontSize: '16px'
        }}>
          Configure Model Context Protocol (MCP) servers to extend AI capabilities
        </p>
      </div>

      <div style={{
        display: 'flex',
        justifyContent: 'center',
        width: '100%',
        padding: '0 20px'
      }}>
        <div style={{
          maxWidth: '900px',
          width: '100%'
        }}>
          <MCPServerManager />
        </div>
      </div>
    </div>
  );
};

export default MCPPage;