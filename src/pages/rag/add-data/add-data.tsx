/* eslint-disable @typescript-eslint/no-unused-vars */
import {
    BreadcrumbGroup,
    Container,
    ContentLayout,
    Flashbar,
    FormField,
    Header,
    Select,
    SpaceBetween,
    Tabs,
} from '@cloudscape-design/components';
import { useContext, useEffect, useState } from 'react';
import { LoadingStatus } from '../../../common/types';
import { OptionsHelper } from '../../../common/helpers/options-helper';
import BaseAppLayout from '../../../components/base-app-layout';
import useOnFollow from '../../../common/hooks/use-on-follow';
import { useForm } from '../../../common/hooks/use-form';
import { Utils } from '../../../common/utils';
import { AppContext } from '../../../common/app-context';
import { useSearchParams } from 'react-router-dom';
import { AddDataData } from './types';
import DataFileUpload from './data-file-upload';
import { CHATBOT_NAME } from '../../../common/constants';
import { Workspace } from '../../../API';
import workspaceService from '../../../services/workspace.service';

export default function AddData() {
    const onFollow = useOnFollow();
    const appContext = useContext(AppContext);
    const [searchParams, setSearchParams] = useSearchParams();
    const [submitting, setSubmitting] = useState(false);
    const [activeTab, setActiveTab] = useState(
        searchParams.get('tab') || 'file',
    );
    const [workspaces, setWorkspaces] = useState<Workspace[]>([]);
    const [workspacesLoadingStatus, setWorkspacesLoadingStatus] =
        useState<LoadingStatus>('loading');
    const { data, onChange, errors, validate } = useForm<AddDataData>({
        initialValue: () => {
            return {
                workspace: null,
                query: '',
            };
        },
        validate: (form) => {
            const errors: Record<string, string | string[]> = {};

            if (!form.workspace) {
                errors.workspace = 'Workspace is required';
            }

            return errors;
        },
    });

    const workspaceOptions = OptionsHelper.getSelectWorkspaceOptions(
        workspaces || [],
    );
    const selectedWorkspace = workspaces.find(
        (w) => w.workspaceId === data.workspace?.value,
    );

    const recursiveToCamel = (item: unknown): unknown => {
        if (Array.isArray(item)) {
            return item.map((el: unknown) => recursiveToCamel(el));
        } else if (typeof item === 'function' || item !== Object(item)) {
            return item;
        }
        return Object.fromEntries(
            Object.entries(item as Record<string, unknown>).map(
                ([key, value]: [string, unknown]) => [
                    key.replace(/([-_][a-z])/gi, (c) =>
                        c.toUpperCase().replace(/[-_]/g, ''),
                    ),
                    recursiveToCamel(value),
                ],
            ),
        );
    };

    useEffect(() => {
        // if (!appContext) return;

        (async () => {
            // const apiClient = new ApiClient(appContext);
            try {
                const result = await workspaceService.getWorkspaces();
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                const resultCamelCase = result.map((workspace: any) =>
                    recursiveToCamel(workspace),
                );

                const workspaceId = searchParams.get('workspaceId');
                if (workspaceId) {
                    const workspace = resultCamelCase.find(
                        // eslint-disable-next-line @typescript-eslint/no-explicit-any
                        (workspace: any) => workspace.id === workspaceId,
                    );

                    if (workspace) {
                        onChange({
                            workspace: {
                                label: workspace.name,
                                value: workspaceId,
                            },
                        });
                    }
                }

                // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
                setWorkspaces(resultCamelCase!);
                setWorkspacesLoadingStatus('finished');

                //after workspaces are loaded, check if workspaceId is in the url params and load the workspace
                if (workspaceId) {
                    const workspace = resultCamelCase.find((workspace: any) => workspace.workspaceId === workspaceId);

                    if (workspace) {
                        onChange({
                            workspace: {
                                label: workspace.name,
                                value: workspaceId,
                            },
                            query: '',
                        });
                    }
                }
            } catch (error) {
                setWorkspacesLoadingStatus('error');
            }
        })();
    }, [appContext, onChange, searchParams]);

    // if (Utils.isDevelopment()) {
    //     console.log('re-render');
    // }

    const workspace = workspaces.find(
        (c) => c.workspaceId === data.workspace?.value,
    );
    const showTabs = !workspace?.kendraIndexExternal;
    const disabledTabs =
        workspace?.engine === 'kendra' ? ['qna', 'website', 'rssfeed'] : [];

    return (
        <BaseAppLayout
            contentType="cards"
            breadcrumbs={
                <BreadcrumbGroup
                    onFollow={onFollow}
                    items={[
                        {
                            text: CHATBOT_NAME,
                            href: '/',
                        },
                        {
                            text: 'RAG',
                            href: '/rag',
                        },
                        {
                            text: 'Document Libraries',
                            href: '/rag/workspaces',
                        },
                        ...(data.workspace?.label
                            ? [
                                  {
                                      text: data.workspace?.label,
                                      href: `/rag/workspaces/${data.workspace?.value}`,
                                  },
                              ]
                            : []),
                        {
                            text: 'Add Data',
                            href: '/rag/workspaces/add-data',
                        },
                    ]}
                />
            }
            content={
                <ContentLayout header={<Header variant="h1">Add Data</Header>}>
                    <SpaceBetween size="l">
                        <Container>
                            <SpaceBetween size="l">
                                <FormField
                                    label="Document Library"
                                    errorText={errors.workspace}
                                >
                                    <Select
                                        loadingText="Loading libraries (might take few seconds)..."
                                        statusType={workspacesLoadingStatus}
                                        placeholder="Select a document library"
                                        filteringType="auto"
                                        selectedOption={data.workspace}
                                        options={workspaceOptions}
                                        onChange={({
                                            detail: { selectedOption },
                                        }) => {
                                            onChange({
                                                workspace: selectedOption,
                                            });
                                            setSearchParams((current) => ({
                                                ...Utils.urlSearchParamsToRecord(
                                                    current,
                                                ),
                                                workspaceId:
                                                    selectedOption.value ?? '',
                                            }));
                                        }}
                                        empty={'No Document Libraries available'}
                                    />
                                </FormField>
                            </SpaceBetween>
                        </Container>
                        {workspace?.kendraIndexExternal && (
                            <Flashbar
                                items={[
                                    {
                                        type: 'info',
                                        content: (
                                            <>
                                                Data upload is not available for
                                                external Kendra indexes
                                            </>
                                        ),
                                    },
                                ]}
                            />
                        )}
                        {showTabs && (
                            <Tabs
                                tabs={[
                                    {
                                        label: 'Upload Files',
                                        id: 'file',
                                        content: (
                                            <DataFileUpload
                                                data={data}
                                                validate={validate}
                                                selectedWorkspace={
                                                    selectedWorkspace
                                                }
                                            />
                                        ),
                                    },
                                    // {
                                    //     label: 'Add Text',
                                    //     id: 'text',
                                    //     content: (
                                    //         <AddText
                                    //             data={data}
                                    //             validate={validate}
                                    //             submitting={submitting}
                                    //             setSubmitting={setSubmitting}
                                    //             selectedWorkspace={
                                    //                 selectedWorkspace
                                    //             }
                                    //         />
                                    //     ),
                                    // },
                                ]}
                                activeTabId={activeTab}
                                onChange={({ detail: { activeTabId } }) => {
                                    setActiveTab(activeTabId);
                                    setSearchParams((current) => ({
                                        ...Utils.urlSearchParamsToRecord(
                                            current,
                                        ),
                                        tab: activeTabId,
                                    }));
                                }}
                            />
                        )}
                    </SpaceBetween>
                </ContentLayout>
            }
        />
    );
}
