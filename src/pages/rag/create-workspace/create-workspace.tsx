import { useContext, useEffect, useState } from 'react';
import {
    ContentLayout,
    BreadcrumbGroup,
    StatusIndicator,
    SpaceBetween,
} from '@cloudscape-design/components';
import { CreateWorkspaceHeader } from './create-workspace-header';
import { AppContext } from '../../../common/app-context';
import BaseAppLayout from '../../../components/base-app-layout';
import useOnFollow from '../../../common/hooks/use-on-follow';
import CreateWorkspaceOpenSearch from './create-workspace-opensearch';
import SelectEnginePanel from './select-engine-panel';
import { CHATBOT_NAME } from '../../../common/constants';
import { Utils } from '../../../common/utils';
import ragEnginesService from '../../../services/ragEngines.service';

export default function CreateWorkspace() {
    const onFollow = useOnFollow();
    const appContext = useContext(AppContext);
    const [engine, setEngine] = useState('');
    const [loading, setLoading] = useState(true);
    const [engines, setEngines] = useState<Map<string, boolean>>(new Map());

    useEffect(() => {
        // if (!appContext?.config) return;

        (async () => {
            // const apiClient = new ApiClient(appContext);
            try {
                const listRagEngines = await ragEnginesService.getEngines();

                const engineMap = new Map<string, boolean>();

                listRagEngines.forEach((engine) => {
                    engineMap.set(engine.id, engine.enabled);
                });

                if (listRagEngines.length > 0) {
                    if (engineMap.get('aurora') === true) {
                        setEngine('aurora');
                    } else if (engineMap.get('opensearch') === true) {
                        setEngine('opensearch');
                    } else if (engineMap.get('kendra') === true) {
                        setEngine('kendra');
                    } else if (engineMap.get('aoss') === true) {
                        setEngine('aoss');
                    }
                }

                setEngines(engineMap);
            } catch (error) {
                console.error(Utils.getErrorMessage(error));
            }

            setLoading(false);
        })();
    }, [appContext]);

    return (
        <BaseAppLayout
            breadcrumbs={
                <BreadcrumbGroup
                    onFollow={onFollow}
                    items={[
                        {
                            text: CHATBOT_NAME,
                            href: '/',
                        },
                        {
                            text: 'RAG',
                            href: '/rag',
                        },
                        {
                            text: 'Document Libraries',
                            href: '/rag/workspaces',
                        },
                        {
                            text: 'Create Document Library',
                            href: '/rag/workspaces/create',
                        },
                    ]}
                    expandAriaLabel="Show path"
                    ariaLabel="Breadcrumbs"
                />
            }
            content={
                <ContentLayout header={<CreateWorkspaceHeader />}>
                    <SpaceBetween size="l">
                        <SelectEnginePanel
                            engines={engines}
                            engine={engine}
                            setEngine={setEngine}
                        />
                        {loading && (
                            <StatusIndicator type="loading">
                                Loading
                            </StatusIndicator>
                        )}
                        {engine === 'opensearch' && (
                            <CreateWorkspaceOpenSearch />
                        )}
                    </SpaceBetween>
                </ContentLayout>
            }
        />
    );
}
