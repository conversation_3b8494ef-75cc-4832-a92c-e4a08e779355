import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
} from "@cloudscape-design/components";
import { useCallback, useContext, useEffect, useState } from "react";
import { RagDocumentType } from "../../../common/types";
import RouterButton from "../../../components/wrappers/router-button";
import { TableEmptyState } from "../../../components/table-empty-state";
import { AppContext } from "../../../common/app-context";
// import { ApiClient } from "../../../common/api-client/api-client";
import { getColumnDefinition } from "./columns";
import { Utils } from "../../../common/utils";
import { DocumentsResult } from "../../../API";
import documentService from "../../../services/document.service";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/redux/store/configureStore";

export interface DocumentsTabProps {
  workspaceId?: string;
  documentType: RagDocumentType;
}

export default function DocumentsTab(props: DocumentsTabProps) {
  const appContext = useContext(AppContext);
  const [loading, setLoading] = useState(true);
  const [currentPageIndex, setCurrentPageIndex] = useState(1);
  const [pages, setPages] = useState<(DocumentsResult | undefined)[]>([]);

  const dataState = useSelector((state: RootState) => {
    return state.rootReducer;
  });
const userRoles = dataState?.chatReducer?.userRoles;  

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const recursiveToCamel = (item: unknown): unknown => {
    if (Array.isArray(item)) {
      return item.map((el: unknown) => recursiveToCamel(el));
    } else if (typeof item === "function" || item !== Object(item)) {
      return item;
    }
    return Object.fromEntries(
      Object.entries(item as Record<string, unknown>).map(
        ([key, value]: [string, unknown]) => [
          key.replace(/([-_][a-z])/gi, (c) =>
            c.toUpperCase().replace(/[-_]/g, "")
          ),
          recursiveToCamel(value),
        ]
      )
    );
  };

  const getDocuments = useCallback(
    async (params: { lastDocumentId?: string; pageIndex?: number }) => {
      // if (!appContext) return;
      if (!props.workspaceId) return;

      setLoading(true);

      // const apiClient = new ApiClient(appContext);
      try {
        const payload = {
          workspace_id: props.workspaceId,
          document_type: props.documentType,
          last_document_id: params?.lastDocumentId,
        };
        const result = await documentService.getDocuments(payload);

        setPages((current) => {
          const foundIndex = current.findIndex(
            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
            (c) => c!.lastDocumentId === result.lastDocumentId
          );

          if (foundIndex !== -1) {
            current[foundIndex] = result;
            return [...current];
          } else if (typeof params.pageIndex !== "undefined") {
            current[params.pageIndex - 1] = result;
            return [...current];
          } else if (result.items.length === 0) {
            return current;
          } else {
            return [...current, result];
          }
        });
      } catch (error) {
        console.error(Utils.getErrorMessage(error));
      }

      setLoading(false);
    },
    [appContext, props.documentType, props.workspaceId]
  );

  useEffect(() => {
    getDocuments({});
  }, [getDocuments]);

  const onPageChange = async (pageIndex: number) => {
    if (pageIndex < 1 || pageIndex > pages.length) {
      return;
    }
    setCurrentPageIndex(pageIndex);

    if (pages[pageIndex - 1]) {
      return;
    }

    const lastDocumentId =
      pageIndex > 1 ? pages[currentPageIndex - 2]?.lastDocumentId : undefined;
    await getDocuments({ lastDocumentId, pageIndex });
  };

  //   const onNextPageClick = async () => {
  //     const lastDocumentId = pages[currentPageIndex - 1]?.lastDocumentId;

  //     if (lastDocumentId) {
  //       if (pages.length <= currentPageIndex) {
  //         await getDocuments({ lastDocumentId });
  //       }
  //       //   setCurrentPageIndex((current) => Math.min(pages.length + 1, current + 1));
  //       setCurrentPageIndex((current) => current + 1);
  //     }
  //   };

  //   const onPreviousPageClick = async () => {
  //     setCurrentPageIndex((current) =>
  //       Math.max(1, Math.min(pages.length - 1, current - 1))
  //     );
  //   };

  const onNextPageClick = async () => {
    if (currentPageIndex < pages.length) {
      setCurrentPageIndex(currentPageIndex + 1);
    } else if (pages.length > 0) {
      const lastDocumentId = pages[pages.length - 1]?.lastDocumentId;
      if (lastDocumentId) {
        await getDocuments({ lastDocumentId });
        setCurrentPageIndex(currentPageIndex + 1);
      }
    }
  };

  const onPreviousPageClick = async () => {
    if (currentPageIndex > 1) {
      setCurrentPageIndex(currentPageIndex - 1);
    }
  };

  const refreshPage = async () => {
    if (currentPageIndex <= 1) {
      await getDocuments({ pageIndex: currentPageIndex });
    } else {
      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion, @typescript-eslint/no-non-null-asserted-optional-chain
      const lastDocumentId = pages[currentPageIndex - 2]?.lastDocumentId!;
      await getDocuments({ lastDocumentId });
    }
  };

  const typeStr = ragDocumentTypeToString(props.documentType);
  const typeAddStr = ragDocumentTypeToAddString(props.documentType);
  const typeTitleStr = ragDocumentTypeToTitleString(props.documentType);

  const columnDefinitions = getColumnDefinition(props.documentType);

  return (
    <Table
      loading={loading}
      loadingText={`Loading ${typeStr}s`}
      columnDefinitions={columnDefinitions}
      items={
        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion, @typescript-eslint/no-non-null-asserted-optional-chain
        pages[Math.min(pages.length - 1, currentPageIndex - 1)]?.items!
      }
      header={
        <Header
          actions={
            <SpaceBetween direction="horizontal" size="xs">
              {!userRoles?.includes("READ_ONLY") &&<Button iconName="refresh" onClick={refreshPage} />}
              {!userRoles?.includes("READ_ONLY") && <RouterButton
                href={`/rag/workspaces/add-data?workspaceId=${props.workspaceId}&tab=${props.documentType}`}
              >
                {typeAddStr}
              </RouterButton>}
            </SpaceBetween>
          }
          description="Please expect a delay for your changes to be reflected. Press the refresh button to see the latest changes."
        >
          {typeTitleStr}
        </Header>
      }
      empty={
        <TableEmptyState
          resourceName={typeStr}
          createHref={`/rag/workspaces/add-data?workspaceId=${props.workspaceId}&tab=${props.documentType}`}
          createText={typeAddStr}
        />
      }
      pagination={
        pages.length === 0 ? null : (
          <Pagination
            openEnd={true}
            pagesCount={pages.length}
            currentPageIndex={currentPageIndex}
            onNextPageClick={onNextPageClick}
            onPreviousPageClick={onPreviousPageClick}
            onChange={({ detail }) => onPageChange(detail.currentPageIndex)}
          />
        )
      }
    />
  );
}

function ragDocumentTypeToString(type: RagDocumentType) {
  switch (type) {
    case "file":
      return "File";
    case "text":
      return "Text";
    case "qna":
      return "Q&A";
    case "website":
      return "Website";
    case "rssfeed":
      return "RSS Feed";
    case "rsspost":
      return "RSS Post";
  }
}

function ragDocumentTypeToTitleString(type: RagDocumentType) {
  switch (type) {
    case "file":
      return "Files";
    case "text":
      return "Texts";
    case "qna":
      return "Q&As";
    case "website":
      return "Websites";
    case "rssfeed":
      return "RSS Feeds";
    case "rsspost":
      return "RSS Posts";
  }
}

function ragDocumentTypeToAddString(type: RagDocumentType) {
  switch (type) {
    case "file":
      return "Upload files";
    case "text":
      return "Add texts";
    case "qna":
      return "Add Q&A";
    case "website":
      return "Crawl website";
    case "rssfeed":
      return "Subcribe to RSS Feed";
    case "rsspost":
      return "Add RSS Post";
  }
}
