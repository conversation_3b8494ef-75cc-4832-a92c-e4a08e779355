import {
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    ContentLayout,
    <PERSON><PERSON>,
    <PERSON>er,
    SpaceBetween,
    StatusIndicator,
    Tabs,
} from '@cloudscape-design/components';
import useOnFollow from '../../../common/hooks/use-on-follow';
import BaseAppLayout from '../../../components/base-app-layout';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import { useCallback, useContext, useEffect, useState } from 'react';
import { AppContext } from '../../../common/app-context';
// import { ApiClient } from "../../../common/api-client/api-client";
import { Utils } from '../../../common/utils';
import RouterButton from '../../../components/wrappers/router-button';
import RouterButtonDropdown from '../../../components/wrappers/router-button-dropdown';
import DocumentsTab from './documents-tab';
import OpenSearchWorkspaceSettings from './open-search-workspace-settings';
import { CHATBOT_NAME } from '../../../common/constants';
import { Workspace } from '../../../API';
import workspaceService from '../../../services/workspace.service';
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/redux/store/configureStore";

export default function WorkspacePane() {
    const appContext = useContext(AppContext);
    const navigate = useNavigate();
    const onFollow = useOnFollow();
    const { workspaceId } = useParams();
    const [searchParams, setSearchParams] = useSearchParams();
    const [activeTab, setActiveTab] = useState(
        searchParams.get('tab') || 'file',
    );
    const [loading, setLoading] = useState(true);
    const [workspace, setWorkspace] = useState<Workspace | undefined | null>(
        null,
    );
    const dataState = useSelector((state: RootState) => {
        return state.rootReducer;
      });
    const userRoles = dataState?.chatReducer?.userRoles;    
    const getWorkspace = useCallback(async () => {
        // if (!appContext || !workspaceId) return;

        // const apiClient = new ApiClient(appContext);
        try {
            const result = await workspaceService.getWorkspace(workspaceId);
            if (!result) {
                navigate('/rag/workspaces');
                return;
            }
            setWorkspace(result);
        } catch (error) {
            console.error(error);
        }
        setLoading(false);
    }, [appContext, navigate, workspaceId]);

    useEffect(() => {
        getWorkspace();
    }, [getWorkspace]);

    const showTabs = !workspace?.kendraIndexExternal;
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const disabledTabs =
        workspace?.engine === 'kendra' ? ['qna', 'website', 'rssfeed'] : [];

    return (
        <BaseAppLayout
            contentType="cards"
            breadcrumbs={
                !userRoles?.includes("READ_ONLY") && <BreadcrumbGroup
                    onFollow={onFollow}
                    items={[
                        {
                            text: CHATBOT_NAME,
                            href: '/',
                        },
                        {
                            text: 'RAG',
                            href: '/rag',
                        },
                        {
                            text: 'Document Libraries',
                            href: '/rag/workspaces',
                        },
                        {
                            text: workspace?.name || '',
                            href: `/rag/workspaces/${workspace?.workspaceId}`,
                        },
                    ]}
                />
            }
            content={
                <ContentLayout
                    header={
                         <Header
                            variant="h1"
                            actions={
                                !userRoles?.includes("READ_ONLY") && <SpaceBetween direction="horizontal" size="xs">
                                    <RouterButton
                                        href={`/rag/semantic-search?workspaceId=${workspaceId}`}
                                    >
                                        Semantic search
                                    </RouterButton>
                                    <RouterButtonDropdown
                                        items={[
                                            {
                                                id: 'upload-file',
                                                text: 'Upload files',
                                                href: `/rag/workspaces/add-data?tab=file&workspaceId=${workspaceId}`,
                                            },
                                            // {
                                            //     id: 'add-text',
                                            //     text: 'Add texts',
                                            //     href: `/rag/workspaces/add-data?tab=text&workspaceId=${workspaceId}`,
                                            // },
                                            // {
                                            //     id: 'add-qna',
                                            //     text: 'Add Q&A',
                                            //     href: `/rag/workspaces/add-data?tab=qna&workspaceId=${workspaceId}`,
                                            // },
                                            // {
                                            //     id: 'crawl-website',
                                            //     text: 'Crawl website',
                                            //     href: `/rag/workspaces/add-data?tab=website&workspaceId=${workspaceId}`,
                                            // },
                                            // {
                                            //     id: 'add-rss-subscription',
                                            //     text: 'Add RSS subscription',
                                            //     href: `/rag/workspaces/add-data?tab=rssfeed&workspaceId=${workspaceId}`,
                                            // },
                                        ]}
                                    >
                                        Add data
                                    </RouterButtonDropdown>
                                </SpaceBetween>
                            }
                        >
                            {loading ? (
                                <StatusIndicator type="loading">
                                    Loading...
                                </StatusIndicator>
                            ) : (
                                workspace?.name
                            )}
                        </Header>
                    }
                >
                    <SpaceBetween size="l">
                        {workspace && workspace.engine === 'opensearch' && !userRoles?.includes("READ_ONLY") &&  (
                            <OpenSearchWorkspaceSettings
                                workspace={workspace}
                            />
                        )}
                        {workspace?.kendraIndexExternal && (
                            <Flashbar
                                items={[
                                    {
                                        type: 'info',
                                        content: (
                                            <>
                                                Data upload is not available for
                                                external Kendra indexes
                                            </>
                                        ),
                                    },
                                ]}
                            />
                        )}
                        {workspace && showTabs && (
                            <Tabs
                                tabs={[
                                    {
                                        label: 'Files',
                                        id: 'file',
                                        content: (
                                            <DocumentsTab
                                                workspaceId={workspaceId}
                                                documentType="file"
                                            />
                                        ),
                                    },
                                   
                                ]}
                                activeTabId={activeTab}
                                onChange={({ detail: { activeTabId } }) => {
                                    setActiveTab(activeTabId);
                                    setSearchParams((current) => ({
                                        ...Utils.urlSearchParamsToRecord(
                                            current,
                                        ),
                                        tab: activeTabId,
                                    }));
                                }}
                            />
                        )}
                    </SpaceBetween>
                </ContentLayout>
            }
        />
    );
}

 // {
                                    //     label: 'Texts',
                                    //     id: 'text',
                                    //     content: (
                                    //         <DocumentsTab
                                    //             workspaceId={workspaceId}
                                    //             documentType="text"
                                    //         />
                                    //     ),
                                    // },
                                    // {
                                    //     label: 'Q&A',
                                    //     id: 'qna',
                                    //     disabled: disabledTabs.includes('qna'),
                                    //     content: (
                                    //         <DocumentsTab
                                    //             workspaceId={workspaceId}
                                    //             documentType="qna"
                                    //         />
                                    //     ),
                                    // },
                                    // {
                                    //     label: 'Websites',
                                    //     id: 'website',
                                    //     disabled:
                                    //         disabledTabs.includes('website'),
                                    //     content: (
                                    //         <DocumentsTab
                                    //             workspaceId={workspaceId}
                                    //             documentType="website"
                                    //         />
                                    //     ),
                                    // },
                                    // {
                                    //     label: 'RSS Feeds',
                                    //     id: 'rssfeed',
                                    //     disabled:
                                    //         disabledTabs.includes('rssfeed'),
                                    //     content: (
                                    //         <DocumentsTab
                                    //             workspaceId={workspaceId}
                                    //             documentType="rssfeed"
                                    //         />
                                    //     ),
                                    // },