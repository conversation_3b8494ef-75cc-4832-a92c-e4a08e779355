import {
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    Header<PERSON>,
    SpaceBetween,
} from '@cloudscape-design/components';
import RouterButton from '../../../components/wrappers/router-button';
import { useNavigate } from 'react-router-dom';
import { useContext, useState } from 'react';
import WorkspaceDeleteModal from '../../../components/rag/workspace-delete-modal';
import { AppContext } from '../../../common/app-context';
import { Workspace } from '../../../API';
import { Utils } from '../../../common/utils';
import workspaceService from '../../../services/workspace.service';
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/redux/store/configureStore";

interface WorkspacesPageHeaderProps extends HeaderProps {
    title?: string;
    createButtonText?: string;
    getWorkspaces: () => Promise<void>;
    selectedWorkspaces: readonly Workspace[];
}

export function WorkspacesPageHeader({
    title = 'Document Libraries',
    ...props
}: WorkspacesPageHeaderProps) {
    const navigate = useNavigate();
    const appContext = useContext(AppContext);
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const isOnlyOneSelected = props.selectedWorkspaces.length === 1;
    const canDeleteWorkspace =
        props.selectedWorkspaces.length === 1 &&
        (props.selectedWorkspaces[0].status == 'ready' ||
            props.selectedWorkspaces[0].status == 'submitted' ||
            props.selectedWorkspaces[0].status == 'error');

    const dataState = useSelector((state: RootState) => {
        return state.rootReducer;
    });
    const userRoles = dataState?.chatReducer?.userRoles;         

    const onRefreshClick = async () => {
        try {
            await props.getWorkspaces();
        } catch (error) {
            console.error(Utils.getErrorMessage(error));
        }
    };

    const onViewDetailsClick = () => {
        if (props.selectedWorkspaces.length === 0) return;

        navigate(`/rag/workspaces/${props.selectedWorkspaces[0].workspaceId}`);
    };

    const onDeleteClick = () => {
        setShowDeleteModal(true);
    };

    const onDeleteWorkspace = async () => {
        // if (!appContext) return;
        if (!isOnlyOneSelected) return;

        setShowDeleteModal(false);
        // const apiClient = new ApiClient(appContext);
        try {
            await workspaceService.deleteWorkspace(
                props.selectedWorkspaces[0].workspaceId,
            );

            setTimeout(async () => {
                await props.getWorkspaces();
            }, 1500);
        } catch (error) {
            console.error(Utils.getErrorMessage(error));
        }
    };

    return (
        <>
            <WorkspaceDeleteModal
                visible={showDeleteModal && canDeleteWorkspace}
                onDiscard={() => setShowDeleteModal(false)}
                onDelete={onDeleteWorkspace}
                workspace={props.selectedWorkspaces[0]}
            />
            <Header
                variant="awsui-h1-sticky"
                actions={
                    <SpaceBetween size="xs" direction="horizontal">
                        {!userRoles?.includes("READ_ONLY") &&<Button iconName="refresh" onClick={onRefreshClick} />}
                        {!userRoles?.includes("READ_ONLY") && 
                        <RouterButton
                            data-testid="header-btn-view-details"
                            disabled={!isOnlyOneSelected}
                            onClick={onViewDetailsClick}>View
                        </RouterButton>}
                        {!userRoles?.includes("READ_ONLY") && <RouterButton
                            data-testid="header-btn-view-details"
                            disabled={!canDeleteWorkspace}
                            onClick={onDeleteClick}
                        >
                            Delete
                        </RouterButton>}
                        {!userRoles?.includes("READ_ONLY") && <RouterButton
                            data-testid="header-btn-create"
                            variant="primary"
                            href="/rag/workspaces/create"
                        >
                            Create Library
                        </RouterButton>}
                    </SpaceBetween>
                }
                {...props}
            >
                {title}
            </Header>
        </>
    );
}
