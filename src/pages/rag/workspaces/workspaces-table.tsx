import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    PropertyFilter,
    SpaceBet<PERSON>en,
    Table,
} from '@cloudscape-design/components';
import { useCollection } from '@cloudscape-design/collection-hooks';
import { useCallback, useContext, useEffect, useRef, useState } from 'react';
import { TextHelper } from '../../../common/helpers/text-helper';
import { TableEmptyState } from '../../../components/table-empty-state';
import { TableNoMatchState } from '../../../components/table-no-match-state';
import { WorkspacesPageHeader } from './workspaces-page-header';
import { PropertyFilterI18nStrings } from '../../../common/i18n/property-filter-i18n-strings';
import {
    WorkspaceColumnFilteringProperties,
    WorkspacesColumnDefinitions,
} from './column-definitions';
import { AppContext } from '../../../common/app-context';
// import { ApiClient } from "../../../common/api-client/api-client";
import { Workspace } from '../../../API';
import { Utils } from '../../../common/utils';
import workspaceService from '../../../services/workspace.service';

export default function WorkspacesTable() {
    const appContext = useContext(AppContext);
    const [workspaces, setWorkspaces] = useState<Workspace[]>([]);
    const [loading, setLoading] = useState(true);
    const [loadError, setLoadError] = useState('');
    const workspacesLoaded = useRef(false);
    const {
        items,
        actions,
        filteredItemsCount,
        collectionProps,
        paginationProps,
        propertyFilterProps,
    } = useCollection(workspaces, {
        propertyFiltering: {
            filteringProperties: WorkspaceColumnFilteringProperties,
            empty: (
                <TableEmptyState
                    resourceName="Workspace"
                    createHref="/rag/workspaces/create"
                />
            ),
            noMatch: (
                <TableNoMatchState
                    onClearFilter={() => {
                        actions.setPropertyFiltering({
                            tokens: [],
                            operation: 'and',
                        });
                    }}
                />
            ),
        },
        pagination: { pageSize: 50 },
        sorting: {
            defaultState: {
                sortingColumn: WorkspacesColumnDefinitions[4],
                isDescending: true,
            },
        },
        selection: {},
    });

    const recursiveToCamel = (item: unknown): unknown => {
        if (Array.isArray(item)) {
            return item.map((el: unknown) => recursiveToCamel(el));
        } else if (typeof item === 'function' || item !== Object(item)) {
            return item;
        }
        return Object.fromEntries(
            Object.entries(item as Record<string, unknown>).map(
                ([key, value]: [string, unknown]) => [
                    key.replace(/([-_][a-z])/gi, (c) =>
                        c.toUpperCase().replace(/[-_]/g, ''),
                    ),
                    recursiveToCamel(value),
                ],
            ),
        );
    };

    const getWorkspaces = useCallback(async () => {
        if (!workspacesLoaded.current) {
            setLoading(true);
            workspacesLoaded.current = true;
            setLoadError('');
            try {
                const result = await workspaceService.getWorkspaces();
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                const resultCamelCase = result.map((workspace: any) =>
                    recursiveToCamel(workspace),
                );

                setWorkspaces(resultCamelCase);
            } catch (error) {
                if (error.response) {
                    setLoadError(error.response.data.detail);
                    if (error.response.data.error_code) {
                        console.log('Error code:', error.response.data.error_code);
                    }
                    if (error.response.data.detail) {
                        console.log('Error message:', error.response.data.detail);
                    }
                    // console.error(Utils.getErrorMessage(error));
                }
            } finally {
                setLoading(false);
            }
        }
    }, [appContext]);

    useEffect(() => {
        // if (!appContext) return;

        getWorkspaces();
    }, [appContext, getWorkspaces]);

    return (
        <Table
            {...collectionProps}
            items={items}
            columnDefinitions={WorkspacesColumnDefinitions}
            selectionType="single"
            variant="full-page"
            stickyHeader={true}
            resizableColumns={true}
            header={
                <SpaceBetween size="xs">
                    {loadError.length > 0 && (
                        <Alert
                            statusIconAriaLabel="Error"
                            type="error"
                            header="Error"
                        >
                            {loadError}
                        </Alert>
                    )}
                    <WorkspacesPageHeader
                        selectedWorkspaces={collectionProps.selectedItems ?? []}
                        getWorkspaces={getWorkspaces}
                        counter={
                            loading
                                ? undefined
                                : TextHelper.getHeaderCounterText(
                                    workspaces,
                                    collectionProps.selectedItems,
                                )
                        }
                    />
                </SpaceBetween>
            }
            loading={loading}
            loadingText="Loading Document Libraries"
            filter={
                <PropertyFilter
                    {...propertyFilterProps}
                    i18nStrings={PropertyFilterI18nStrings}
                    filteringPlaceholder={'Filter Libraries'}
                    countText={TextHelper.getTextFilterCounterText(
                        filteredItemsCount,
                    )}
                    expandToViewport={true}
                />
            }
            pagination={<Pagination {...paginationProps} />}
        />
    );
}
