import { BreadcrumbGroup } from '@cloudscape-design/components';
import useOnFollow from '../../../common/hooks/use-on-follow';
import WorkspacesTable from './workspaces-table';
import BaseAppLayout from '../../../components/base-app-layout';
import { CHATBOT_NAME } from '../../../common/constants';
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/redux/store/configureStore";

export default function Workspaces() {
    const onFollow = useOnFollow();

    const dataState = useSelector((state: RootState) => {
        return state.rootReducer;
    });
    const userRoles = dataState?.chatReducer?.userRoles; 

    return (
        <BaseAppLayout
            contentType="table"
            breadcrumbs={
                !userRoles?.includes("READ_ONLY") && <BreadcrumbGroup
                    onFollow={onFollow}
                    items={[
                        {
                            text: CHATBOT_NAME,
                            href: '/',
                        },
                        {
                            text: 'RAG',
                            href: '/rag',
                        },
                        {
                            text: 'Document Libraries',
                            href: '/rag/workspaces',
                        },
                    ]}
                />
            }
            content={<WorkspacesTable />}
        />
    );
}
