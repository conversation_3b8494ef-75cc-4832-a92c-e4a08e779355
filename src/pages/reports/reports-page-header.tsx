import { Head<PERSON>, HeaderProps } from "@cloudscape-design/components";

interface ReportsPageHeaderProps extends HeaderProps {
  title?: string;
}

export function ReportsPageHeader({
  title = "Reports",
  ...props
}: ReportsPageHeaderProps) {
  return (
    <Header
      variant="awsui-h1-sticky"
      {...props}
      description="Export various reports to csv file."
    >
      {title}
    </Header>
  );
}
