import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>er,
  <PERSON>ton,
  SpaceBetween,
} from "@cloudscape-design/components";
import { ReportsPageHeader } from "./reports-page-header";
import { AppContext } from "../../common/app-context";
import { useContext, useEffect, useState } from "react";
import useOnFollow from "../../common/hooks/use-on-follow";
import BaseAppLayout from "../../components/base-app-layout";
import { CHATBOT_NAME } from "../../common/constants";
import { ReportsCard } from "../../API";
import feedbackService from "../../services/feedback.service";
import axios from "axios";

export default function Reports() {
  const onFollow = useOnFollow();
  const appContext = useContext(AppContext);
  const [data, setData] = useState<ReportsCard[]>([]);
  const [loading, setLoading] = useState(true);

  const CARD_DEFINITIONS = {
    header: (item: ReportsCard) => (
      <div>
        <Header>{item.name}</Header>
      </div>
    ),
    sections: [
      {
        id: "state",
        header: "",
        content: (item: ReportsCard) => (
          <>
            <p>{item.description}</p>
            <SpaceBetween direction="horizontal" size="xs">
              <Button variant="primary" onClick={exportReport(item, false)}>
                Export to CSV
              </Button>
              <Button variant="primary" onClick={exportReport(item, true)}>
                Export to CSV formatted
              </Button>
            </SpaceBetween>
          </>

          // <StatusIndicator type={item.enabled ? "success" : "stopped"}>
          //   {item.enabled ? "Enabled" : "Disabled"}
          // </StatusIndicator>
        ),
      },
    ],
  };

  const reportCardItems: ReportsCard[] = [
    {
      id: "1",
      name: "Feedback",
      description: "Export feedback to csv",
      enabled: true,
    },
  ];

  const exportReport =
    (item: ReportsCard, isFormatted: boolean) => async () => {
      if (item.id === "1") {
        const response = await feedbackService.exportcsv(isFormatted);
        if (response.url) {
          axios
            .get(response.url, {
              responseType: "blob",
            })
            .then(async (response) => {
              const file = new Blob([response.data], {
                type: "application/csv",
              });
              const fileURL = URL.createObjectURL(file);
              const link = document.createElement("a");
              link.href = fileURL;
              link.setAttribute("download", "feedback.csv");
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link);
            })
            .catch((error) => {
              console.error(error);
            });
        }
      }
    };

  useEffect(() => {
    (async () => {
      try {
        setData(reportCardItems);
      } catch (error) {
        console.error(error);
      }

      setLoading(false);
    })();
  }, [appContext]);

  return (
    <BaseAppLayout
      contentType="cards"
      breadcrumbs={
        <BreadcrumbGroup
          onFollow={onFollow}
          items={[
            {
              text: CHATBOT_NAME,
              href: "/",
            },
            {
              text: "Reports",
              href: "#",
            },
          ]}
        />
      }
      content={
        <Cards
          stickyHeader={true}
          cardDefinition={CARD_DEFINITIONS}
          loading={loading}
          loadingText="Loading Reports"
          items={data || []}
          variant="full-page"
          header={<ReportsPageHeader />}
        />
      }
    />
  );
}
