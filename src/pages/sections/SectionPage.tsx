import React, { useEffect } from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>B<PERSON><PERSON><PERSON>,
  <PERSON>,
} from "@cloudscape-design/components";
import { useLocation } from "react-router-dom";
import BaseAppLayout from "../../components/base-app-layout";
import PartiesToAgreement1_1Page from "./PartiesToAgreement1_1Page";
import PeriodOfAgreement1_2Page from "./PeriodOfAgreement1_2Page";
import ScopeOfAgreement1_3Page from "./ScopeOfAgreement1_3Page";
import AuthorityToShareData1_4Page from "./AuthorityToShareData1_4Page";
import Modification1_5Page from "./Modification1_5Page";
import Termination1_6Page from "./Termination1_6Page";
import Acknowledgements1_9Page from "./Acknowledgements1_9Page";
import RolesAndResponsibilities1_10Page from "./RolesAndResponsibilities1_10Page";
import Funding1_11Page from "./Funding1_11Page";

const SectionPage: React.FC = () => {
  const location = useLocation();

  // Handle scrolling to specific section based on hash
  useEffect(() => {
    if (location.hash) {
      const elementId = location.hash.substring(1); // Remove the # from hash
      const element = document.getElementById(elementId);
      if (element) {
        // Fixed header height based on which section is clicked
        const fixedHeaderHeight =
          elementId === "parties-to-agreement" ? 127 : 49;

        // Fixed offset for showing previous section
        const previousSectionOffset = 20;

        // Special handling for the first section
        if (elementId === "parties-to-agreement") {
          // First section only needs to account for fixed header
          const yOffset = -(fixedHeaderHeight + 20); // Add some padding
          const y =
            element.getBoundingClientRect().top + window.pageYOffset + yOffset;
          window.scrollTo({ top: y, behavior: "smooth" });
        } else {
          // For all other sections, account for both fixed header AND show previous section border
          const yOffset = -(fixedHeaderHeight + previousSectionOffset);
          const y =
            element.getBoundingClientRect().top + window.pageYOffset + yOffset;
          window.scrollTo({ top: y, behavior: "smooth" });
        }
      }
    }
  }, [location.hash]);

  const sectionStyle = {
    border: "1px solid #e1e4e8",
    borderRadius: "8px",
    padding: "8px",
    marginBottom: "8px",
    backgroundColor: "#ffffff",
  };

  const pageStyle = {
    fontSize: "14px",
    lineHeight: "1.2",
  };

  const content = (
    // ----ensure scrollability----
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        gap: "12px",
        padding: "16px",
        paddingBottom: "13000px", // ----Add extra bottom padding to ensure scrollability----
      }}
    >
      <Header variant="h1" description="">
        <span style={{ fontSize: "24px", lineHeight: "1.2", margin: "0" }}>
          Generate a Data Use Agreement
        </span>
      </Header>

      {/* Section 1.1 - Parties to Agreement */}
      <div id="parties-to-agreement">
        <PartiesToAgreement1_1Page />
      </div>

      {/* Section 1.2 - Period of Agreement */}
      <div id="period-of-agreement">
        <PeriodOfAgreement1_2Page />
      </div>

      {/* Section 1.3 - Scope of Agreement */}
      <div id="scope-of-agreement">
        <ScopeOfAgreement1_3Page />
      </div>

      {/* Section 1.4 - Authority to Share Data */}
      <div id="authority-to-share-data">
        <AuthorityToShareData1_4Page />
      </div>

      {/* Section 1.5 - Modification */}
      <div id="modification">
        <Modification1_5Page />
      </div>

      {/* Section 1.6 - Termination */}
      <div id="termination">
        <Termination1_6Page />
      </div>

      {/* Section 1.7 - Violation of Terms */}
      <div id="violation-of-terms" style={sectionStyle}>
        <Header variant="h3">
          <span style={{ fontSize: "15px", lineHeight: "1.2", margin: "0" }}>
            Section 1.7 - Violation of Terms
          </span>
        </Header>
        <p style={{ fontSize: "14px", lineHeight: "1.2", margin: "4px 0" }}></p>
      </div>

      {/* Section 1.8 - Indemnification */}
      <div id="indemnification" style={sectionStyle}>
        <Header variant="h3">
          <span style={{ fontSize: "15px", lineHeight: "1.2", margin: "0" }}>
            Section 1.8 - Indemnification
          </span>
        </Header>
        <p style={{ fontSize: "14px", lineHeight: "1.2", margin: "4px 0" }}></p>
      </div>

      {/* Section 1.9 - Acknowledgements */}
      <div id="acknowledgements">
        <Acknowledgements1_9Page />
      </div>

      {/* Section 1.10 - Roles and Responsibilities */}
      <div id="roles-and-responsibilities">
        <RolesAndResponsibilities1_10Page />
      </div>

      {/* Section 1.11 - Funding */}
      <div id="funding">
        <Funding1_11Page />
      </div>

      {/* Section 1.12 - Others */}
      <div id="others" style={sectionStyle}>
        <Header variant="h3">
          <span style={{ fontSize: "15px", lineHeight: "1.2", margin: "0" }}>
            Section 1.12 - Others
          </span>
        </Header>
        <p style={{ fontSize: "14px", lineHeight: "1.2", margin: "4px 0" }}></p>
      </div>

      {/* Section 2.0 - Purpose/Use case */}
      <div id="purpose-use-case" style={sectionStyle}>
        <Header variant="h3">
          <span style={{ fontSize: "15px", lineHeight: "1.2", margin: "0" }}>
            Section 2.0 - Purpose/Use case
          </span>
        </Header>
        <p style={{ fontSize: "14px", lineHeight: "1.2", margin: "4px 0" }}></p>
      </div>
      
      {/* Section 3.1 - Data Description */}
      <div id="data-description" style={sectionStyle}>
        <Header variant="h3">
          <span style={{ fontSize: "15px", lineHeight: "1.2", margin: "0" }}>
            Section 3.1 - Data Description
          </span>
        </Header>
        <p style={{ fontSize: "14px", lineHeight: "1.2", margin: "4px 0" }}></p>
      </div>

      {/* Section 3.2 - Data Ownership */}
      <div id="data-ownership" style={sectionStyle}>
        <Header variant="h3">
          <span style={{ fontSize: "15px", lineHeight: "1.2", margin: "0" }}>
            Section 3.2 - Data Ownership
          </span>
        </Header>
        <p style={{ fontSize: "14px", lineHeight: "1.2", margin: "4px 0" }}></p>
      </div>

      {/* Section 3.3 - Service Level */}
      <div id="service-level" style={sectionStyle}>
        <Header variant="h3">
          <span style={{ fontSize: "15px", lineHeight: "1.2", margin: "0" }}>
            Section 3.3 - Service Level
          </span>
        </Header>
        <p style={{ fontSize: "14px", lineHeight: "1.2", margin: "4px 0" }}></p>
      </div>

      {/* Section 3.3.1 - Data Quality */}
      <div id="data-quality" style={sectionStyle}>
        <Header variant="h3">
          <span style={{ fontSize: "15px", lineHeight: "1.2", margin: "0" }}>
            Section 3.3.1 - Data Quality
          </span>
        </Header>
        <p style={{ fontSize: "14px", lineHeight: "1.2", margin: "4px 0" }}></p>
      </div>
      {/* Section 4.1 Disclosure and Use */}
      <div id="disclosure-and-use" style={sectionStyle}>
        <Header variant="h3">
          <span style={{ fontSize: "15px", lineHeight: "1.2", margin: "0" }}>
            Section 4.1 - Disclosure and Use
          </span>
        </Header>
        <p style={{ fontSize: "14px", lineHeight: "1.2", margin: "4px 0" }}></p>
      </div>
      {/* Section 4.2 - Control of Identifiable Data */}
      <div id="control-of-identifiable-data" style={sectionStyle}>
        <Header variant="h3">
          <span style={{ fontSize: "15px", lineHeight: "1.2", margin: "0" }}>
            Section 4.2 - Control of Identifiable data
          </span>
        </Header>
        <p style={{ fontSize: "14px", lineHeight: "1.2", margin: "4px 0" }}></p>
      </div>
      {/* Section 4.3 - Data Deidentification */}
      <div id="data-deidentification" style={sectionStyle}>
        <Header variant="h3">
          <span style={{ fontSize: "15px", lineHeight: "1.2", margin: "0" }}>
            Section 4.3 - Data Deidentification
          </span>
        </Header>
        <p style={{ fontSize: "14px", lineHeight: "1.2", margin: "4px 0" }}></p>
      </div>
      {/* Section 4.3.1 Person */}
      <div id="person" style={sectionStyle}>
        <Header variant="h3">
          <span style={{ fontSize: "15px", lineHeight: "1.2", margin: "0" }}>
            Section 4.3.1 - Person
          </span>
        </Header>
        <p style={{ fontSize: "14px", lineHeight: "1.2", margin: "4px 0" }}></p>
      </div>
      {/* Section 4.3.2 - Organization */}
      <div id="organization" style={sectionStyle}>
        <Header variant="h3">
          <span style={{ fontSize: "15px", lineHeight: "1.2", margin: "0" }}>
            Section 4.3.2 - Organization
          </span>
        </Header>
        <p style={{ fontSize: "14px", lineHeight: "1.2", margin: "4px 0" }}></p>
      </div>
      {/* Section 4.4 - Data Sharing */}
      <div id="data-sharing" style={sectionStyle}>
        <Header variant="h3">
          <span style={{ fontSize: "15px", lineHeight: "1.2", margin: "0" }}>
            Section 4.4 - Data Sharing
          </span>
        </Header>
        <p style={{ fontSize: "14px", lineHeight: "1.2", margin: "4px 0" }}></p>
      </div>
      {/* Section 4.4.1 - Data Linkage */}
      <div id="data-linkage" style={sectionStyle}>
        <Header variant="h3">
          <span style={{ fontSize: "15px", lineHeight: "1.2", margin: "0" }}>
            Section 4.4.1 - Data Linkage
          </span>
        </Header>
        <p style={{ fontSize: "14px", lineHeight: "1.2", margin: "4px 0" }}></p>
      </div>
      {/* Section 4.4.2 - Data Reuse */}
      <div id="data-reuse" style={sectionStyle}>
        <Header variant="h3">
          <span style={{ fontSize: "15px", lineHeight: "1.2", margin: "0" }}>
            Section 4.4.2 - Data Reuse
          </span>
        </Header>
        <p style={{ fontSize: "14px", lineHeight: "1.2", margin: "4px 0" }}></p>
      </div>
      {/* Section 4.5 - Data Redisclosure */}
      <div id="data-redisclosure" style={sectionStyle}>
        <Header variant="h3">
          <span style={{ fontSize: "15px", lineHeight: "1.2", margin: "0" }}>
            Section 4.5 - Data Redisclosure
          </span>
        </Header>
        <p style={{ fontSize: "14px", lineHeight: "1.2", margin: "4px 0" }}></p>
      </div>
      {/* Section 4.6 - Publication */}
      <div id="publication" style={sectionStyle}>
        <Header variant="h3">
          <span style={{ fontSize: "15px", lineHeight: "1.2", margin: "0" }}>
            Section 4.6 - Publication
          </span>
        </Header>
        <p style={{ fontSize: "14px", lineHeight: "1.2", margin: "4px 0" }}></p>
      </div>
      {/* Section 5.1 - General */}
      <div id="general" style={sectionStyle}>
        <Header variant="h3">
          <span style={{ fontSize: "15px", lineHeight: "1.2", margin: "0" }}>
            Section 5.1 - General
          </span>
        </Header>
        <p style={{ fontSize: "14px", lineHeight: "1.2", margin: "4px 0" }}></p>
      </div>
      {/* Section 5.2 - Data Access Controls */}
      <div id="data-access-controls" style={sectionStyle}>
        <Header variant="h3">
          <span style={{ fontSize: "15px", lineHeight: "1.2", margin: "0" }}>
            Section 5.2 - Data Access Controls
          </span>
        </Header>
        <p style={{ fontSize: "14px", lineHeight: "1.2", margin: "4px 0" }}></p>
      </div>
      {/* Section 5.3 - Network Access */}
      <div id="network-access" style={sectionStyle}>
        <Header variant="h3">
          <span style={{ fontSize: "15px", lineHeight: "1.2", margin: "0" }}>
            Section 5.3 - Network Access
          </span>
        </Header>
        <p style={{ fontSize: "14px", lineHeight: "1.2", margin: "4px 0" }}></p>
      </div>
      {/* Section 5.4 - Physical Access */}
      <div id="physical-access" style={sectionStyle}>
        <Header variant="h3">
          <span style={{ fontSize: "15px", lineHeight: "1.2", margin: "0" }}>
            Section 5.4 - Physical Access
          </span>
        </Header>
        <p style={{ fontSize: "14px", lineHeight: "1.2", margin: "4px 0" }}></p>
      </div>
      {/* Section 5.5 - Transmission and Storage */}
      <div id="transmission-and-storage" style={sectionStyle}>
        <Header variant="h3">
          <span style={{ fontSize: "15px", lineHeight: "1.2", margin: "0" }}>
            Section 5.5 - Transmission and Storage
          </span>
        </Header>
        <p style={{ fontSize: "14px", lineHeight: "1.2", margin: "4px 0" }}></p>
      </div>
      {/* Section 5.7 - Cloud Computing (where applicable) */}
      <div id="cloud-computing" style={sectionStyle}>
        <Header variant="h3">
          <span style={{ fontSize: "15px", lineHeight: "1.2", margin: "0" }}>
            Section 5.7 - Cloud Computing (where applicable)
          </span>
        </Header>
        <p style={{ fontSize: "14px", lineHeight: "1.2", margin: "4px 0" }}></p>
      </div>
      {/* Section 5.8 - Incidents Including Reporting */}
      <div id="incidents-including-reporting" style={sectionStyle}>
        <Header variant="h3">
          <span style={{ fontSize: "15px", lineHeight: "1.2", margin: "0" }}>
            Section 5.8 - Incidents Including Reporting
          </span>
        </Header>
        <p style={{ fontSize: "14px", lineHeight: "1.2", margin: "4px 0" }}></p>
      </div>
      {/* Section 5.9 - Audit */}
      <div id="audit" style={sectionStyle}>
        <Header variant="h3">
          <span style={{ fontSize: "15px", lineHeight: "1.2", margin: "0" }}>
            Section 5.9 - Audit
          </span>
        </Header>
        <p style={{ fontSize: "14px", lineHeight: "1.2", margin: "4px 0" }}></p>
      </div>
      {/* Section 6.0 - Reporting Requirements */}
      <div id="reporting-requirements" style={sectionStyle}>
        <Header variant="h3">
          <span style={{ fontSize: "15px", lineHeight: "1.2", margin: "0" }}>
            Section 6.0 - Reporting Requirements
          </span>
        </Header>
        <p style={{ fontSize: "14px", lineHeight: "1.2", margin: "4px 0" }}></p>
      </div>
      {/* Section 7.0 - Applicable Laws and Regulations */}
      <div id="applicable-laws-and-regulations" style={sectionStyle}>
        <Header variant="h3">
          <span style={{ fontSize: "15px", lineHeight: "1.2", margin: "0" }}>
            Section 7.0 - Applicable Laws and Regulations
          </span>
        </Header>
        <p style={{ fontSize: "14px", lineHeight: "1.2", margin: "4px 0" }}></p>
      </div>
      {/* Section 8.0 - Signatures */}
      <div id="signatures" style={sectionStyle}>
        <Header variant="h3">
          <span style={{ fontSize: "15px", lineHeight: "1.2", margin: "0" }}>
            Section 8.0 - Signatures
          </span>
        </Header>
        <p style={{ fontSize: "14px", lineHeight: "1.2", margin: "4px 0" }}></p>
      </div>
    </div>
  );

  return (
    <div style={pageStyle}>
      <BaseAppLayout content={content} />
    </div>
  );
};

export default SectionPage;