import {
    ContentLayout,
    <PERSON><PERSON>,
    <PERSON>s,
    SpaceBetween,
    Link,
    BreadcrumbGroup,
} from '@cloudscape-design/components';
import BaseAppLayout from '../components/base-app-layout';
import useOnFollow from '../common/hooks/use-on-follow';

export default function Welcome() {
  const onFollow = useOnFollow();

  return (
    <BaseAppLayout
      breadcrumbs={
        <BreadcrumbGroup
          onFollow={onFollow}
          items={[
            {
              text: 'IntelliReview',
              href: "/",
            },
          ]}
        />
      }
      content={
        <ContentLayout
          header={
            <Header
              variant="h1"
              description="Policy Bot summary here"
            >
              Policy Bot
            </Header>
          }
        >
          <SpaceBetween size="l">
            <Cards
              cardDefinition={{
                header: (item) => (
                  <Link
                    external={item.external}
                    href={item.href}
                    fontSize="heading-m"
                  >
                    {item.name}
                  </Link>
                ),
                sections: [
                  {
                    content: (item) => (
                      <div>
                        <img
                          src={item.img}
                          alt="Placeholder"
                          style={{ width: "100%" }}
                        />
                      </div>
                    ),
                  },
                  {
                    content: (item) => (
                      <div>
                        <div>{item.description}</div>
                      </div>
                    ),
                  },
                  {
                    id: "type",
                    header: "Type",
                    content: (item) => item.type,
                  },
                ],
              }}
              cardsPerRow={[{ cards: 1 }, { minWidth: 700, cards: 2 }]}
              items={[
                {
                  name: "Amazon Bedrock",
                  external: true,
                  type: "AWS Fully Managed",
                  href: "https://aws.amazon.com/bedrock/",
                  img: "/images/welcome/amazon-bedrock.png",
                  description:
                    "Amazon Bedrock is a fully managed service that makes foundation models (FMs) from Amazon and leading AI startups available through an API.",
                },
                {
                  name: "CFG AI Hosted Models",
                  type: "CFG AI Hosted",
                  href: "#",
                  img: "/images/welcome/cfgai.png",
                  description:
                    "Interface with CFG AI Hosted Models, Databases, Vector Engines and Storage Options.",
                },  
              ]}
            />
          </SpaceBetween>
        </ContentLayout>
      }
    ></BaseAppLayout>
  );
}
