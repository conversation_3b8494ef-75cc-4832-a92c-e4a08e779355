import React, { useState } from 'react';
import BaseAppLayout from '../../components/base-app-layout';
import Sessions from '../../components/chatbot/sessions';
import { useParams } from 'react-router-dom';
import { BreadcrumbGroup } from '@cloudscape-design/components';
import { CHATBOT_NAME } from '../../common/constants';
import useOnFollow from '../../common/hooks/use-on-follow';
import { WorkspaceHub } from '../../components/workspaces/workspace-hub';

export default function WorkspaceHubPage() {
    useParams();
    const [toolsOpen, setToolsOpen] = useState(false);
    const onFollow = useOnFollow();

    return (
        <BaseAppLayout
            toolsHide={false}
            toolsOpen={toolsOpen}
            breadcrumbs={
                <BreadcrumbGroup
                    onFollow={onFollow}
                    items={[
                        {
                            text: CHATBOT_NAME,
                            href: '/',
                        },
                        {
                            text: 'Prompts',
                            href: '/chatbot/prompts',
                        },
                    ]}
                />
            }
            onToolsChange={({ detail }) => setToolsOpen(detail.open)}
            tools={<Sessions toolsOpen={toolsOpen} />}
            toolsWidth={500}
            content={<WorkspaceHub />}
        />
    );
}
