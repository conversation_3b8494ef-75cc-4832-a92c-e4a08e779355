import { combineReducers, Action } from '@reduxjs/toolkit';
import chatReducer, { intialChatStateProps } from '../../pages/chatbot/playground/chat.slice';
const appReducer = combineReducers({
    chatReducer,
});

const rootReducer = (state: AppState | undefined, action: Action): AppState => {
    return appReducer(state, action);
  };
  
export default rootReducer;

interface AppState {
    chatReducer: intialChatStateProps;
  }