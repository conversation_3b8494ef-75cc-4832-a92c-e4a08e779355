import { ChatRequestInput } from '../common/types';
import { ChatBotMessageResponse } from '../components/chatbot/types';
import { chatAPI } from '../configs/api';


class ChatService {
    /**
     * Timesout after 2 minutes
     * @param payload 
     * @returns Response from the LLM
     */
    sendQuery(payload: ChatRequestInput): Promise<ChatBotMessageResponse>{

        const timeoutPromise = new Promise<ChatBotMessageResponse>((_, reject) =>{
            setTimeout(() => {
                reject(new Error("Request timed out after 4 minutes"));
            }, 240000);
        })

        const apiCallPromise = chatAPI.post<ChatBotMessageResponse>('', payload).then((response) => response.data);
     
        return Promise.race([apiCallPromise, timeoutPromise])
    }
}

export default new ChatService();
