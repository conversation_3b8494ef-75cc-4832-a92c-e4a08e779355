/* eslint-disable @typescript-eslint/no-explicit-any */
import {
    FileDownloadPresignedUrlRequest,
    FileUploadPresignedUrlRequest,
    WorkspaceFileUploadPresignedUrlRequest,
    WorkspaceFileDownloadPresignedUrlRequest,
    UploadFileInfo
} from '../API';
import { documentsAPI } from '../configs/api';

/**
 * The DocumentService is used to upload and download files from the workspace
 */
class DocumentService {
    /**
     * 
     * @param payload 
     * @returns 
     */
    presignedWorkspaceFileUploadPost(
        payload: WorkspaceFileUploadPresignedUrlRequest,
    ) {
        return documentsAPI
            .post<any>('/workspace/upload/presigned', payload)
            .then((response) => response.data);
    }

    /**
     * 
     * @param payload 
     * @returns 
     */
    presignedWorkspaceFileDownloadPost(
        payload: WorkspaceFileDownloadPresignedUrlRequest,
    ) {
        return documentsAPI
            .post<any>('/workspace/download/presigned', payload)
            .then((response) => response.data);
    }

    /**
     * 
     * @param payload 
     * @returns 
     */
    presignedFileUploadPost(payload: FileUploadPresignedUrlRequest) {
        return documentsAPI
            .post<any>('/upload/presigned', payload)
            .then((response) => response.data);
    }

        /**
     * 
     * @param payload 
     * @returns 
     */
        validateContextLength(files: UploadFileInfo[], model_id) {
            return documentsAPI
                .post<any>('/validate_context_length', { files, model_id })
                .then((response) => response.data);
        }

    /**
     * 
     * @param payload 
     * @returns 
     */
    presignedFileDownloadPost(payload: FileDownloadPresignedUrlRequest) {
        return documentsAPI
            .post<any>('/download/presigned', payload)
            .then((response) => response.data);
    }

    /**
     * 
     * @param payload 
     * @returns 
     */
    getDocuments(payload: any) {
        return documentsAPI
            .post<any>('/list', payload)
            .then((response) => response.data);
    }

    /**
     * 
     * @param payload 
     * @returns 
     */
    addWebsiteDocument(payload: any) {
        return documentsAPI
            .post<any>('/website', payload)
            .then((response) => response.data);
    }

    /**
     * 
     * @param payload 
     * @returns 
     */
    addFileDocument(payload: any) {
        return documentsAPI
            .post<any>('/add/file', payload)
            .then((response) => response.data);
    }
}

export default new DocumentService();
