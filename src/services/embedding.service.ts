/* eslint-disable @typescript-eslint/no-explicit-any */
import { EmbeddingModel } from '../API';
import { embeddingsAPI } from '../configs/api';

/**
 * The EmbeddingService is used to interact with the embeddings API
 */
class EmbeddingService {
    /**
     * 
     * @returns 
     */
    getModels() {
        return embeddingsAPI
            .get<Array<EmbeddingModel>>('')
            .then((response) => response.data);
    }

    /**
     * 
     * @param payload 
     * @returns 
     */
    calculateEmbeddings(payload: any) {
        return embeddingsAPI
            .post<any>('/generate', payload)
            .then((response) => response.data);
    }
}

export default new EmbeddingService();
