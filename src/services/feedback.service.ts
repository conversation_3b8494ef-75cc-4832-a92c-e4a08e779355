import { FeedbackData } from '../components/chatbot/types';
import { feedbackAPI } from '../configs/api';

/**
 * The FeedbackService is used to interact with the feedback API
 */
class FeedbackService {
    /**
     * 
     * @param feedbackData 
     * @returns 
     */
    saveFeedback(feedbackData: FeedbackData) {
        return feedbackAPI
            .post<any>('', feedbackData)
            .then((response) => response.data);
    }

    /**
     * 
     * @returns 
     */
    exportcsv(isFormatted: boolean) {
        return feedbackAPI
            .post<any>('/export/csv', { isFormatted: isFormatted })
            .then((response) => response.data);
    }
}

export default new FeedbackService();
