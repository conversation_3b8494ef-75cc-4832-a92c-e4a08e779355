import { Model } from "../API";
import { coreModelsAPI } from "../configs/api";

/**
 * The ModelService is used to interact with the models API
 */
class ModelService {
  /**
   *
   * @returns
   */
  getModels() {
    return coreModelsAPI
      .get<Array<Model>>("")
      .then((response) => response.data);
  }

  /**
   *
   * @returns
   */
  getCFGModels() {
    return coreModelsAPI
      .get<Array<Model>>("/cfgai")
      .then((response) => response.data);
  }
}

export default new ModelService();
