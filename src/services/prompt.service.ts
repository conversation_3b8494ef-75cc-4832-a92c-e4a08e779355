import { Prompt } from "../API";
import { corePromptsAPI } from "../configs/api";

/**
 * The PromptService is used to interact with the prompts API
 */
class PromptService {
  /**
   *
   * @returns
   */
  getPrompts() {
    return corePromptsAPI.get<any>("").then((response) => response.data.data);
  }

  /**
   *
   * @param promptName
   * @returns
   */
  getPromptByName(promptName: string) {
    return corePromptsAPI
      .get<any>("/" + promptName)
      .then((response) => response.data.data);
  }

  /**
   *
   * @param prompt_id
   * @returns
   */
  getPromptById(prompt_id: string) {
    return corePromptsAPI
      .get<any>("/id/" + prompt_id)
      .then((response) => response.data);
  }

  /**
   *
   * @param prompt_id
   * @returns
   */
  deletePromptById(prompt_id: string) {
    return corePromptsAPI
      .delete<any>("/" + prompt_id)
      .then((response) => response.data);
  }

  /**
   *
   * @param payload
   * @returns
   */
  createPrompt(payload: Prompt) {
    return corePromptsAPI
      .post<Prompt>("", payload)
      .then((response) => response.data);
  }

  /**
   *
   * @param payload
   * @returns
   */
  updatePrompt(payload: Prompt) {
    return corePromptsAPI
      .put<Prompt>("", payload)
      .then((response) => response.data);
  }

  /**
   *
   * @param promptId
   * @param promptTemplate
   * @returns
   */
  updatePromptTemplate(promptId: string, promptTemplate: string) {
    const payload = { id: promptId, prompt: promptTemplate };
    return corePromptsAPI
      .post<any>("/template", payload)
      .then((response) => response.data);
  }
}

export default new PromptService();
