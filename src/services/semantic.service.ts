import { semanticAPI, semanticBedrockAPI } from '../configs/api'

class SemanticService {
  runQuery(payload: any) {
    return semanticAPI.post<any>('/query', payload).then(response => response.data)
  }

  getText2SQLResponseFromBedrock(provider: string, modelName: string, msg: string) {
    const payload = { provider: provider, modelName: modelName, inputText: msg };
    return semanticBedrockAPI.post<any>('/northwind', payload).then(response => response.data)
  }
}

export default new SemanticService()
