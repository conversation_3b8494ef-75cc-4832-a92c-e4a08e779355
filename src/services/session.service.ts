import { coreSessionsAPI } from "../configs/api";

/**
 * The SessionService is used to interact with the sessions API
 */
class SessionService {
  /**
   * Get a session by passing userId and sessionId
   * @param sessionId
   * @param userId
   * @returns
   */
  getSession(sessionId: string, userId: string) {
    const payload = { sessionId: sessionId, userId: userId };
    return coreSessionsAPI
      .post<any>("", payload)
      .then((response) => response.data);
  }

  /**
   * Retrieve all sessions for a user
   * @param userId
   * @returns
   */
  listSessions(userId: string) {
    return coreSessionsAPI
      .get<any>("/" + userId)
      .then((response) => response.data);
  }

  /**
   * Delete a session by passing userId and sessionId
   * @param sessionId
   * @param userId
   * @returns
   */
  deleteSession(sessionId: string, userId: string) {
    const payload = { sessionId: sessionId, userId: userId };
    return coreSessionsAPI
      .post<any>("/delete", payload)
      .then((response) => response.data);
  }

  /**
   * Delete all user sessions for this user
   * @param userId
   * @returns
   */
  deleteSessions(userId: string) {
    return coreSessionsAPI
      .delete<any>("/all/" + userId)
      .then((response) => response.data);
  }

  /**
   * Export all sessions to a CSV file
   * @returns
   */
  exportcsv() {
    return coreSessionsAPI
      .get<any>("/export/csv")
      .then((response) => response.data);
  }

  /**
   * Export all sessions to a CSV file
   * @returns
   */
  exportallcsv() {
    return coreSessionsAPI
      .get<any>("/export/all/csv")
      .then((response) => response.data);
  }
}

export default new SessionService();
