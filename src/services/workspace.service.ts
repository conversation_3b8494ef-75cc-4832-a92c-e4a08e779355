/* eslint-disable @typescript-eslint/no-explicit-any */
import { workspaceAPI } from '../configs/api';

class WorkspaceService {
    /**
     * Return list of workspaces created outside of CFG AI
     * @returns Workspaces
     */
    getWorkspaces() {
        return workspaceAPI.get<any>('').then((response) => response.data
        //filter out byod workspaces from result
        .filter(workspace => !workspace.name.startsWith('byod')));
    }

    /**
     * Return list of workspaces i.e. vectorstore collections created in CFG AI
     * @returns Workspaces
     */
    getCFGWorkspaces() {
        return workspaceAPI
            .get<any>('/cfgai')
            .then((response) => response.data);
    }

    /**
     * Get workspace details by id
     * @param workspaceId
     * @returns workspace
     */
    getWorkspace(workspaceId: string) {
        return workspaceAPI
            .get<any>('/' + workspaceId)
            .then((response) => response.data);
    }

    /**
     * Delete workspace by id
     * @param workspaceId
     * @returns
     */
    deleteWorkspace(workspaceId: string) {
        return workspaceAPI
            .delete<any>('/' + workspaceId)
            .then((response) => response.data);
    }

    /**
     * Create aurora workspace
     * @param payload
     * @returns
     */
    createAuroraWorkspace(payload: any) {
        return workspaceAPI
            .post<any>('/create/aura_ws', payload)
            .then((response) => response.data);
    }

    /**
     * Create opensearch workspace
     * @param payload
     * @returns
     */
    createOpenSearchWorkspace(payload: any) {
        return workspaceAPI
            .post<any>('/create/opensearch_ws', payload)
            .then((response) => response.data);
    }
    createAOSSWorkspace(payload: any){
        return workspaceAPI.post<any>('/create/aoss_ws', payload).then(response => response.data)
      } 
}

  


export default new WorkspaceService()