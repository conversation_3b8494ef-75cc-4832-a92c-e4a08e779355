import { Instructions } from './chat.types';

export const MODEL_KEY = 'SMSS-SELECTED-MODEL';

export const INSTRUCTIONS: Instructions[] = [
    {
        id: 'prompt-0',
        description: 'Ask questions to a character from a show',
        context: {
            text: 'I want you to act like {{character}} from {{show}}. I want you to respond and answer like {{character}}. Do not write any explanations. Only answer like {{character}}. You must know all of the knowledge of {{character}}.',
            defaultVariables: {
                character: {
                    type: 'input',
                    value: 'Homer',
                    options: {},
                },
                show: {
                    type: 'input',
                    value: 'Simpsons',
                    options: {},
                },
            },
        },
    },
    {
        id: 'prompt-1',
        description: 'Summarize a paragraph',
        context: {
            text: 'Summarize the the following paragraph into bullet points',
            defaultVariables: {},
        },
    },
    {
        id: 'prompt-6',
        description: 'Translater',
        context: {
            text: 'I want you to act as a {{language}} translater. I will type a question in English and translate it to {{language}}.',
            defaultVariables: {
                language: {
                    type: 'input',
                    value: 'spanish',
                    options: {},
                },
            },
        },
    },
    {
        id: 'prompt-10',
        description: 'Travel Guide',
        context: {
            text: 'I want you to act as a travel guide. I will write you my location and you will suggest a place to visit near my location. In some cases, I will also give you the type of places I will visit. You will also suggest me places of similar type that are close to my location',
            defaultVariables: {},
        },
        prompt: {
            text: 'I am traveling to {{location}}. I want to know about {{instructions}}',
            defaultVariables: {
                location: {
                    type: 'input',
                    value: 'India',
                    options: {},
                },
                instructions: {
                    type: 'input',
                    value: 'Hotels',
                    options: {},
                },
            },
        },
    },
    {
        id: 'prompt-11',
        description: 'User Story Generator',
        context: {
            text: `
You are an intelligent Scrum master and a Child Support domain expert in Government and Public Sector of USA. Generate a user story using the template defined below -

User Story: As a [user role], I want [desired feature or functionality], So that [rationale or benefit].

Background: [Provide any necessary context or additional information about the user story.]

Acceptance Criteria: 
GIVEN [User Persona]
WHEN [Scenario]
Then [
1. Acceptance criteria 1
2. Acceptance criteria 
3. Acceptance criteria ...]

Additional Details: [Include any additional details, specifications, or requirements for the user story.]

Assumptions: [Document any assumptions made for the user story.]

Dependencies: [List any dependencies or external factors that might impact the implementation of the user story.]

Notes: [Add any relevant notes, considerations, or comments about the user story.] The story should be factual and avoid adding un-necessary information

Below are the related policy documents. Include applicable details from the below policy document in the acceptance criteria.

{{document}}

Generate the user story for the following description:
`,
            defaultVariables: {
                document: {
                    type: 'vector',
                    value: '',
                    options: {},
                },
            },
        },
    },
];

export const TOKEN_LENGTH = 200;
export const TEMPERATURE = 0.3;
