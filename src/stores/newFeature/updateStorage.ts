// 本地存储键名
const STORAGE_KEY = 'feature_updates_read_status';

// 获取已读状态
export const getReadStatus = (): Record<string, boolean> => {
  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    return stored ? JSON.parse(stored) : {};
  } catch (error) {
    console.warn('Failed to load read status from localStorage:', error);
    return {};
  }
};

// 保存已读状态
export const saveReadStatus = (readStatus: Record<string, boolean>): void => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(readStatus));
  } catch (error) {
    console.warn('Failed to save read status to localStorage:', error);
  }
};

// 标记单个更新为已读
export const markAsRead = (updateId: string): void => {
  const currentStatus = getReadStatus();
  currentStatus[updateId] = true;
  saveReadStatus(currentStatus);
};

// 标记所有更新为已读
export const markAllAsRead = (updateIds: string[]): void => {
  const currentStatus = getReadStatus();
  updateIds.forEach(id => {
    currentStatus[id] = true;
  });
  saveReadStatus(currentStatus);
};

// 检查更新是否已读
export const isUpdateRead = (updateId: string): boolean => {
  const readStatus = getReadStatus();
  return readStatus[updateId] || false;
};

// 清除所有已读状态（用于测试或重置）
export const clearAllReadStatus = (): void => {
  try {
    localStorage.removeItem(STORAGE_KEY);
  } catch (error) {
    console.warn('Failed to clear read status from localStorage:', error);
  }
}; 