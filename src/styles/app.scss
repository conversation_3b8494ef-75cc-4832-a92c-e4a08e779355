* {
  box-sizing: border-box;
  font-family: "Roboto", sans-serif;
}

:root {
  --app-color-scheme: light;
  color-scheme: var(--app-color-scheme);
}

html,
body,
#root,
div[data-amplify-authenticator],
div[data-amplify-theme] {
  height: 100%;
}

body {
  background-color: #ffffff;
  overflow-y: scroll;
}

body.awsui-dark-mode {
  background-color: #0e1b2a;
}

.matrix-table {
  border: 1px solid #d6d6d6;
  border-radius: 2px;
  border-collapse: collapse;
  font-size: 1.1rem;

  th {
    border: 1px solid #d6d6d6;
  }

  td {
    border: 1px solid #d6d6d6;
    padding: 10px;
  }
}

.awsui-dark-mode {
  .matrix-table {
    border: 1px solid rgb(95, 107, 122);

    th {
      border: 1px solid rgb(95, 107, 122);
    }

    td {
      border: 1px solid rgb(95, 107, 122);
      padding: 12px;
    }
  }
}

.jsonContainer {
  //font-family: "Open Sans", sans-serif;
  font-size: 1em;
  background-color: #0e1b2ac3;
}

.jsonStrings {
  color: rgb(74, 234, 167);

}

.jsonNumbers {
  color: rgb(255, 223, 60);
}

.jsonBool {
  color: rgb(252, 178, 250);
  font-weight: 600;
}

.jsonNull {
  color: rgb(74, 205, 234);
  font-weight: 600;
}

[class^="awsui_top-navigation"] {
  background-color: rgb(34, 44, 103) !important;
}

.awsui_title_k5dlb_1itj4_197 {
  border-left: 1px solid white;
  padding: 10px;
}

.awsui_external-icon_93a1u_yw009_170:not(#\9){
  display:none;
}

.awsui_button_vjswe_1u1vg_101.awsui_variant-modal-dismiss_vjswe_1u1vg_165:not(#\9){
  display: none;
}

/* 隐藏 Modal 关闭按钮 - 更通用的规则 */
[class*="modal"] [class*="dismiss"]:not(#\9),
[class*="modal"] [class*="close"]:not(#\9),
[data-testid="modal-dismiss"]:not(#\9),
[aria-label="Close"]:not(#\9) {
  display: none !important;
}
.awsui_container_1cbgc_yqmfc_125.awsui_display-inline-block_1cbgc_yqmfc_201:not(#\9){
  word-break: normal;
}

#link-self\:r4\: span,
#link-self\:ra\: span,
#link-self\:rg\: span,
#link-self\:rm\: span,
#link-self\:ro\: span {
  color: white;
}

.awsui_text_m5h9f_1kye8_165 {
  color: white !important;
}

.awsui_utility-wrapper_k5dlb_1itj4_238:not(#\9)::after {
  background: none !important;
}

#link-self\:rg\: span span {
  color: white !important;
}

.awsui_icon_m5h9f_1kye8_169 {
  color: white !important;
}

.awsui_has-caret_18eso_1drma_137:not(#\9){
border: 1.5px solid #007cba;
border-radius: 5px;
}

.awsui_list-item_l0dv0_11mln_167{
  padding-bottom: 10px !important;
}

.awsui_link_l0dv0_11mln_214 {
  color: #000000 !important;
  font-weight: 400 !important;
  font-family: 'Roboto' !important;
  font-size: 15px !important;
  line-height: 18px !important;
  gap: 5px
}

.awsui_hide-navigation_hyvsj_p48sj_848 {
  display: none !important;
}

.awsui_tabs-tab_14rmt_xb8w1_227:not(#\9):not(:last-child) > button > .awsui_tabs-tab-label_14rmt_xb8w1_238{
  border-right: none;
  padding: 5px;
}
.awsui_pagination-button-left_14rmt_xb8w1_212:not(#\9){
  border-right: none;
}
.awsui_pagination-button-right_14rmt_xb8w1_219:not(#\9){
  border-left: none;
}
.awsui_header-text_gwq0h_1g8ek_331{
color: #222c67;
}
.awsui_button_vjswe_1u1vg_101.awsui_variant-normal_vjswe_1u1vg_125.awsui_disabled_vjswe_1u1vg_201:not(#\9){
  border:none;
}

.awsui_tabs-tab-link_14rmt_xb8w1_253:not(#\9):not(.awsui_tabs-tab-disabled_14rmt_xb8w1_315):after{
  background: none;
}
.awsui_tabs-header-with-divider_14rmt_xb8w1_354:not(#\9){
  border-bottom: none;
}
.awsui_navigation-container_hyvsj_p48sj_711:not(#\9){
  --awsui-navigation-width-y98gph: 260px;
}
.awsui_tabs-tab_14rmt_xb8w1_227:not(#\9):last-child > button > .awsui_tabs-tab-label_14rmt_xb8w1_238{
  //visibility: hidden;
  padding: 5px;
}
//"Send button" colors (disabled)
.awsui_button_vjswe_1u1vg_101.awsui_variant-primary_vjswe_1u1vg_209.awsui_disabled_vjswe_1u1vg_201:not(#\9) {
  color: white;
  background-color: #827f7c;
}
.awsui_button_vjswe_1u1vg_101.awsui_variant-primary_vjswe_1u1vg_209:not(#\9){
  background-color: #007cba;
}

.awsui_label_14mhv_62fef_182 {
  color: #222c67 !important;
}

.awsui_text-content_6absk_bvan2_94 {
  background-color: #f2f8fc;
  border-radius: 10px;
  padding: 10px;
  max-width: fit-content;
  inline-size: max-content;

strong{
  color: #222c67;
}
  .awsui-top-navigation .awsui-title {
    font-size: 18px;
    font-weight: 600;
    line-height: 'normal';
    margin-left: 3;
    color: white;
  }

  .awsui_icon_h11ix_s5sg5_98.awsui_variant-normal_h11ix_s5sg5_219:not(#\9) {
    order: -1;
    margin-right: 2px;
    margin-left: 0px;
  }

  .awsui_button_vjswe_1u1vg_101.awsui_variant-primary_vjswe_1u1vg_209:not(#\9) {
    color: black;
    width: 220px;
    height: 50px;
    border: none !important;
    border-color: none !important;
  }
}