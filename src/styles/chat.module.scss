@use "@cloudscape-design/design-tokens/index" as awsui;

.markdownContent br {
  line-height: 10px !important;
  content: '';
  display: block !important;
  height: 0px;
}

.chat_container {
  margin-bottom: -40px;
  min-height: calc(100vh - 96px);
  display: flex;
  flex-direction: column;
}

@media (min-width: 466px) {
  .chat_container {
    min-height: calc(100vh - 104px);
  }
}

@media (min-width: 689px) {
  .chat_container {
    min-height: calc(100vh - 60px);
  }
}

@media (min-width: 913px) {
  .chat_container {
    min-height: calc(100vh - 68px);
  }
}

.welcome_text {
  color: awsui.$color-text-body-default;
  font-size: 3rem;
  font-weight: bolder;
  opacity: 0.4;
}

.input_container {
  position: sticky;
  bottom: 0;
  padding-bottom: awsui.$space-scaled-l;
  padding-top: awsui.$space-scaled-xxl;
  background: linear-gradient(
                  to bottom,
                  transparent 0%,
                  awsui.$color-background-container-content 20%
  );
  margin-left: -8px;
  margin-right: -8px;
  display: flex;
  container-type: inline-size;
  z-index: 10;
}

.input_textarea_container {
  display: grid;
  grid-template-columns: auto 1fr auto;
  align-items: center;
  gap: 4px;
}

.prompts_input_container {
  display: grid;
  align-items: center;
}

.prompts_input_container.no_suggestion {
  grid-template-columns: auto 1fr;
}

.prompts_input_container.suggestion {
  grid-template-columns: 1fr;
}


.input_textarea {
  resize: none;
  border: none;
  padding: 12px 12px 12px 4px;
  background-color: transparent;
  outline: none;
  width: 100%;
  height: 100%;
  font-size: 1rem;
}

.input_controls {
  display: grid;
  grid-template-rows: 1fr;
  grid-template-columns: minmax(300px, 800px) minmax(180px, auto);
  gap: awsui.$space-scaled-xs;
  align-items: end;


  .input_controls_selects_3 {
    width: 100%;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    align-items: center;
    gap: awsui.$space-scaled-xs;
  }

  .input_controls_selects_23 {
    width: 100%;
    display: grid;
    grid-template-columns: 1fr 1fr 0.6fr;
    align-items: center;
    gap: awsui.$space-scaled-xs;
  }

  .input_controls_selects_2 {
    width: 100%;
    display: grid;
    grid-template-columns: 1fr 1fr;
    align-items: center;
    gap: awsui.$space-scaled-xs;
  }

  .input_controls_selects_1 {
    width: 100%;
    display: grid;
    grid-template-columns: 1fr;
    align-items: center;
    gap: awsui.$space-scaled-xs;
  }

  .input_controls_right {
    justify-self: end;
  }
}

.btn_chabot_message_copy {
  float: right;
}

.btn_chabot_metadata_copy {
  display: flex;
  float: right;
  margin-top: .4em;
}

.documentContentArea {
  flex-grow: 1;
  margin-right: 10px;
}

.documentContentRow {
  display: flex;
  background-color: white;
  padding: 20px 24px 20px 24px;
  border-radius: 10px;
  align-items: flex-start;
  justify-content: space-between;
}

.documentTab {
  display: flex;
  align-items: center;
  gap: 2px;
}

.documentName {
  font-size: 11px;
  display: flex;
  border-radius: 10px;
  border: 1px solid #007cba;
  max-width: 20ch;
  padding: 8px 5px 8px 5px;
  color: #007cba;
  background-color: white;
  cursor: pointer;
}

.documentNameActive {
  font-size: 11px;
  display: flex;
  border-radius: 10px;
  border: none;
  max-width: 20ch;
  padding: 8px 5px 8px 5px;
  color: white;
  background-color: #007cba;
  cursor: pointer;
}

.documentNewTabCopyContainer {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.extraButtonContainer {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: center;
  margin-left: auto;
  height: 100%;
}

.img_chabot_message {
  max-width: 30%;
  border-radius: 8px;
}

@media (max-width: 689px) {
  .input_controls {
    display: grid;
    grid-template-rows: 1fr 1fr;
    grid-template-columns: 1fr;
    gap: awsui.$space-scaled-xs;
  }
}

@container (max-width: 689px) {
  .input_controls {
    display: grid;
    grid-template-rows: 1fr 1fr;
    grid-template-columns: 1fr;
    gap: awsui.$space-scaled-xs;
  }
}

.codeMarkdown {
  background-color: awsui.$color-charts-line-grid;
  text-overflow: ellipsis;
  overflow: scroll;
  border-radius: 5px;
  padding: 5px;
}

.markdownTable {
  width: 100%;
  border-collapse: collapse;
  margin-top: 1em;
  border: 1px solid awsui.$color-border-divider-default;
  border-radius: awsui.$border-radius-container;
}

.markdownTable th,
.markdownTable td {
  border: 1px solid awsui.$color-border-divider-default;
  padding: 8px;
  text-align: left;
}

.markdownTable th {
  background-color: awsui.$color-background-container-header;
}

.markdownTable tr:nth-child(even) {
  background-color: awsui.$color-background-container-content;
}

.thumbsContainer {
  display: flex;
  align-items: center;
  margin-top: 8px;
}

.thumbsIcon {
  cursor: pointer;
  margin-right: 10px;
  opacity: 0.5;
}

/* Styles for thumbs up icon. Should be compatible with dark theme */
.thumbsUp {
  color: #539fe5;
}

/* Styles for thumbs down icon. Should be compatible with dark theme */
.thumbsDown {
  color: #539fe5;
}

/* Style for clicked state */
.clicked {
  opacity: 0.5;
  pointer-events: none; /* Disable pointer events for a clicked icon */
}

/* Styles for selected icon */
.thumbsIcon.selected {
  opacity: 1 !important;
  pointer-events: none; /* Disable pointer events for the selected icon */
}

.promptMessage {
  color: #ff0000;
  font-size: 10px;
  margin-left: 14px;
}

.promptContainer {
  margin-top: 14px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.feedbackDropdownContainer {
  padding-left: 1.3rem;
}

.giveFeedbackThumbsContainer {
  display: flex;
  justify-content: space-between;
}

.provideFeedbackMessage {
  color: #0071bc;
  margin-right: 10px;
  font-weight: 400;
  line-height: 18px;
  font-size: 14px;
}

.errorMessageMargin {
  margin-left: 3px;
}

.multiChat3 {
  display: grid;
  grid-template-columns: 190px 1fr min-content;
  gap: 0px;
}

.multiChat4 {
  display: grid;
  grid-template-columns: 220px 1fr min-content;
  gap: 0px;
}

hr {
  border: 1px solid #e9ebed;
}

.document_library_container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  flex-wrap: wrap;
}

.chat_input_container {
  border: 2px solid #007cba;
  //box-shadow: 0 0 8px rgba(0, 124, 186, 0.6);
  border-radius: 18px !important;
  position: sticky;
  bottom: 0;
  background-color: var(--awsui-color-background-container-content);
  z-index: 100;
  transition: box-shadow 0.4s ease;
  margin-bottom: 16px;
}

/* 当输入框获得焦点时添加阴影效果 */
.chat_input_container:focus-within {
  box-shadow: 0 0 8px rgba(0, 124, 186, 0.6);
}

//Generate a Data Governance Document center
.chat_header {
  display: flex;
  // justify-content: space-between;
  justify-content: center;
  align-items: flex-end;
}

.chat_welcome_message {
  background-color: #e9eaf0;
  border-radius: 10px;
  padding: 10px;
  inline-size: max-content;
  font-weight: 500;
  margin: 0;
}

.prompt_suggestions_btn {
  font-size: 14px;
  font-weight: 600;
  background-color: #f2f8fc;
  border: none;
  border-radius: 10px;
  margin: 4px;
  cursor: pointer;
}

.clear_input_btn {
  background-color: white;
  color: #007cba;
  font-size: 14px;
  padding: 4px 20px 4px 20px;
  border-radius: 1.5rem;
  border: 2px solid #007cba;
  font-weight: 600;
  margin-right: 5px;
  cursor: pointer;
}

.clear_input_btn:hover {
  color: #222c67;
  border: 2px solid #222c67;
}

.send_input_btn {
  color: white;
  background-color: #007cba;
  font-size: 14px;
  padding: 4px 20px 4px 20px;
  border-radius: 1.5rem;
  border: 2px solid #007cba;
  font-weight: 600;
  cursor: pointer;
}

.send_input_btn:hover {
  background-color: #222c67;
  border: 2px solid #222c67;
}

.clear_input_btn:disabled, .send_input_btn:disabled {
  background-color: #e5e5e5;
  color: #898989;
  font-size: 14px;
  padding: 4px 20px 4px 20px;
  border-radius: 1.5rem;
  border: 2px solid #e5e5e5;
  font-weight: 600;
  cursor: default;
}

.item_container {
  width: 220px;
  border-radius: 20px;
  padding: 10px 24px 10px 14px;
  border-right: 4px;
}

.selected_item {
  background-color: #CCE5F1;
}

.chat_history_title {
  font-weight: 400;
  font-size: 15px;
  line-height: 18px;
  margin: 0px;
  color: black;
  overflow: hidden;
  width: 180px;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.chat_history_date {
  color: #827F7C;
  font-size: 11px;
  margin: 0px;
  line-height: 18px;
}

.loadingDots {
  display: inline-flex;
  margin-left: 5px;
}

.dot {
  background-color: #0071BC;
  border-radius: 50%;
  width: 5px;
  height: 5px;
  margin: 0 3px;
  display: inline-block;
  animation: dotPulse 1.5s infinite ease-in-out;
}

.dot:nth-child(2) {
  animation-delay: 0.2s;
}

.dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes dotPulse {
  0%, 60%, 100% {
    transform: scale(1);
    opacity: 0.5;
  }
  30% {
    transform: scale(1.3);
    opacity: 1;
  }
}

// tokens limits warning
.warningContainer {
  display: inline-flex;
  align-items: center;
  margin-top: 22px;
  border: 1px solid #0071BC;
  border-radius: 4px;
  background-color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  padding: 0;
}

.warningDetails {
  padding: 0;
}

.warningDetails[open] {
  width: 300px; /* Fixed width when expanded */
}

.warningSummary {
  cursor: pointer;
  font-weight: normal;
  color: #0071BC;
  display: flex;
  align-items: center;
  padding: 8px 12px;
  font-size: 14px;
  outline: none;
  white-space: nowrap;
}

//.warningIcon {
//  margin-right: 6px;
//  font-size: 16px;
//  color: #0071BC; /* Match the blue color from the thumbs up/down icons */
//}
.warningContent {
  margin-top: 8px;
  color: #333;
  font-size: 14px;
  max-height: 180px; /* Limit the height */
  overflow-y: auto; /* Add scrolling */
  padding: 0 12px 8px 12px;
}

.warningList {
  margin: 8px 0;
  padding-left: 20px;
}

.warningList li {
  margin-bottom: 4px;
}

//model info popover
.model_notification {
  position: fixed;
  bottom: 20px;
  left: 0;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  pointer-events: none;
  animation: fadeIn 0.3s ease-in-out forwards;
}

.model_notification_content {
  background-color: #f2f3f3;
  border: 1px solid #e1e4e6;
  border-radius: 4px;
  padding: 10px 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  pointer-events: auto;
  animation: slideUp 0.3s ease-out forwards;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(20px); }
  to { transform: translateY(0); }
}