/* 覆盖层 */
.new-feature-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: transparent;
  z-index: 9999;
  pointer-events: none;
}

/* 主面板 */
.new-feature-panel {
  position: fixed;
  top: 61px; /* 调整到header下方 */
  right: 33px; /* 调整到bell icon下方的位置 */
  border: 3px solid #e1e5e9;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
  width: 520px;
  max-width: 520px;
  max-height: 70vh;
  display: flex;
  flex-direction: column;
  animation: slideDown 0.3s ease-out;
  pointer-events: auto;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 添加箭头指示器 */
.new-feature-panel::before {
  content: '';
  position: absolute;
  top: -9.5px;
  right: 50px;
  width: 0;
  height: 0;
  border-left: 11px solid transparent;
  border-right: 11px solid transparent;
  border-bottom: 11px solid rgb(250, 249, 249);
  filter: drop-shadow(0 -3px 6px rgba(0, 0, 0, 0.15));
}

/* 添加箭头的边框 */
.new-feature-panel::after {
  content: '';
  position: absolute;
  top: -14px;
  right: 47px;
  width: 0;
  height: 0;
  border-left: 14px solid transparent;
  border-right: 14px solid transparent;
  border-bottom: 14px solid #e1e5e9;
  z-index: -1;
}

/* 头部 */
.new-feature-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 24px 12px;
  border-bottom: 1px solid #e5e7eb;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 10px;
}

.new-feature-header h3 {
  margin: 0;
  font-size: 15px;
  font-weight: 600;
  color: #1f2937;
}

/* 未读badge */
.unread-badge {
  background-color: #ef4444;
  color: white;
  padding: 3px 6px;
  border-radius: 10px;
  font-size: 12px;
  font-weight: 500;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.mark-all-read-btn {
  background-color: #3b82f6;
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 5px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.mark-all-read-btn:hover {
  background-color: #2563eb;
}

.close-btn {
  background: none;
  border: none;
  font-size: 16px;
  color: #6b7280;
  cursor: pointer;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.close-btn:hover {
  background-color: #f3f4f6;
  color: #374151;
}

/* 内容区域 */
.new-feature-content {
  flex: 1;
  overflow-y: auto;
  padding: 0 24px;
  max-height: 60vh;
}

.no-updates {
  text-align: center;
  padding: 30px 15px;
  color: #6b7280;
  font-size: 12px;
}

/* 更新项 */
.update-item {
  border-bottom: 1px solid #f3f4f6;
  padding: 15px 0;
  position: relative;
  transition: all 0.2s;
}

.update-item:last-child {
  border-bottom: none;
}

.update-item.unread {
  background: linear-gradient(90deg, rgba(59, 130, 246, 0.05) 0%, transparent 100%);
  /* 圆点跟 feature 内容的距离 */
  padding-left: 29px;
  margin-left: -24px;
  padding-right: 24px;
  margin-right: -24px;
  position: relative;
}

.update-item.unread::before {
  content: '';
  position: absolute;
  /* 圆点和左边界的距离 */
  left: 13px;
  top: 18px;
  width: 8px;
  height: 8px;
  background-color: #3b82f6;
  border-radius: 50%;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
  animation: breathe 2s ease-in-out infinite;
}

@keyframes breathe {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
  }
  50% {
    transform: scale(1.4);
    opacity: 0.8;
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
  }
}

.update-item.read {
  opacity: 0.8;
}

.update-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.update-meta {
  display: flex;
  align-items: center;
  gap: 10px;
  flex: 1;
}

.update-type {
  background-color: #f3f4f6;
  color: #374151;
  padding: 3px 6px;
  border-radius: 5px;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 3px;
}

.update-date {
  color: #6b7280;
  font-size: 13px;
}

.mark-read-btn {
  background-color: #10b981;
  color: white;
  border: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s;
}

.mark-read-btn:hover {
  background-color: #059669;
  transform: scale(1.1);
}

.update-title {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  line-height: 1.4;
}

.update-description {
  color: #4b5563;
  line-height: 1.5;
  font-size: 14px;
}

.update-description p {
  margin: 0 0 6px 0;
}

.update-description p:last-child {
  margin-bottom: 0;
}

.update-description ul {
  margin: 6px 0;
  padding-left: 16px;
}

.update-description li {
  margin: 3px 0;
}

.update-description strong {
  font-weight: 600;
  color: #1f2937;
}

.unread-indicator {
  position: absolute;
  top: 15px;
  right: 13px; /* 增加与右边界的距离 */
  width: 6px;
  height: 6px;
  background-color: #ef4444;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .new-feature-panel {
    position: fixed;
    top: 56px;
    left: 50%;
    right: auto;
    transform: translateX(-50%);
    width: 90%;
    max-width: 450px;
    max-height: 75vh;
  }

  .new-feature-panel::before {
    left: 50%;
    right: auto;
    transform: translateX(-50%);
  }
}

@media (max-width: 640px) {
  .new-feature-panel {
    width: 95%;
    max-width: 400px;
    max-height: 80vh;
  }
  
  .new-feature-header {
    padding: 12px 20px 10px;
  }
  
  .new-feature-content {
    padding: 0 20px;
  }
  
  .header-actions {
    gap: 6px;
  }
  
  .mark-all-read-btn {
    font-size: 11px;
    padding: 4px 8px;
  }
  
  .update-title {
    font-size: 15px;
  }
  
  .update-description {
    font-size: 13px;
  }
}

/* 滚动条样式 */
.new-feature-content::-webkit-scrollbar {
  width: 5px;
}

.new-feature-content::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.new-feature-content::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.new-feature-content::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
} 