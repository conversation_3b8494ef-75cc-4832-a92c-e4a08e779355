const replace = require('replace-in-file');
const moment = require('moment-timezone');
const fs = require('fs');

// Generate the current timestamp
const timeStamp = moment()
  .tz('America/New_York')
  .format('MM.DD.YYYY hh:mm:ss A z');

const options = {
  files: [
    'src/common/environment.ts',
    'src/common/environment.prod.ts',
    'src/common/environment.dev.ts',
  ],
  from: /timeStamp\s*:\s*'(.*)'/g,
  to: `timeStamp: '${timeStamp}'`,
  allowEmptyPaths: false,
};

try {
  // Ensure the placeholder exists in all files
  options.files.forEach((file) => {
    const content = fs.readFileSync(file, 'utf8');
    if (!content.includes('timeStamp:')) {
      fs.appendFileSync(file, "\ntimeStamp: ''\n", 'utf8');
    }
  });

  // Perform the replacement
  const changedFiles = replace.sync(options);

  if (!changedFiles.length) {
    throw new Error(
      `No matching files were modified. Ensure that the specified files contain "timeStamp: ''".`
    );
  }

  console.log(`Build timestamp successfully set to: ${timeStamp}`);
} catch (error) {
  console.error('An error occurred while updating the timestamp:', error.message);
  throw error;
}
